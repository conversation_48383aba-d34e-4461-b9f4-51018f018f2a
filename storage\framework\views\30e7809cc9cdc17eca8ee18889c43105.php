<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('تفاصيل المصروف')); ?>

            </h2>
            <div>
                <a href="<?php echo e(route('expenses.edit', $expense)); ?>" class="btn btn-primary me-2">
                    <i class="fas fa-edit"></i> <?php echo e(__('تعديل')); ?>

                </a>
                <a href="<?php echo e(route('expenses.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> <?php echo e(__('عودة')); ?>

                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="container-fluid">
            <div class="row">
                <!-- Expense Details -->
                <div class="col-md-8">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-4">
                        <div class="p-6 bg-white border-b border-gray-200">
                            <h3 class="mb-4"><?php echo e(__('معلومات المصروف')); ?></h3>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted"><?php echo e(__('المرجع')); ?></label>
                                        <p class="mb-0"><?php echo e($expense->reference_number); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted"><?php echo e(__('التاريخ')); ?></label>
                                        <p class="mb-0"><?php echo e($expense->expense_date->format('Y-m-d')); ?></p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted"><?php echo e(__('الفرع')); ?></label>
                                        <p class="mb-0"><?php echo e($expense->branch->name); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted"><?php echo e(__('التصنيف')); ?></label>
                                        <p class="mb-0">
                                            <span class="badge" style="background-color: <?php echo e($expense->category->color ?: '#6c757d'); ?>">
                                                <?php echo e($expense->category->name); ?>

                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted"><?php echo e(__('المبلغ')); ?></label>
                                        <p class="mb-0"><?php echo e(number_format($expense->amount, 2)); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted"><?php echo e(__('طريقة الدفع')); ?></label>
                                        <p class="mb-0">
                                            <?php if($expense->payment_method == 'cash'): ?>
                                                <span class="badge bg-success"><?php echo e(__('نقدي')); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-info"><?php echo e(__('بنكي')); ?></span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label text-muted"><?php echo e(__('الوصف')); ?></label>
                                        <p class="mb-0"><?php echo e($expense->description ?: '-'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="col-md-4">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-4">
                        <div class="p-6 bg-white border-b border-gray-200">
                            <h3 class="mb-4"><?php echo e(__('معلومات إضافية')); ?></h3>

                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo e(__('تم الإنشاء بواسطة')); ?></label>
                                <p class="mb-0"><?php echo e($expense->user->name); ?></p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo e(__('تاريخ الإنشاء')); ?></label>
                                <p class="mb-0"><?php echo e($expense->created_at->format('Y-m-d H:i')); ?></p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo e(__('آخر تحديث')); ?></label>
                                <p class="mb-0"><?php echo e($expense->updated_at->format('Y-m-d H:i')); ?></p>
                            </div>

                            <?php if($expense->cashTransaction): ?>
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo e(__('معاملة النقدية')); ?></label>
                                    <p class="mb-0">
                                        <span class="badge bg-<?php echo e($expense->cashTransaction->amount < 0 ? 'danger' : 'success'); ?>">
                                            <?php echo e(number_format(abs($expense->cashTransaction->amount), 2)); ?>

                                        </span>
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Delete Form -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6 bg-white border-b border-gray-200">
                            <form action="<?php echo e(route('expenses.destroy', $expense)); ?>" method="POST" onsubmit="return confirm('<?php echo e(__('هل أنت متأكد من حذف هذا المصروف؟')); ?>')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-trash"></i> <?php echo e(__('حذف المصروف')); ?>

                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .badge {
            padding: 0.5em 0.75em;
        }
        .form-label {
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }
        .btn-group {
            flex-direction: row-reverse;
        }
        .btn-group .btn {
            margin-right: 0;
            margin-left: 0.25rem;
        }
    </style>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\expenses\show.blade.php ENDPATH**/ ?>