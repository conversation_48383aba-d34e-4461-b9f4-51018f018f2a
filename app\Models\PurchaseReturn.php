<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class PurchaseReturn extends Model
{
    use HasFactory;

    protected $fillable = [
        'return_number',
        'purchase_id',
        'supplier_id',
        'branch_id',
        'store_id',
        'user_id',
        'total_amount',
        'refund_amount',
        'status',
        'return_type',
        'reason',
        'notes',
        'return_date',
        'approved_at',
        'approved_by'
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'refund_amount' => 'decimal:2',
        'return_date' => 'date',
        'approved_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->return_number)) {
                $model->return_number = 'RET-' . date('Y') . '-' . str_pad(
                    static::whereYear('created_at', date('Y'))->count() + 1,
                    6,
                    '0',
                    STR_PAD_LEFT
                );
            }
        });
    }

    // Relationships
    public function purchase()
    {
        return $this->belongsTo(Purchase::class);
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function items()
    {
        return $this->hasMany(PurchaseReturnItem::class);
    }

    public function transactions()
    {
        return $this->morphMany(AccountTransaction::class, 'transactionable');
    }

    // Business Logic Methods
    public function canBeEdited(): bool
    {
        return in_array($this->status, ['pending']);
    }

    public function canBeApproved(): bool
    {
        return $this->status === 'pending';
    }

    public function canBeCompleted(): bool
    {
        return $this->status === 'approved';
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'approved']);
    }

    public function approve(User $approver): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => $approver->id
        ]);

        return true;
    }

    public function complete(): bool
    {
        if (!$this->canBeCompleted()) {
            return false;
        }

        // Process inventory adjustments
        foreach ($this->items as $item) {
            $this->adjustInventoryForItem($item);
        }

        // Process supplier account refund if applicable
        if ($this->refund_amount > 0) {
            $this->processSupplierRefund();
        }

        $this->update(['status' => 'completed']);

        return true;
    }

    public function cancel(string $reason = null): bool
    {
        if (!$this->canBeCancelled()) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => $this->notes . ($reason ? "\nCancellation reason: " . $reason : '')
        ]);

        return true;
    }

    protected function adjustInventoryForItem(PurchaseReturnItem $item)
    {
        if ($item->inventory_adjusted) {
            return; // Already adjusted
        }

        // Determine the location (branch or store)
        $location = $this->store_id ? $this->store : $this->branch;

        if (!$location) {
            return;
        }

        // Get the appropriate inventory model
        $inventoryClass = $this->store_id ? StoreInventory::class : BranchInventory::class;
        $locationField = $this->store_id ? 'store_id' : 'branch_id';

        // Find the inventory record
        $inventory = $inventoryClass::where($locationField, $location->id)
            ->where('product_id', $item->product_id)
            ->first();

        if ($inventory) {
            // Reduce inventory quantity (return removes from our stock)
            $inventory->decrement('quantity', $item->quantity_returned);

            // Mark as adjusted
            $item->update(['inventory_adjusted' => true]);
        }
    }

    protected function processSupplierRefund()
    {
        $supplierAccount = $this->supplier->account;

        if (!$supplierAccount) {
            return;
        }

        // Create refund transaction (debit to supplier account - reduces their balance)
        $transaction = AccountTransaction::create([
            'account_id' => $supplierAccount->id,
            'reference_number' => 'REF-' . $this->return_number,
            'type' => 'debit',
            'amount' => $this->refund_amount,
            'balance_before' => $supplierAccount->current_balance,
            'balance_after' => $supplierAccount->current_balance - $this->refund_amount,
            'description' => 'استرداد مقابل مرتجع شراء رقم ' . $this->return_number,
            'transactionable_type' => static::class,
            'transactionable_id' => $this->id,
            'user_id' => auth()->id()
        ]);

        // Update supplier account balance
        $supplierAccount->update([
            'current_balance' => $supplierAccount->current_balance - $this->refund_amount
        ]);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }
}
