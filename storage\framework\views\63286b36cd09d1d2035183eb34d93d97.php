<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-receipt text-primary me-2"></i>
                    تفاصيل المشتريات #<?php echo e($purchase->invoice_number); ?>

                </h1>
                <p class="text-muted mb-0">عرض تفاصيل عملية الشراء والمنتجات المرتبطة</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(user_route('purchases.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة إلى المشتريات
                </a>
                <?php if($purchase->status === 'pending'): ?>
                    <a href="<?php echo e(user_route('purchases.edit', $purchase)); ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </a>
                <?php endif; ?>
                <?php if($purchase->status === 'completed' && $purchase->actual_remaining_amount > 0): ?>
                    <a href="<?php echo e(user_route('supplier-payments.create', $purchase)); ?>" class="btn btn-warning">
                        <i class="fas fa-money-bill-wave me-2"></i>دفع مبلغ
                    </a>
                    <a href="<?php echo e(user_route('supplier-payments.show', $purchase)); ?>" class="btn btn-outline-warning">
                        <i class="fas fa-history me-2"></i>تاريخ المدفوعات
                    </a>
                <?php endif; ?>
                <?php if($purchase->status === 'completed'): ?>
                    <a href="<?php echo e(route('admin.purchase-returns.create', ['purchase_id' => $purchase->id])); ?>"
                        class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-2"></i>إرجاع منتجات
                    </a>
                <?php endif; ?>
                <a href="<?php echo e(user_route('purchases.print', $purchase)); ?>" class="btn btn-outline-info" target="_blank">
                    <i class="fas fa-print me-2"></i>طباعة
                </a>
            </div>
        </div>

        <!-- Distribution Status Alert -->
        <?php if(!$purchase->is_distributed && $purchase->status === 'pending_distribution'): ?>
            <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle me-3 fs-4"></i>
                    <div class="flex-grow-1">
                        <h5 class="alert-heading mb-1">يتطلب توزيع المنتجات</h5>
                        <p class="mb-2">هذه المشتريات تحتاج إلى توزيع المنتجات على الفروع والمخازن لإكمال العملية.</p>
                        <a href="<?php echo e(route('admin.purchases.distribute', $purchase)); ?>" class="btn btn-warning btn-sm">
                            <i class="fas fa-boxes me-2"></i>توزيع المنتجات الآن
                        </a>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Status Alert -->
        <?php if($purchase->status === 'pending'): ?>
            <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> هذه العملية قيد الانتظار ويمكن تعديلها أو إكمالها.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php elseif($purchase->status === 'completed'): ?>
            <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <strong>مكتمل:</strong> تم إكمال هذه العملية بنجاح.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php if($purchase->actual_remaining_amount > 0): ?>
                <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه دفع:</strong> يوجد مبلغ متبقي
                    <?php echo e(format_currency($purchase->actual_remaining_amount)); ?> لهذه العملية.
                    <a href="<?php echo e(user_route('supplier-payments.create', $purchase)); ?>"
                        class="btn btn-warning btn-sm ms-2">
                        <i class="fas fa-money-bill-wave me-1"></i>دفع الآن
                    </a>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
        <?php elseif($purchase->status === 'cancelled'): ?>
            <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-times-circle me-2"></i>
                <strong>ملغي:</strong> تم إلغاء هذه العملية.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    عدد المنتجات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($purchase->items->count()); ?>

                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-boxes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    إجمالي الكمية
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo e($purchase->items->sum('quantity')); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-cubes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    إجمالي القيمة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo e(format_currency($purchase->total_amount ?? 0)); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    المبلغ المتبقي
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo e(format_currency($purchase->actual_remaining_amount ?? 0)); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchase Details -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-info-circle me-2"></i>معلومات الشراء
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">رقم الفاتورة:</strong>
                            </div>
                            <div class="col-sm-8">
                                <span class="badge bg-primary px-3 py-2"><?php echo e($purchase->invoice_number); ?></span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">المورد:</strong>
                            </div>
                            <div class="col-sm-8">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-info text-white me-2">
                                        <i class="fas fa-truck"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold"><?php echo e($purchase->supplier->name); ?></div>
                                        <small
                                            class="text-muted"><?php echo e($purchase->supplier->phone ?? 'لا يوجد هاتف'); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">الفرع:</strong>
                            </div>
                            <div class="col-sm-8">
                                <span
                                    class="badge bg-light text-dark border"><?php echo e($purchase->branch ? $purchase->branch->name : 'غير محدد'); ?></span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">التاريخ:</strong>
                            </div>
                            <div class="col-sm-8">
                                <div class="text-dark">
                                    <?php echo e(\Carbon\Carbon::parse($purchase->purchase_date)->format('d/m/Y')); ?></div>
                                <small
                                    class="text-muted"><?php echo e(\Carbon\Carbon::parse($purchase->purchase_date)->format('h:i A')); ?></small>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4">
                                <strong class="text-muted">الحالة:</strong>
                            </div>
                            <div class="col-sm-8">
                                <?php if($purchase->status === 'completed'): ?>
                                    <span class="badge bg-success-soft text-success px-3 py-2">
                                        <i class="fas fa-check-circle me-1"></i>مكتمل
                                    </span>
                                    <?php if($purchase->is_distributed && $purchase->distributed_at): ?>
                                        <div class="small text-muted mt-1">
                                            تم التوزيع: <?php echo e($purchase->distributed_at); ?>

                                        </div>
                                    <?php endif; ?>
                                <?php elseif($purchase->status === 'cancelled'): ?>
                                    <span class="badge bg-danger-soft text-danger px-3 py-2">
                                        <i class="fas fa-times-circle me-1"></i>ملغي
                                    </span>
                                <?php elseif($purchase->status === 'pending_distribution'): ?>
                                    <span class="badge bg-info-soft text-info px-3 py-2">
                                        <i class="fas fa-boxes me-1"></i>في انتظار التوزيع
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-warning-soft text-warning px-3 py-2">
                                        <i class="fas fa-clock me-1"></i>قيد الانتظار
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-calculator me-2"></i>ملخص مالي
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-6">
                                <strong class="text-muted">المجموع الفرعي:</strong>
                            </div>
                            <div class="col-sm-6 text-end">
                                <span
                                    class="fw-bold text-dark"><?php echo e(format_currency($purchase->total_amount ?? 0)); ?></span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-6">
                                <strong class="text-muted">الخصم:</strong>
                            </div>
                            <div class="col-sm-6 text-end">
                                <span class="text-danger">
                                    <?php if($purchase->discount_type === 'percentage'): ?>
                                        <?php echo e($purchase->discount_value); ?>%
                                        (<?php echo e(format_currency($purchase->discount_amount)); ?>)
                                    <?php else: ?>
                                        <?php echo e(format_currency($purchase->discount_amount ?? 0)); ?>

                                    <?php endif; ?>
                                </span>
                            </div>
                        </div>
                        <hr>
                        <div class="row mb-3">
                            <div class="col-sm-6">
                                <strong class="text-primary">المجموع النهائي:</strong>
                            </div>
                            <div class="col-sm-6 text-end">
                                <span
                                    class="fw-bold text-primary h5"><?php echo e(format_currency($purchase->final_amount)); ?></span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-6">
                                <strong class="text-success">المبلغ المدفوع:</strong>
                            </div>
                            <div class="col-sm-6 text-end">
                                <span
                                    class="fw-bold text-success"><?php echo e(format_currency($purchase->paid_amount ?? 0)); ?></span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <strong class="text-warning">المبلغ المتبقي:</strong>
                            </div>
                            <div class="col-sm-6 text-end">
                                <span
                                    class="fw-bold text-warning"><?php echo e(format_currency($purchase->actual_remaining_amount ?? 0)); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-boxes me-2"></i>المنتجات المشتراة
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th class="border-0 fw-bold">المنتج</th>
                                <th class="border-0 fw-bold text-center">الكمية</th>
                                <th class="border-0 fw-bold text-center">سعر التكلفة</th>
                                <th class="border-0 fw-bold text-center">المجموع</th>
                                <?php if($purchase->is_distributed): ?>
                                    <th class="border-0 fw-bold text-center">موزع إلى</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $purchase->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr class="border-bottom">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-secondary text-white me-3">
                                                <i class="fas fa-box"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold text-dark"><?php echo e($item->product->name); ?></div>
                                                <small
                                                    class="text-muted"><?php echo e($item->product->sku ?? 'لا يوجد رمز'); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span
                                            class="badge bg-light text-dark border px-3 py-2"><?php echo e($item->quantity); ?></span>
                                    </td>
                                    <td class="text-center">
                                        <span
                                            class="fw-semibold text-info"><?php echo e(format_currency($item->cost_price ?? 0)); ?></span>
                                    </td>
                                    <td class="text-center">
                                        <span
                                            class="fw-bold text-success"><?php echo e(format_currency($item->total_price ?? 0)); ?></span>
                                    </td>
                                    <?php if($purchase->is_distributed): ?>
                                        <td class="text-center">
                                            <?php if($item->is_distributed && $item->distribution_location_type && $item->distribution_location_id): ?>
                                                <?php if($item->distribution_location_type === 'branch'): ?>
                                                    <?php
                                                        $branch = \App\Models\Branch::find(
                                                            $item->distribution_location_id,
                                                        );
                                                    ?>
                                                    <span class="badge bg-primary-soft text-primary px-3 py-2">
                                                        <i
                                                            class="fas fa-building me-1"></i><?php echo e($branch->name ?? 'فرع غير معروف'); ?>

                                                    </span>
                                                <?php else: ?>
                                                    <?php
                                                        $store = \App\Models\Store::find(
                                                            $item->distribution_location_id,
                                                        );
                                                    ?>
                                                    <span class="badge bg-info-soft text-info px-3 py-2">
                                                        <i
                                                            class="fas fa-warehouse me-1"></i><?php echo e($store->name ?? 'مخزن غير معروف'); ?>

                                                    </span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="badge bg-warning-soft text-warning px-3 py-2">
                                                    <i class="fas fa-clock me-1"></i>لم يتم التوزيع
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="<?php echo e($purchase->is_distributed ? '5' : '4'); ?>"
                                        class="text-center py-5">
                                        <div class="empty-state">
                                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">لا توجد منتجات</h5>
                                            <p class="text-muted">لم يتم إضافة أي منتجات لهذه العملية</p>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="<?php echo e($purchase->is_distributed ? '4' : '3'); ?>"
                                    class="text-end fw-bold text-primary">المجموع الكلي:</th>
                                <th class="text-center">
                                    <span
                                        class="fw-bold text-primary h5"><?php echo e(format_currency($purchase->total_amount ?? 0)); ?></span>
                                </th>
                                <?php if($purchase->is_distributed): ?>
                                    <th></th>
                                <?php endif; ?>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <?php if($purchase->status === 'pending'): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-cogs me-2"></i>إجراءات العملية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-3 justify-content-center">
                        <button type="button" class="btn btn-success btn-lg"
                            onclick="completePurchase(<?php echo e($purchase->id); ?>)">
                            <i class="fas fa-check-circle me-2"></i>إكمال الشراء
                        </button>
                        <button type="button" class="btn btn-danger btn-lg"
                            onclick="cancelPurchase(<?php echo e($purchase->id); ?>)">
                            <i class="fas fa-times-circle me-2"></i>إلغاء الشراء
                        </button>
                    </div>
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            بعد إكمال العملية لن يمكن تعديلها أو إلغاؤها
                        </small>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Purchase Returns -->
        <?php if($purchase->status === 'completed' && $purchase->returns->count() > 0): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-secondary">
                        <i class="fas fa-undo me-2"></i>مرتجعات هذه العملية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>رقم المرتجع</th>
                                    <th>تاريخ المرتجع</th>
                                    <th>نوع المرتجع</th>
                                    <th>المبلغ المرتجع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $purchase->returns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $return): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <a href="<?php echo e(route('admin.purchase-returns.show', $return)); ?>"
                                                class="text-decoration-none">
                                                <?php echo e($return->return_number); ?>

                                            </a>
                                        </td>
                                        <td><?php echo e($return->return_date->format('Y-m-d')); ?></td>
                                        <td>
                                            <?php if($return->return_type == 'full'): ?>
                                                <span class="badge bg-primary">مرتجع كامل</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">مرتجع جزئي</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e(number_format($return->refund_amount, 2)); ?> ج.م</td>
                                        <td>
                                            <?php switch($return->status):
                                                case ('pending'): ?>
                                                    <span class="badge bg-warning">في الانتظار</span>
                                                <?php break; ?>

                                                <?php case ('approved'): ?>
                                                    <span class="badge bg-info">معتمد</span>
                                                <?php break; ?>

                                                <?php case ('completed'): ?>
                                                    <span class="badge bg-success">مكتمل</span>
                                                <?php break; ?>

                                                <?php case ('cancelled'): ?>
                                                    <span class="badge bg-danger">ملغي</span>
                                                <?php break; ?>
                                            <?php endswitch; ?>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(route('admin.purchase-returns.show', $return)); ?>"
                                                class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            function completePurchase(purchaseId) {
                if (confirm('هل أنت متأكد من إكمال هذه العملية؟\nلن يمكن التراجع عن هذا الإجراء.')) {
                    fetch(`<?php echo e(url('')); ?>/admin/purchases/${purchaseId}/complete`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                'Content-Type': 'application/json',
                            },
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('حدث خطأ أثناء إكمال العملية');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('حدث خطأ أثناء إكمال العملية');
                        });
                }
            }

            function cancelPurchase(purchaseId) {
                if (confirm('هل أنت متأكد من إلغاء هذه العملية؟\nلن يمكن التراجع عن هذا الإجراء.')) {
                    fetch(`<?php echo e(url('')); ?>/admin/purchases/${purchaseId}/cancel`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                'Content-Type': 'application/json',
                            },
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('حدث خطأ أثناء إلغاء العملية');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('حدث خطأ أثناء إلغاء العملية');
                        });
                }
            }
        </script>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('styles'); ?>
        <style>
            .avatar-circle {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
            }

            .border-left-primary {
                border-left: 0.25rem solid #4e73df !important;
            }

            .border-left-success {
                border-left: 0.25rem solid #1cc88a !important;
            }

            .border-left-warning {
                border-left: 0.25rem solid #f6c23e !important;
            }

            .border-left-info {
                border-left: 0.25rem solid #36b9cc !important;
            }

            .bg-success-soft {
                background-color: rgba(28, 200, 138, 0.1) !important;
            }

            .bg-danger-soft {
                background-color: rgba(231, 74, 59, 0.1) !important;
            }

            .bg-warning-soft {
                background-color: rgba(246, 194, 62, 0.1) !important;
            }

            .empty-state {
                padding: 3rem 1rem;
            }

            .table-hover tbody tr:hover {
                background-color: rgba(0, 0, 0, 0.02);
            }

            @media print {

                .btn,
                .card-header,
                .alert {
                    display: none !important;
                }

                .card {
                    border: none !important;
                    box-shadow: none !important;
                }
            }
        </style>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\purchases\show.blade.php ENDPATH**/ ?>