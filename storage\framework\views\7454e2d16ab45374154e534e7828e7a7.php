<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">الحسابات</h5>
                        <a href="<?php echo e(route('accounts.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة حساب
                        </a>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo e(route('accounts.index')); ?>" method="GET" class="mb-4">
                            <div class="row">
                                <div class="col-md-4">
                                    <select name="type" class="form-select">
                                        <option value="">جميع الأنواع</option>
                                        <option value="branch" <?php echo e(request('type') == 'branch' ? 'selected' : ''); ?>>فرع</option>
                                        <option value="supplier" <?php echo e(request('type') == 'supplier' ? 'selected' : ''); ?>>مورد</option>
                                        <option value="customer" <?php echo e(request('type') == 'customer' ? 'selected' : ''); ?>>عميل</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <select name="is_active" class="form-select">
                                        <option value="">جميع الحالات</option>
                                        <option value="1" <?php echo e(request('is_active') == '1' ? 'selected' : ''); ?>>نشط</option>
                                        <option value="0" <?php echo e(request('is_active') == '0' ? 'selected' : ''); ?>>غير نشط</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> تصفية
                                    </button>
                                </div>
                            </div>
                        </form>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>الرمز</th>
                                        <th>النوع</th>
                                        <th>الرصيد الحالي</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($account->name); ?></td>
                                            <td><?php echo e($account->code); ?></td>
                                            <td><?php echo e($account->type == 'branch' ? 'فرع' : ($account->type == 'supplier' ? 'مورد' : 'عميل')); ?></td>
                                            <td><?php echo e(number_format($account->current_balance, 2)); ?></td>
                                            <td><?php echo e($account->is_active ? 'نشط' : 'غير نشط'); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo e(route('accounts.show', $account)); ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('accounts.edit', $account)); ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('accounts.destroy', $account)); ?>" method="POST" class="d-inline">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-4">
                            <?php echo e($accounts->links()); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\accounts\index.blade.php ENDPATH**/ ?>