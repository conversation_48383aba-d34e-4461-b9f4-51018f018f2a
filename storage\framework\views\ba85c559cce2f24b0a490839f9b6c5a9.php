<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-shopping-cart text-primary"></i> إدارة المبيعات
            </h2>
            <div class="d-flex gap-2">
                
                <a href="<?php echo e(user_route('sales.create')); ?>" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> عملية بيع جديدة
                </a>
                
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        مبيعات اليوم
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo e(number_format($stats['today_sales'] ?? 0, 2)); ?> ج.م
                                    </div>
                                    <div class="text-xs text-muted">
                                        <?php echo e($stats['today_sales_count'] ?? 0); ?> عملية
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        مبيعات الشهر
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo e(number_format($stats['month_sales'] ?? 0, 2)); ?> ج.م
                                    </div>
                                    <div class="text-xs text-muted">
                                        <?php echo e($stats['month_sales_count'] ?? 0); ?> عملية
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        متوسط قيمة البيع
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo e(number_format($stats['avg_sale_value'] ?? 0, 2)); ?> ج.م
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        عمليات معلقة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo e($stats['pending_sales'] ?? 0); ?>

                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Sales Table Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list"></i> قائمة المبيعات
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Enhanced Filters -->
                    <div class="bg-light p-3 rounded mb-4">
                        <form action="<?php echo e(user_route('sales.index')); ?>" method="GET" id="filterForm">
                            <div class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label small fw-bold">البحث</label>
                                    <div class="input-group">
                                        <input type="text" name="search" class="form-control"
                                            placeholder="رقم الفاتورة أو اسم العميل..." value="<?php echo e(request('search')); ?>">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small fw-bold">العميل</label>
                                    <select name="customer_id" class="form-select" onchange="this.form.submit()">
                                        <option value="">جميع العملاء</option>
                                        <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($customer->id); ?>"
                                                <?php echo e(request('customer_id') == $customer->id ? 'selected' : ''); ?>>
                                                <?php echo e($customer->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small fw-bold">من تاريخ</label>
                                    <input type="date" name="start_date" class="form-control"
                                        value="<?php echo e(request('start_date')); ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small fw-bold">إلى تاريخ</label>
                                    <input type="date" name="end_date" class="form-control"
                                        value="<?php echo e(request('end_date')); ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small fw-bold">الحالة</label>
                                    <select name="status" class="form-select" onchange="this.form.submit()">
                                        <option value="">جميع الحالات</option>
                                        <option value="completed"
                                            <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>
                                            <i class="fas fa-check-circle"></i> مكتمل
                                        </option>
                                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>
                                            <i class="fas fa-clock"></i> قيد الانتظار
                                        </option>
                                        <option value="cancelled"
                                            <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>
                                            <i class="fas fa-times-circle"></i> ملغي
                                        </option>
                                    </select>
                                </div>
                                <?php if(auth()->user()->canAccessAllBranches() && $branches->count() > 0): ?>
                                    <div class="col-md-2">
                                        <label class="form-label small fw-bold">الفرع</label>
                                        <select name="branch_id" class="form-select" onchange="this.form.submit()">
                                            <option value="">جميع الفروع</option>
                                            <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($branch->id); ?>"
                                                    <?php echo e(request('branch_id') == $branch->id ? 'selected' : ''); ?>>
                                                    <?php echo e($branch->name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                <?php endif; ?>
                                <div class="col-md-1">
                                    <label class="form-label small fw-bold">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="button" class="btn btn-outline-secondary"
                                            onclick="clearFilters()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Filter Buttons -->
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary"
                                            onclick="setDateFilter('today')">
                                            اليوم
                                        </button>
                                        <button type="button" class="btn btn-outline-primary"
                                            onclick="setDateFilter('yesterday')">
                                            أمس
                                        </button>
                                        <button type="button" class="btn btn-outline-primary"
                                            onclick="setDateFilter('week')">
                                            هذا الأسبوع
                                        </button>
                                        <button type="button" class="btn btn-outline-primary"
                                            onclick="setDateFilter('month')">
                                            هذا الشهر
                                        </button>
                                        <button type="button" class="btn btn-outline-primary"
                                            onclick="setDateFilter('last_month')">
                                            الشهر الماضي
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Enhanced Table -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle table-bordered" id="salesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 40px;">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th style="min-width: 120px;">
                                        <a href="#" class="text-white text-decoration-none"
                                            onclick="sortTable('invoice_number')">
                                            <i class="fas fa-receipt me-1"></i>رقم الفاتورة
                                            <i class="fas fa-sort ms-1"></i>
                                        </a>
                                    </th>
                                    <th style="min-width: 150px;">
                                        <a href="#" class="text-white text-decoration-none"
                                            onclick="sortTable('customer_name')">
                                            <i class="fas fa-user me-1"></i>العميل
                                            <i class="fas fa-sort ms-1"></i>
                                        </a>
                                    </th>
                                    <?php if(auth()->user()->canAccessAllBranches()): ?>
                                        <th style="min-width: 100px;">
                                            <i class="fas fa-store me-1"></i>الفرع
                                        </th>
                                    <?php endif; ?>
                                    <th style="min-width: 120px;">
                                        <a href="#" class="text-white text-decoration-none"
                                            onclick="sortTable('created_at')">
                                            <i class="fas fa-calendar me-1"></i>التاريخ
                                            <i class="fas fa-sort ms-1"></i>
                                        </a>
                                    </th>
                                    <th style="min-width: 80px;" class="text-center">
                                        <i class="fas fa-boxes me-1"></i>المنتجات
                                    </th>
                                    <th style="min-width: 120px;">
                                        <a href="#" class="text-white text-decoration-none"
                                            onclick="sortTable('total_amount')">
                                            <i class="fas fa-money-bill me-1"></i>الإجمالي
                                            <i class="fas fa-sort ms-1"></i>
                                        </a>
                                    </th>
                                    <th style="min-width: 140px;">
                                        <i class="fas fa-credit-card me-1"></i>حالة الدفع
                                    </th>
                                    <th style="min-width: 100px;">
                                        <i class="fas fa-user-tie me-1"></i>البائع
                                    </th>
                                    <th style="min-width: 140px;" class="text-center">
                                        <i class="fas fa-cogs me-1"></i>الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $sales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr class="sale-row" data-sale-id="<?php echo e($sale->id); ?>">
                                        <td>
                                            <input type="checkbox" class="form-check-input sale-checkbox"
                                                value="<?php echo e($sale->id); ?>">
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold text-primary"><?php echo e($sale->invoice_number); ?></span>
                                                <small
                                                    class="text-muted"><?php echo e($sale->created_at->format('H:i')); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div
                                                    class="avatar-sm bg-light rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="fas fa-user text-muted"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo e($sale->customer->name ?? 'عميل نقدي'); ?>

                                                    </div>
                                                    <?php if($sale->customer && $sale->customer->phone): ?>
                                                        <small class="text-muted"><?php echo e($sale->customer->phone); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <?php if(auth()->user()->canAccessAllBranches()): ?>
                                            <td>
                                                <span
                                                    class="badge bg-info"><?php echo e($sale->branch->name ?? 'غير محدد'); ?></span>
                                            </td>
                                        <?php endif; ?>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span><?php echo e($sale->created_at->format('Y-m-d')); ?></span>
                                                <small
                                                    class="text-muted"><?php echo e($sale->created_at->diffForHumans()); ?></small>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-secondary fs-6"><?php echo e($sale->items->count()); ?></span>
                                            <br><small class="text-muted">منتج</small>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold fs-6"><?php echo e(number_format($sale->total_amount, 2)); ?>

                                                    ج.م</span>
                                                <?php if($sale->discount_amount > 0): ?>
                                                    <small class="text-warning">خصم:
                                                        <?php echo e(number_format($sale->discount_amount, 2)); ?> ج.م</small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            
                                            <div class="d-flex flex-column">
                                                <span
                                                    class="badge bg-<?php echo e($sale->payment_status_color); ?> px-2 py-1 mb-1 fw-bold">
                                                    <?php if($sale->payment_status === 'paid'): ?>
                                                        <i class="fas fa-check-circle me-1"></i>
                                                    <?php elseif($sale->payment_status === 'partial'): ?>
                                                        <i class="fas fa-clock me-1"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-times-circle me-1"></i>
                                                    <?php endif; ?>
                                                    <?php echo e($sale->payment_status_label); ?>

                                                </span>
                                                <?php if($sale->payment_status !== 'paid'): ?>
                                                    <div class="small text-muted">
                                                        <?php if($sale->payment_status === 'partial'): ?>
                                                            <div>مدفوع:
                                                                <?php echo e(number_format($sale->getTotalPaymentsAttribute(), 2)); ?>

                                                                ج.م</div>
                                                            <div class="text-danger fw-bold">
                                                                متبقي:
                                                                <?php echo e(number_format($sale->getActualRemainingAmountAttribute(), 2)); ?>

                                                                ج.م
                                                            </div>
                                                        <?php else: ?>
                                                            <div class="text-danger fw-bold">
                                                                المطلوب:
                                                                <?php echo e(number_format($sale->total_amount - $sale->discount_amount, 2)); ?>

                                                                ج.م
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="small text-success">
                                                        مدفوع:
                                                        <?php echo e(number_format($sale->getTotalPaymentsAttribute(), 2)); ?>

                                                        ج.م
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            
                                        </td>
                                        <td>
                                            <?php if($sale->user): ?>
                                                <div class="d-flex align-items-center">
                                                    <div
                                                        class="avatar-xs bg-primary rounded-circle d-flex align-items-center justify-content-center me-1">
                                                        <i class="fas fa-user text-white"
                                                            style="font-size: 0.7rem;"></i>
                                                    </div>
                                                    <small><?php echo e($sale->user->name); ?></small>
                                                </div>
                                            <?php else: ?>
                                                <small class="text-muted">غير محدد</small>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex gap-1 justify-content-center flex-wrap">
                                                <!-- Quick Action Buttons -->
                                                <a href="<?php echo e(user_route('sales.show', $sale)); ?>"
                                                    class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if($sale->status === 'completed' && $sale->canReceivePayment()): ?>
                                                    <a href="<?php echo e(user_route('customer-payments.create', ['sale_id' => $sale->id])); ?>"
                                                        class="btn btn-outline-success btn-sm" title="إضافة دفعة">
                                                        <i class="fas fa-money-bill-wave"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <?php if($sale->status === 'completed' && $sale->canBeReturned()): ?>
                                                    <a href="<?php echo e(user_route('sale-returns.create', ['sale_id' => $sale->id])); ?>"
                                                        class="btn btn-outline-warning btn-sm" title="إنشاء مرتجع">
                                                        <i class="fas fa-undo"></i>
                                                    </a>
                                                <?php endif; ?>

                                                <!-- Dropdown Menu -->
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button"
                                                        class="btn btn-outline-primary dropdown-toggle"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fas fa-cog"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item"
                                                                href="<?php echo e(user_route('sales.show', $sale)); ?>">
                                                                <i class="fas fa-eye text-info"></i> عرض التفاصيل
                                                            </a>
                                                        </li>
                                                        <?php if($sale->status === 'pending'): ?>
                                                            <li>
                                                                <a class="dropdown-item"
                                                                    href="<?php echo e(user_route('sales.edit', $sale)); ?>">
                                                                    <i class="fas fa-edit text-primary"></i> تعديل
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                        <li>
                                                            <a class="dropdown-item"
                                                                href="<?php echo e(user_route('sales.print', $sale)); ?>"
                                                                target="_blank">
                                                                <i class="fas fa-print text-secondary"></i> طباعة
                                                            </a>
                                                        </li>
                                                        <?php if(isset($sale->receipt_url)): ?>
                                                            <li>
                                                                <a class="dropdown-item"
                                                                    href="<?php echo e($sale->receipt_url); ?>" target="_blank">
                                                                    <i class="fas fa-receipt text-info"></i> إيصال
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                        <?php if($sale->status === 'completed'): ?>
                                                            <?php if($sale->canReceivePayment()): ?>
                                                                <li>
                                                                    <a class="dropdown-item"
                                                                        href="<?php echo e(user_route('customer-payments.create', ['sale_id' => $sale->id])); ?>">
                                                                        <i
                                                                            class="fas fa-money-bill-wave text-success"></i>
                                                                        إضافة دفعة
                                                                    </a>
                                                                </li>
                                                            <?php endif; ?>
                                                            <?php if($sale->canBeReturned()): ?>
                                                                <li>
                                                                    <a class="dropdown-item"
                                                                        href="<?php echo e(user_route('sale-returns.create', ['sale_id' => $sale->id])); ?>">
                                                                        <i class="fas fa-undo text-warning"></i> إنشاء
                                                                        مرتجع
                                                                    </a>
                                                                </li>
                                                            <?php endif; ?>
                                                            <li>
                                                                <hr class="dropdown-divider">
                                                            </li>
                                                        <?php endif; ?>
                                                        <?php if($sale->status === 'pending' && auth()->user()->hasRole('admin')): ?>
                                                            <li>
                                                                <form
                                                                    action="<?php echo e(user_route('sales.destroy', $sale)); ?>"
                                                                    method="POST" class="d-inline">
                                                                    <?php echo csrf_field(); ?>
                                                                    <?php echo method_field('DELETE'); ?>
                                                                    <button type="submit"
                                                                        class="dropdown-item text-danger"
                                                                        onclick="return confirm('هل أنت متأكد من حذف هذه العملية؟')">
                                                                        <i class="fas fa-trash"></i> حذف
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="<?php echo e(auth()->user()->canAccessAllBranches() ? '10' : '9'); ?>"
                                            class="text-center py-5">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                                <h5 class="text-muted">لا توجد عمليات بيع</h5>
                                                <p class="text-muted">لم يتم العثور على عمليات بيع مطابقة للمعايير
                                                    المحددة</p>
                                                <a href="<?php echo e(user_route('sales.create')); ?>" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> إنشاء عملية بيع جديدة
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Bulk Actions Bar -->
                    <div class="d-flex justify-content-between align-items-center mt-3" id="bulkActionsBar"
                        style="display: none !important;">
                        <div class="d-flex align-items-center">
                            <span class="me-3">
                                <span id="selectedCount">0</span> عنصر محدد
                            </span>
                            <div class="btn-group btn-group-sm">
                                <?php if(auth()->user()->hasRole('admin')): ?>
                                    <button type="button" class="btn btn-outline-danger" onclick="bulkDelete()">
                                        <i class="fas fa-trash"></i> حذف المحدد
                                    </button>
                                <?php endif; ?>
                                <button type="button" class="btn btn-outline-primary" onclick="bulkPrint()">
                                    <i class="fas fa-print"></i> طباعة المحدد
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="bulkExport()">
                                    <i class="fas fa-download"></i> تصدير المحدد
                                </button>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                            <i class="fas fa-times"></i> إلغاء التحديد
                        </button>
                    </div>

                    <!-- Enhanced Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div class="d-flex align-items-center">
                            <span class="text-muted me-3">
                                عرض <?php echo e($sales->firstItem() ?? 0); ?> إلى <?php echo e($sales->lastItem() ?? 0); ?>

                                من أصل <?php echo e($sales->total()); ?> عملية
                            </span>
                            <select class="form-select form-select-sm" style="width: auto;"
                                onchange="changePerPage(this.value)">
                                <option value="10" <?php echo e(request('per_page') == 10 ? 'selected' : ''); ?>>10 لكل صفحة
                                </option>
                                <option value="25" <?php echo e(request('per_page') == 25 ? 'selected' : ''); ?>>25 لكل صفحة
                                </option>
                                <option value="50" <?php echo e(request('per_page') == 50 ? 'selected' : ''); ?>>50 لكل صفحة
                                </option>
                                <option value="100" <?php echo e(request('per_page') == 100 ? 'selected' : ''); ?>>100 لكل صفحة
                                </option>
                            </select>
                        </div>
                        <div>
                            <?php echo e($sales->appends(request()->query())->links()); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
        <style>
            .border-left-primary {
                border-left: 0.25rem solid #4e73df !important;
            }

            .border-left-success {
                border-left: 0.25rem solid #1cc88a !important;
            }

            .border-left-info {
                border-left: 0.25rem solid #36b9cc !important;
            }

            /* Table Design Improvements */
            #salesTable {
                font-size: 0.9rem;
            }

            #salesTable th {
                font-size: 0.85rem;
                font-weight: 600;
                white-space: nowrap;
                vertical-align: middle;
                border-bottom: 2px solid #dee2e6;
                padding: 0.75rem 0.5rem;
            }

            #salesTable td {
                vertical-align: middle;
                padding: 0.75rem 0.5rem;
            }

            .avatar-sm {
                width: 32px;
                height: 32px;
                font-size: 0.8rem;
            }

            .avatar-xs {
                width: 24px;
                height: 24px;
                font-size: 0.7rem;
            }

            .table-responsive {
                border-radius: 0.375rem;
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            }

            .btn-group-sm .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }

            /* Responsive adjustments */
            @media (max-width: 768px) {
                #salesTable {
                    font-size: 0.8rem;
                }

                #salesTable th,
                #salesTable td {
                    padding: 0.5rem 0.25rem;
                }

                .btn-sm {
                    padding: 0.2rem 0.4rem;
                    font-size: 0.7rem;
                }
            }

            /* Badge improvements */
            .badge {
                font-size: 0.75rem;
                font-weight: 500;
            }

            /* Payment status colors */
            .bg-success {
                background-color: #28a745 !important;
            }

            .bg-warning {
                background-color: #ffc107 !important;
                color: #212529 !important;
            }

            .bg-danger {
                background-color: #dc3545 !important;
            }

            .bg-info {
                background-color: #17a2b8 !important;
            }

            .bg-secondary {
                background-color: #6c757d !important;
            }

            .border-left-warning {
                border-left: 0.25rem solid #f6c23e !important;
            }

            .avatar-sm {
                width: 2rem;
                height: 2rem;
            }

            .avatar-xs {
                width: 1.5rem;
                height: 1.5rem;
            }

            .sale-row:hover {
                background-color: #f8f9fa;
            }

            .table th {
                border-top: none;
                font-weight: 600;
                font-size: 0.875rem;
            }

            .btn-group-sm>.btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }
        </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
        <script>
            // Global variables
            let selectedSales = [];

            // Initialize page
            document.addEventListener('DOMContentLoaded', function() {
                initializeCheckboxes();
                initializeTooltips();
            });

            // Checkbox functionality
            function initializeCheckboxes() {
                const selectAllCheckbox = document.getElementById('selectAll');
                const saleCheckboxes = document.querySelectorAll('.sale-checkbox');

                selectAllCheckbox.addEventListener('change', function() {
                    saleCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateSelectedSales();
                });

                saleCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', updateSelectedSales);
                });
            }

            function updateSelectedSales() {
                const checkboxes = document.querySelectorAll('.sale-checkbox:checked');
                selectedSales = Array.from(checkboxes).map(cb => cb.value);

                const selectedCount = selectedSales.length;
                document.getElementById('selectedCount').textContent = selectedCount;

                const bulkActionsBar = document.getElementById('bulkActionsBar');
                if (selectedCount > 0) {
                    bulkActionsBar.style.display = 'flex';
                } else {
                    bulkActionsBar.style.display = 'none';
                }

                // Update select all checkbox state
                const selectAllCheckbox = document.getElementById('selectAll');
                const totalCheckboxes = document.querySelectorAll('.sale-checkbox').length;

                if (selectedCount === 0) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = false;
                } else if (selectedCount === totalCheckboxes) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = true;
                } else {
                    selectAllCheckbox.indeterminate = true;
                }
            }

            function clearSelection() {
                document.querySelectorAll('.sale-checkbox').forEach(cb => cb.checked = false);
                document.getElementById('selectAll').checked = false;
                updateSelectedSales();
            }

            // Filter functions
            function setDateFilter(period) {
                const startDateInput = document.querySelector('input[name="start_date"]');
                const endDateInput = document.querySelector('input[name="end_date"]');
                const today = new Date();

                let startDate, endDate;

                switch (period) {
                    case 'today':
                        startDate = endDate = today.toISOString().split('T')[0];
                        break;
                    case 'yesterday':
                        const yesterday = new Date(today);
                        yesterday.setDate(yesterday.getDate() - 1);
                        startDate = endDate = yesterday.toISOString().split('T')[0];
                        break;
                    case 'week':
                        const weekStart = new Date(today);
                        weekStart.setDate(today.getDate() - today.getDay());
                        startDate = weekStart.toISOString().split('T')[0];
                        endDate = today.toISOString().split('T')[0];
                        break;
                    case 'month':
                        startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
                        endDate = today.toISOString().split('T')[0];
                        break;
                    case 'last_month':
                        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
                        startDate = lastMonth.toISOString().split('T')[0];
                        endDate = lastMonthEnd.toISOString().split('T')[0];
                        break;
                }

                startDateInput.value = startDate;
                endDateInput.value = endDate;
                document.getElementById('filterForm').submit();
            }

            function clearFilters() {
                const form = document.getElementById('filterForm');
                form.querySelectorAll('input, select').forEach(input => {
                    if (input.type === 'checkbox' || input.type === 'radio') {
                        input.checked = false;
                    } else {
                        input.value = '';
                    }
                });
                form.submit();
            }

            function changePerPage(perPage) {
                const url = new URL(window.location);
                url.searchParams.set('per_page', perPage);
                url.searchParams.delete('page'); // Reset to first page
                window.location.href = url.toString();
            }

            // Bulk actions
            function bulkDelete() {
                if (selectedSales.length === 0) return;

                if (confirm(`هل أنت متأكد من حذف ${selectedSales.length} عملية بيع؟`)) {
                    // Implementation for bulk delete
                    console.log('Bulk delete:', selectedSales);
                }
            }

            function bulkPrint() {
                if (selectedSales.length === 0) return;

                selectedSales.forEach(saleId => {
                    const printUrl = `<?php echo e(user_route('sales.print', ':id')); ?>`.replace(':id', saleId);
                    window.open(printUrl, '_blank');
                });
            }

            function bulkExport() {
                if (selectedSales.length === 0) return;

                const exportUrl = `<?php echo e(user_route('sales.index')); ?>?export=excel&ids=${selectedSales.join(',')}`;
                window.location.href = exportUrl;
            }

            // Export functions
            function exportSales(format) {
                const url = new URL(window.location);
                url.searchParams.set('export', format);
                window.location.href = url.toString();
            }

            // Sort functionality
            function sortTable(column) {
                const url = new URL(window.location);
                const currentSort = url.searchParams.get('sort');
                const currentDirection = url.searchParams.get('direction') || 'asc';

                if (currentSort === column) {
                    url.searchParams.set('direction', currentDirection === 'asc' ? 'desc' : 'asc');
                } else {
                    url.searchParams.set('sort', column);
                    url.searchParams.set('direction', 'asc');
                }

                window.location.href = url.toString();
            }

            // Initialize tooltips
            function initializeTooltips() {
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function(tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }

            // Auto-refresh functionality (optional)
            function enableAutoRefresh(interval = 30000) {
                setInterval(() => {
                    if (selectedSales.length === 0) {
                        window.location.reload();
                    }
                }, interval);
            }
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\sales\index.blade.php ENDPATH**/ ?>