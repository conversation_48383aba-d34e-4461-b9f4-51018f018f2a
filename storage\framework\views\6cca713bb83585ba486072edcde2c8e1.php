<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h3 mb-0">إدارة العملاء</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="<?php echo e(route('seller.dashboard')); ?>">لوحة التحكم</a></li>
                                <li class="breadcrumb-item active">العملاء</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-store"></i> <?php echo e(auth()->user()->branch->name ?? 'غير محدد'); ?>

                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-gradient rounded-3 p-3">
                                    <i class="fas fa-users text-white fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">إجمالي العملاء</h6>
                                <h4 class="mb-0"><?php echo e(number_format($customerSummary['total_customers'] ?? 0)); ?></h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-gradient rounded-3 p-3">
                                    <i class="fas fa-chart-line text-white fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">إجمالي المبيعات</h6>
                                <h4 class="mb-0"><?php echo e(number_format($customerSummary['total_sales'] ?? 0, 2)); ?> ج.م</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-gradient rounded-3 p-3">
                                    <i class="fas fa-money-bill-wave text-white fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">المبلغ المدفوع</h6>
                                <h4 class="mb-0"><?php echo e(number_format($customerSummary['total_paid'] ?? 0, 2)); ?> ج.م</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-gradient rounded-3 p-3">
                                    <i class="fas fa-exclamation-triangle text-white fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">المبلغ المستحق</h6>
                                <h4 class="mb-0"><?php echo e(number_format($customerSummary['total_owed'] ?? 0, 2)); ?> ج.م</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <form method="GET" action="<?php echo e(route('seller.customers.index')); ?>" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           value="<?php echo e(request('search')); ?>" 
                                           placeholder="البحث بالاسم، الهاتف، البريد الإلكتروني...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="balance_status" class="form-label">حالة الرصيد</label>
                                <select class="form-select" id="balance_status" name="balance_status">
                                    <option value="">جميع العملاء</option>
                                    <option value="owing" <?php echo e(request('balance_status') === 'owing' ? 'selected' : ''); ?>>
                                        عملاء مدينون
                                    </option>
                                    <option value="paid" <?php echo e(request('balance_status') === 'paid' ? 'selected' : ''); ?>>
                                        عملاء مسددون
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="<?php echo e(route('seller.customers.index')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> إعادة تعيين
                                </a>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <a href="<?php echo e(route('seller.customers.create')); ?>" class="btn btn-success w-100">
                                    <i class="fas fa-plus"></i> إضافة عميل
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers Table -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-list text-primary"></i> قائمة العملاء
                                <?php if(request('search')): ?>
                                    <small class="text-muted">(نتائج البحث عن: "<?php echo e(request('search')); ?>")</small>
                                <?php endif; ?>
                            </h5>
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-file-excel"></i> تصدير Excel
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-print"></i> طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0">العميل</th>
                                        <th class="border-0">معلومات الاتصال</th>
                                        <th class="border-0">إجمالي المشتريات</th>
                                        <th class="border-0">المبلغ المدفوع</th>
                                        <th class="border-0">المبلغ المستحق</th>
                                        <th class="border-0">آخر عملية شراء</th>
                                        <th class="border-0 text-center">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                        <?php echo e(strtoupper(substr($customer->name, 0, 1))); ?>

                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0 fw-semibold"><?php echo e($customer->name); ?></h6>
                                                        <?php if($customer->address): ?>
                                                            <small class="text-muted"><?php echo e(Str::limit($customer->address, 40)); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <?php if($customer->phone): ?>
                                                        <div class="mb-1">
                                                            <i class="fas fa-phone text-muted me-1"></i>
                                                            <span><?php echo e($customer->phone); ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if($customer->email): ?>
                                                        <div>
                                                            <i class="fas fa-envelope text-muted me-1"></i>
                                                            <small><?php echo e($customer->email); ?></small>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if(!$customer->phone && !$customer->email): ?>
                                                        <span class="text-muted">غير متوفر</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-semibold"><?php echo e(number_format($customer->total_sales_amount, 2)); ?> ج.م</span>
                                                <br><small class="text-muted"><?php echo e($customer->sales_count); ?> فاتورة</small>
                                            </td>
                                            <td>
                                                <span class="text-success fw-semibold"><?php echo e(number_format($customer->total_paid_amount, 2)); ?> ج.م</span>
                                            </td>
                                            <td>
                                                <?php if($customer->total_remaining_amount > 0): ?>
                                                    <span class="badge bg-warning fs-6"><?php echo e(number_format($customer->total_remaining_amount, 2)); ?> ج.م</span>
                                                <?php else: ?>
                                                    <span class="badge bg-success fs-6">مسدد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($customer->last_sale_date): ?>
                                                    <span class="fw-semibold"><?php echo e(\Carbon\Carbon::parse($customer->last_sale_date)->format('Y-m-d')); ?></span>
                                                    <br><small class="text-muted"><?php echo e(\Carbon\Carbon::parse($customer->last_sale_date)->diffForHumans()); ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">لا توجد مشتريات</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?php echo e(route('seller.customers.show', $customer)); ?>" 
                                                       class="btn btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('seller.customers.edit', $customer)); ?>" 
                                                       class="btn btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if($customer->total_remaining_amount > 0): ?>
                                                        <a href="<?php echo e(route('seller.customer-payments.create', ['customer_id' => $customer->id])); ?>" 
                                                           class="btn btn-outline-success" title="تسجيل دفعة">
                                                            <i class="fas fa-money-bill"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <a href="<?php echo e(route('seller.sales.create', ['customer_id' => $customer->id])); ?>" 
                                                       class="btn btn-outline-info" title="بيع جديد">
                                                        <i class="fas fa-plus"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="7" class="text-center py-5">
                                                <div class="text-muted">
                                                    <i class="fas fa-users fa-3x mb-3"></i>
                                                    <h5>لا توجد عملاء</h5>
                                                    <?php if(request('search')): ?>
                                                        <p>لا توجد عملاء تطابق معايير البحث المحددة</p>
                                                        <a href="<?php echo e(route('seller.customers.index')); ?>" class="btn btn-outline-primary">
                                                            <i class="fas fa-times"></i> إعادة تعيين البحث
                                                        </a>
                                                    <?php else: ?>
                                                        <p>لم يقم أي عميل بالشراء من فرعك بعد</p>
                                                        <a href="<?php echo e(route('seller.customers.create')); ?>" class="btn btn-primary">
                                                            <i class="fas fa-plus"></i> إضافة عميل جديد
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <?php if(method_exists($customers, 'hasPages') && $customers->hasPages()): ?>
                        <div class="card-footer bg-white border-top">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-muted">
                                    عرض <?php echo e($customers->firstItem()); ?> إلى <?php echo e($customers->lastItem()); ?> من أصل <?php echo e($customers->total()); ?> عميل
                                </div>
                                <?php echo e($customers->appends(request()->query())->links()); ?>

                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Outstanding Balances Alert -->
        <?php if(($customerSummary['total_owed'] ?? 0) > 0): ?>
            <div class="row mt-4">
                <div class="col-12">
                    <div class="alert alert-warning border-0 shadow-sm">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="alert-heading mb-1">تنبيه مستحقات العملاء</h5>
                                <p class="mb-2">
                                    إجمالي المبلغ المستحق من العملاء: 
                                    <strong><?php echo e(number_format($customerSummary['total_owed'], 2)); ?> ج.م</strong>
                                    من <strong><?php echo e($customerSummary['customers_owing'] ?? 0); ?></strong> عميل
                                </p>
                                <a href="<?php echo e(route('seller.customers.index', ['balance_status' => 'owing'])); ?>" 
                                   class="btn btn-warning btn-sm">
                                    <i class="fas fa-eye"></i> عرض العملاء المدينين
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\seller\customers\index.blade.php ENDPATH**/ ?>