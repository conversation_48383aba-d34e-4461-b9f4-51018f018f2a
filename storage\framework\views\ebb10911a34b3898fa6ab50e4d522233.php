<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid px-4">
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="d-flex align-items-center">
                <div class="avatar-circle bg-info text-white me-3 d-inline-flex">
                    <i class="fas fa-tag"></i>
                </div>
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 leading-tight mb-0">
                        <?php echo e($category->name); ?>

                    </h2>
                    <p class="text-muted mb-0 small">تفاصيل الفئة ومنتجاتها</p>
                </div>
            </div>
            <div class="d-flex align-items-center gap-2">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(user_route('dashboard')); ?>">الرئيسية</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(user_route('categories.index')); ?>">الفئات</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo e($category->name); ?></li>
                    </ol>
                </nav>
                <div class="d-flex gap-2">
                    <?php if(auth()->user()->isAdmin()): ?>
                        <a href="<?php echo e(user_route('categories.edit', $category)); ?>" class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                    <?php endif; ?>
                    <a href="<?php echo e(user_route('categories.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> عودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي المنتجات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($category->products->count()); ?>

                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-cubes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    المنتجات المتوفرة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo e($category->products->where('total_stock', '>', 0)->count()); ?>

                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    إجمالي المخزون
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo e(number_format($category->products->sum('total_stock'))); ?>

                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-boxes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    قيمة المخزون
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo e(number_format(
                                        $category->products->sum(function ($product) {
                                            return $product->total_stock * ($product->price ?? 0);
                                        }),
                                        2,
                                    )); ?>

                                    ج.م
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Products Section -->
            <div class="col-xl-8 col-lg-7">
                <div class="card shadow border-0">
                    <div class="card-header bg-gradient-success text-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-cubes fa-lg me-3"></i>
                                <h5 class="mb-0 fw-bold">منتجات الفئة (<?php echo e($category->products->count()); ?>)</h5>
                            </div>
                            <div class="d-flex gap-2">
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="view-mode" id="table-view" checked>
                                    <label class="btn btn-outline-light btn-sm" for="table-view">
                                        <i class="fas fa-table"></i>
                                    </label>
                                    <input type="radio" class="btn-check" name="view-mode" id="grid-view">
                                    <label class="btn btn-outline-light btn-sm" for="grid-view">
                                        <i class="fas fa-th"></i>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <?php if($category->products->count() > 0): ?>
                                <!-- Search and Filter Bar -->
                                <div class="p-3 border-bottom bg-light">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-search text-muted"></i>
                                                </span>
                                                <input type="text" id="product-search" class="form-control"
                                                    placeholder="البحث في المنتجات...">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <select id="stock-filter" class="form-select">
                                                <option value="">جميع المنتجات</option>
                                                <option value="available">متوفر</option>
                                                <option value="low">مخزون منخفض</option>
                                                <option value="out">غير متوفر</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <select id="sort-products" class="form-select">
                                                <option value="name">ترتيب حسب الاسم</option>
                                                <option value="price">ترتيب حسب السعر</option>
                                                <option value="stock">ترتيب حسب المخزون</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Table View -->
                                <div id="table-view-content">
                                    <div class="table-responsive">
                                        <table class="table table-hover align-middle mb-0">
                                            <thead class="table-light">
                                                <tr>
                                                    <th width="80">الصورة</th>
                                                    <th>المنتج</th>
                                                    <th>السعر</th>
                                                    <th>المخزون الإجمالي</th>
                                                    <th>توزيع المخزون</th>
                                                    <th>الحالة</th>
                                                    <th width="120">الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="products-table-body">
                                                <?php $__currentLoopData = $category->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr class="product-row"
                                                        data-name="<?php echo e(strtolower($product->name)); ?>"
                                                        data-price="<?php echo e($product->price ?? 0); ?>"
                                                        data-stock="<?php echo e($product->total_stock ?? 0); ?>">
                                                        <td>
                                                            <div class="product-image-container">
                                                                <?php if($product->image): ?>
                                                                    <img src="<?php echo e(asset('storage/' . $product->image)); ?>"
                                                                        alt="<?php echo e($product->name); ?>"
                                                                        class="img-thumbnail product-image"
                                                                        style="width: 60px; height: 60px; object-fit: cover;">
                                                                <?php else: ?>
                                                                    <div class="avatar-circle bg-secondary text-white"
                                                                        style="width: 60px; height: 60px;">
                                                                        <i class="fas fa-box"></i>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div>
                                                                <h6 class="mb-1 fw-bold"><?php echo e($product->name); ?></h6>
                                                                <small class="text-muted">
                                                                    SKU: <?php echo e($product->sku ?: 'غير محدد'); ?>

                                                                </small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="fw-bold text-success">
                                                                <?php echo e(number_format($product->price ?? 0, 2)); ?> ج.م
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="fw-bold">
                                                                <?php echo e(number_format($product->total_stock ?? 0)); ?>

                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="stock-distribution">
                                                                <?php
                                                                    $branchStock = $product->branchInventories->sum(
                                                                        'quantity',
                                                                    );
                                                                    $storeStock = $product->storeInventories->sum(
                                                                        'quantity',
                                                                    );
                                                                ?>
                                                                <small class="d-block">
                                                                    <i class="fas fa-store text-primary"></i>
                                                                    فروع: <?php echo e(number_format($branchStock)); ?>

                                                                </small>
                                                                <small class="d-block">
                                                                    <i class="fas fa-warehouse text-info"></i>
                                                                    مخازن: <?php echo e(number_format($storeStock)); ?>

                                                                </small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <?php
                                                                $totalStock = $product->total_stock ?? 0;
                                                            ?>
                                                            <?php if($totalStock > 10): ?>
                                                                <span class="badge bg-success">متوفر</span>
                                                            <?php elseif($totalStock > 0): ?>
                                                                <span class="badge bg-warning">منخفض</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">غير متوفر</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <a href="<?php echo e(user_route('products.show', $product)); ?>"
                                                                    class="btn btn-sm btn-outline-info"
                                                                    data-bs-toggle="tooltip" title="عرض التفاصيل">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                <?php if(auth()->user()->isAdmin()): ?>
                                                                    <a href="<?php echo e(user_route('products.edit', $product)); ?>"
                                                                        class="btn btn-sm btn-outline-primary"
                                                                        data-bs-toggle="tooltip" title="تعديل">
                                                                        <i class="fas fa-edit"></i>
                                                                    </a>
                                                                <?php endif; ?>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Grid View (Hidden by default) -->
                                <div id="grid-view-content" style="display: none;" class="p-3">
                                    <div class="row" id="products-grid">
                                        <?php $__currentLoopData = $category->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="col-xl-4 col-lg-6 col-md-6 mb-4 product-card"
                                                data-name="<?php echo e(strtolower($product->name)); ?>"
                                                data-price="<?php echo e($product->price ?? 0); ?>"
                                                data-stock="<?php echo e($product->total_stock ?? 0); ?>">
                                                <div class="card h-100 shadow-sm border-0 product-card-inner">
                                                    <div class="position-relative">
                                                        <?php if($product->image): ?>
                                                            <img src="<?php echo e(asset('storage/' . $product->image)); ?>"
                                                                class="card-img-top"
                                                                style="height: 200px; object-fit: cover;"
                                                                alt="<?php echo e($product->name); ?>">
                                                        <?php else: ?>
                                                            <div class="card-img-top d-flex align-items-center justify-content-center bg-light"
                                                                style="height: 200px;">
                                                                <i class="fas fa-box fa-3x text-muted"></i>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php
                                                            $totalStock = $product->total_stock ?? 0;
                                                        ?>
                                                        <div class="position-absolute top-0 end-0 m-2">
                                                            <?php if($totalStock > 10): ?>
                                                                <span class="badge bg-success">متوفر</span>
                                                            <?php elseif($totalStock > 0): ?>
                                                                <span class="badge bg-warning">منخفض</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">غير متوفر</span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>

                                                    <div class="card-body">
                                                        <h6 class="card-title fw-bold"><?php echo e($product->name); ?></h6>
                                                        <p class="card-text text-muted small">
                                                            SKU: <?php echo e($product->sku ?: 'غير محدد'); ?>

                                                        </p>

                                                        <div
                                                            class="d-flex justify-content-between align-items-center mb-2">
                                                            <span class="fw-bold text-success">
                                                                <?php echo e(number_format($product->price ?? 0, 2)); ?> ج.م
                                                            </span>
                                                            <span class="badge bg-primary">
                                                                <?php echo e(number_format($product->total_stock ?? 0)); ?>

                                                                قطعة
                                                            </span>
                                                        </div>

                                                        <div class="stock-info mb-3">
                                                            <?php
                                                                $branchStock = $product->branchInventories->sum(
                                                                    'quantity',
                                                                );
                                                                $storeStock = $product->storeInventories->sum(
                                                                    'quantity',
                                                                );
                                                            ?>
                                                            <small class="d-block text-muted">
                                                                <i class="fas fa-store"></i> فروع:
                                                                <?php echo e(number_format($branchStock)); ?>

                                                            </small>
                                                            <small class="d-block text-muted">
                                                                <i class="fas fa-warehouse"></i> مخازن:
                                                                <?php echo e(number_format($storeStock)); ?>

                                                            </small>
                                                        </div>
                                                    </div>

                                                    <div class="card-footer bg-transparent border-0">
                                                        <div class="d-flex justify-content-center gap-1">
                                                            <a href="<?php echo e(user_route('products.show', $product)); ?>"
                                                                class="btn btn-sm btn-outline-info">
                                                                <i class="fas fa-eye"></i> عرض
                                                            </a>
                                                            <?php if(auth()->user()->isAdmin()): ?>
                                                                <a href="<?php echo e(user_route('products.edit', $product)); ?>"
                                                                    class="btn btn-sm btn-outline-primary">
                                                                    <i class="fas fa-edit"></i> تعديل
                                                                </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="fas fa-box-open fa-3x mb-3"></i>
                                        <h5>لا توجد منتجات في هذه الفئة</h5>
                                        <p>لم يتم إضافة أي منتجات لهذه الفئة بعد.</p>
                                        <a href="<?php echo e(user_route('products.create')); ?>?category_id=<?php echo e($category->id); ?>"
                                            class="btn btn-primary">
                                            <i class="fas fa-plus"></i> إضافة منتج جديد
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Category Information Card -->
            <div class="col-xl-4 col-lg-5 mb-4">
                <div class="card shadow border-0 h-100">
                    <div class="card-header bg-gradient-primary text-white py-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle fa-lg me-3"></i>
                            <h5 class="mb-0 fw-bold">معلومات الفئة</h5>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <!-- Category Icon and Name -->
                        <div class="text-center mb-4">
                            <div class="avatar-circle bg-primary text-white mx-auto mb-3"
                                style="width: 80px; height: 80px; font-size: 2rem;">
                                <i class="fas fa-tag"></i>
                            </div>
                            <h4 class="fw-bold text-primary"><?php echo e($category->name); ?></h4>
                            <p class="text-muted"><?php echo e($category->description ?: 'لا يوجد وصف لهذه الفئة'); ?></p>
                        </div>

                        <!-- Category Details -->
                        <div class="category-details">
                            <div
                                class="detail-item d-flex justify-content-between align-items-center py-3 border-bottom">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-tag text-primary me-3"></i>
                                    <span class="fw-bold">اسم الفئة</span>
                                </div>
                                <span class="text-muted"><?php echo e($category->name); ?></span>
                            </div>

                            <div
                                class="detail-item d-flex justify-content-between align-items-center py-3 border-bottom">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-cubes text-success me-3"></i>
                                    <span class="fw-bold">عدد المنتجات</span>
                                </div>
                                <span class="badge bg-success"><?php echo e($category->products->count()); ?></span>
                            </div>

                            <div
                                class="detail-item d-flex justify-content-between align-items-center py-3 border-bottom">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-calendar text-info me-3"></i>
                                    <span class="fw-bold">تاريخ الإنشاء</span>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold"><?php echo e($category->created_at->format('Y-m-d')); ?></div>
                                    <small class="text-muted"><?php echo e($category->created_at->diffForHumans()); ?></small>
                                </div>
                            </div>

                            <div class="detail-item d-flex justify-content-between align-items-center py-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-clock text-warning me-3"></i>
                                    <span class="fw-bold">آخر تحديث</span>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold"><?php echo e($category->updated_at->format('Y-m-d')); ?></div>
                                    <small class="text-muted"><?php echo e($category->updated_at->diffForHumans()); ?></small>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-4 pt-3 border-top">
                            <h6 class="fw-bold mb-3">إجراءات سريعة</h6>
                            <div class="d-grid gap-2">
                                <?php if(auth()->user()->isAdmin()): ?>
                                    <a href="<?php echo e(user_route('categories.edit', $category)); ?>"
                                        class="btn btn-outline-primary">
                                        <i class="fas fa-edit me-2"></i>تعديل الفئة
                                    </a>
                                <?php endif; ?>
                                <a href="<?php echo e(user_route('products.create')); ?>?category_id=<?php echo e($category->id); ?>"
                                    class="btn btn-outline-success">
                                    <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                                </a>
                                <button class="btn btn-outline-info" onclick="exportCategoryProducts()">
                                    <i class="fas fa-download me-2"></i>تصدير المنتجات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
        <style>
            .avatar-circle {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
            }

            .border-left-primary {
                border-left: 0.25rem solid #4e73df !important;
            }

            .border-left-success {
                border-left: 0.25rem solid #1cc88a !important;
            }

            .border-left-warning {
                border-left: 0.25rem solid #f6c23e !important;
            }

            .border-left-info {
                border-left: 0.25rem solid #36b9cc !important;
            }

            .bg-gradient-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            .bg-gradient-success {
                background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
            }

            .detail-item {
                transition: background-color 0.2s ease;
            }

            .detail-item:hover {
                background-color: #f8f9fa;
            }

            .product-card-inner {
                transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            }

            .product-card-inner:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
            }

            .product-image {
                border-radius: 8px;
            }

            .stock-distribution small {
                line-height: 1.2;
            }

            .table th {
                border-top: none;
                font-weight: 600;
                font-size: 0.85rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .btn-group .btn {
                margin: 0 1px;
            }

            .card {
                transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            }

            .category-details .detail-item {
                border-color: #e3e6f0 !important;
            }

            .stock-info {
                font-size: 0.85rem;
            }

            .product-row {
                transition: background-color 0.2s ease;
            }

            .product-row:hover {
                background-color: #f8f9fa;
            }
        </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize tooltips
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });

                // View mode toggle
                const tableViewBtn = document.getElementById('table-view');
                const gridViewBtn = document.getElementById('grid-view');
                const tableContent = document.getElementById('table-view-content');
                const gridContent = document.getElementById('grid-view-content');

                if (tableViewBtn && gridViewBtn) {
                    tableViewBtn.addEventListener('change', function() {
                        if (this.checked) {
                            tableContent.style.display = 'block';
                            gridContent.style.display = 'none';
                        }
                    });

                    gridViewBtn.addEventListener('change', function() {
                        if (this.checked) {
                            tableContent.style.display = 'none';
                            gridContent.style.display = 'block';
                        }
                    });
                }

                // Search functionality
                const searchInput = document.getElementById('product-search');
                const stockFilter = document.getElementById('stock-filter');
                const sortSelect = document.getElementById('sort-products');

                function filterAndSortProducts() {
                    const searchTerm = searchInput.value.toLowerCase();
                    const stockFilterValue = stockFilter.value;
                    const sortValue = sortSelect.value;

                    // Get all product rows and cards
                    const productRows = document.querySelectorAll('.product-row');
                    const productCards = document.querySelectorAll('.product-card');

                    // Filter products
                    [...productRows, ...productCards].forEach(element => {
                        const name = element.dataset.name;
                        const stock = parseInt(element.dataset.stock);

                        let showElement = true;

                        // Search filter
                        if (searchTerm && !name.includes(searchTerm)) {
                            showElement = false;
                        }

                        // Stock filter
                        if (stockFilterValue) {
                            switch (stockFilterValue) {
                                case 'available':
                                    if (stock <= 10) showElement = false;
                                    break;
                                case 'low':
                                    if (stock <= 0 || stock > 10) showElement = false;
                                    break;
                                case 'out':
                                    if (stock > 0) showElement = false;
                                    break;
                            }
                        }

                        element.style.display = showElement ? '' : 'none';
                    });

                    // Sort products
                    if (sortValue) {
                        sortProducts(sortValue);
                    }
                }

                function sortProducts(sortBy) {
                    const tableBody = document.getElementById('products-table-body');
                    const gridContainer = document.getElementById('products-grid');

                    if (tableBody) {
                        const rows = Array.from(tableBody.querySelectorAll(
                            '.product-row:not([style*="display: none"])'));
                        rows.sort((a, b) => {
                            switch (sortBy) {
                                case 'name':
                                    return a.dataset.name.localeCompare(b.dataset.name);
                                case 'price':
                                    return parseFloat(b.dataset.price) - parseFloat(a.dataset.price);
                                case 'stock':
                                    return parseInt(b.dataset.stock) - parseInt(a.dataset.stock);
                                default:
                                    return 0;
                            }
                        });

                        rows.forEach(row => tableBody.appendChild(row));
                    }

                    if (gridContainer) {
                        const cards = Array.from(gridContainer.querySelectorAll(
                            '.product-card:not([style*="display: none"])'));
                        cards.sort((a, b) => {
                            switch (sortBy) {
                                case 'name':
                                    return a.dataset.name.localeCompare(b.dataset.name);
                                case 'price':
                                    return parseFloat(b.dataset.price) - parseFloat(a.dataset.price);
                                case 'stock':
                                    return parseInt(b.dataset.stock) - parseInt(a.dataset.stock);
                                default:
                                    return 0;
                            }
                        });

                        cards.forEach(card => gridContainer.appendChild(card));
                    }
                }

                // Event listeners
                if (searchInput) {
                    searchInput.addEventListener('input', filterAndSortProducts);
                }

                if (stockFilter) {
                    stockFilter.addEventListener('change', filterAndSortProducts);
                }

                if (sortSelect) {
                    sortSelect.addEventListener('change', filterAndSortProducts);
                }
            });

            // Export functionality
            function exportCategoryProducts() {
                // Implement export logic here
                alert('سيتم تنفيذ وظيفة تصدير المنتجات قريباً');
            }
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\categories\show.blade.php ENDPATH**/ ?>