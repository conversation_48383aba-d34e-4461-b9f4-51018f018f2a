<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h3 mb-0">تفاصيل فرع <?php echo e($branch->name); ?></h2>
                        <p class="text-muted mb-0"><?php echo e($branch->code); ?> - <?php echo e($branch->is_active ? 'نشط' : 'غير نشط'); ?></p>
                    </div>
                    <div>
                        
                        <a href="<?php echo e(route('admin.branches.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> عودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Overview Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المبيعات</h6>
                                <h3 class="mb-0"><?php echo e(number_format($branch->sales->sum('total_amount'), 2)); ?> ج.م</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المشتريات</h6>
                                <h3 class="mb-0"><?php echo e(number_format($branch->purchases->sum('total_amount'), 2)); ?> ج.م
                                </h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-shopping-cart fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">عدد المخازن</h6>
                                <h3 class="mb-0"><?php echo e($branch->stores->count()); ?></h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-warehouse fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">عدد الموظفين</h6>
                                <h3 class="mb-0"><?php echo e($branch->users->count()); ?></h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Information and Financial Summary -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> معلومات الفرع
                        </h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">الكود:</th>
                                <td><?php echo e($branch->code); ?></td>
                            </tr>
                            <tr>
                                <th>الاسم:</th>
                                <td><?php echo e($branch->name); ?></td>
                            </tr>
                            <tr>
                                <th>العنوان:</th>
                                <td><?php echo e($branch->address ?? '-'); ?></td>
                            </tr>
                            <tr>
                                <th>الهاتف:</th>
                                <td><?php echo e($branch->phone ?? '-'); ?></td>
                            </tr>
                            <tr>
                                <th>البريد الإلكتروني:</th>
                                <td><?php echo e($branch->email ?? '-'); ?></td>
                            </tr>
                            
                            <tr>
                                <th>الحالة:</th>
                                <td>
                                    <span class="badge bg-<?php echo e($branch->is_active ? 'success' : 'danger'); ?>">
                                        <?php echo e($branch->is_active ? 'نشط' : 'غير نشط'); ?>

                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>تاريخ الإنشاء:</th>
                                <td><?php echo e($branch->created_at); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie"></i> الملخص المالي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h6 class="text-muted mb-1">مبيعات اليوم</h6>
                                    <h4 class="text-success mb-3">
                                        <?php echo e(number_format($branch->sales->where('created_at', '>=', now()->startOfDay())->sum('total_amount'), 2)); ?>

                                        ج.م
                                    </h4>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="text-muted mb-1">مشتريات اليوم</h6>
                                <h4 class="text-primary mb-3">
                                    <?php echo e(number_format($branch->purchases->where('created_at', '>=', now()->startOfDay())->sum('total_amount'), 2)); ?>

                                    ج.م
                                </h4>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h6 class="text-muted mb-1">مبيعات الشهر</h6>
                                    <h4 class="text-success mb-3">
                                        <?php echo e(number_format($branch->sales->where('created_at', '>=', now()->startOfMonth())->sum('total_amount'), 2)); ?>

                                        ج.م
                                    </h4>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="text-muted mb-1">مشتريات الشهر</h6>
                                <h4 class="text-primary mb-3">
                                    <?php echo e(number_format($branch->purchases->where('created_at', '>=', now()->startOfMonth())->sum('total_amount'), 2)); ?>

                                    ج.م
                                </h4>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <h6 class="text-muted mb-1">صافي الربح (إجمالي)</h6>
                            <h3
                                class="text-<?php echo e($branch->sales->sum('total_amount') - $branch->purchases->sum('total_amount') >= 0 ? 'success' : 'danger'); ?>">
                                <?php echo e(number_format($branch->sales->sum('total_amount') - $branch->purchases->sum('total_amount'), 2)); ?>

                                ج.م
                            </h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stores and Users -->
        <div class="row mb-4">
            

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users"></i> الموظفين (<?php echo e($branch->users->count()); ?>)
                        </h5>
                        <a href="<?php echo e(route('admin.settings.users.create', ['branch_id' => $branch->id])); ?>"
                            class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة موظف
                        </a>
                    </div>
                    <div class="card-body p-0">
                        <?php if($branch->users->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الاسم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الدور</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $branch->users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($user->name); ?></td>
                                                <td><?php echo e($user->email); ?></td>
                                                <td>
                                                    <span
                                                        class="badge bg-info"><?php echo e($user->role->name ?? 'غير محدد'); ?></span>
                                                </td>
                                                <td>
                                                    <a href="<?php echo e(route('admin.settings.users.show', $user)); ?>"
                                                        class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا يوجد موظفين في هذا الفرع</p>
                                <a href="<?php echo e(route('admin.settings.users.create', ['branch_id' => $branch->id])); ?>"
                                    class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة موظف جديد
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart"></i> أحدث المبيعات
                        </h5>
                        <a href="<?php echo e(route('admin.sales.index', ['branch_id' => $branch->id])); ?>"
                            class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                    <div class="card-body p-0">
                        <?php if($branch->sales->take(5)->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>المبلغ</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $branch->sales->sortByDesc('created_at')->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <a href="<?php echo e(route('admin.sales.show', $sale)); ?>"
                                                        class="text-decoration-none">
                                                        #<?php echo e($sale->id); ?>

                                                    </a>
                                                </td>
                                                <td><?php echo e($sale->customer->name ?? 'عميل نقدي'); ?></td>
                                                <td><?php echo e(number_format($sale->total_amount, 2)); ?> ج.م</td>
                                                <td><?php echo e($sale->created_at->format('Y-m-d')); ?></td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد مبيعات حديثة</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-truck"></i> أحدث المشتريات
                        </h5>
                        <a href="<?php echo e(route('admin.purchases.index', ['branch_id' => $branch->id])); ?>"
                            class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                    <div class="card-body p-0">
                        <?php if($branch->purchases->take(5)->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>المورد</th>
                                            <th>المبلغ</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $branch->purchases->sortByDesc('created_at')->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $purchase): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <a href="<?php echo e(route('admin.purchases.show', $purchase)); ?>"
                                                        class="text-decoration-none">
                                                        #<?php echo e($purchase->id); ?>

                                                    </a>
                                                </td>
                                                <td><?php echo e($purchase->supplier->name ?? '-'); ?></td>
                                                <td><?php echo e(number_format($purchase->total_amount, 2)); ?> ج.م</td>
                                                <td><?php echo e($purchase->created_at->format('Y-m-d')); ?></td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-truck fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد مشتريات حديثة</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\admin\branches\show.blade.php ENDPATH**/ ?>