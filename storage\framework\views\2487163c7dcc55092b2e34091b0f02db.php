<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-exchange-alt text-primary"></i> <?php echo e(__('تفاصيل عملية النقل')); ?>

                    #<?php echo e($transfer->transfer_number); ?>

                </h2>
                <p class="text-muted small mb-0">تفاصيل عملية النقل المباشر</p>
            </div>
            <div>
                <a href="<?php echo e(user_route('transfers.direct.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> نقل جديد
                </a>
                <a href="<?php echo e(user_route('transfers.history')); ?>" class="btn btn-secondary">
                    <i class="fas fa-history"></i> سجل النقل
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="container-fluid">
        <!-- Transfer Info -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> معلومات النقل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">رقم النقل:</label>
                                    <span class="badge bg-primary fs-6"><?php echo e($transfer->transfer_number); ?></span>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">نوع النقل:</label>
                                    <span
                                        class="badge bg-info"><?php echo e(ucfirst(str_replace('_', ' إلى ', $transfer->type))); ?></span>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">الحالة:</label>
                                    <?php if($transfer->status === 'completed'): ?>
                                        <span class="badge bg-success">مكتمل</span>
                                    <?php elseif($transfer->status === 'pending'): ?>
                                        <span class="badge bg-warning">معلق</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">ملغي</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">تاريخ الطلب:</label>
                                    <p class="mb-0"><?php echo e($transfer->requested_at->format('Y-m-d H:i')); ?></p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">تاريخ التنفيذ:</label>
                                    <p class="mb-0">
                                        <?php echo e($transfer->received_at ? $transfer->received_at->format('Y-m-d H:i') : '-'); ?>

                                    </p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">نفذ بواسطة:</label>
                                    <p class="mb-0"><?php echo e($transfer->requestedBy->name); ?></p>
                                </div>
                            </div>
                        </div>

                        <?php if($transfer->notes): ?>
                            <div class="mt-3">
                                <label class="form-label fw-bold">ملاحظات:</label>
                                <p class="mb-0 text-muted"><?php echo e($transfer->notes); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-route"></i> مسار النقل
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Source -->
                        <div class="mb-3">
                            <label class="form-label fw-bold text-danger">من:</label>
                            <div class="d-flex align-items-center">
                                <?php if($transfer->source_type === 'branch'): ?>
                                    <i class="fas fa-building text-primary me-2"></i>
                                    <span><?php echo e($transfer->sourceBranch->name ?? 'غير محدد'); ?></span>
                                <?php else: ?>
                                    <i class="fas fa-store text-success me-2"></i>
                                    <span><?php echo e($transfer->sourceStore->name ?? 'غير محدد'); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Arrow -->
                        <div class="text-center mb-3">
                            <i class="fas fa-arrow-down fa-2x text-muted"></i>
                        </div>

                        <!-- Destination -->
                        <div class="mb-3">
                            <label class="form-label fw-bold text-success">إلى:</label>
                            <div class="d-flex align-items-center">
                                <?php if($transfer->destination_type === 'branch'): ?>
                                    <i class="fas fa-building text-primary me-2"></i>
                                    <span><?php echo e($transfer->destinationBranch->name ?? 'غير محدد'); ?></span>
                                <?php else: ?>
                                    <i class="fas fa-store text-success me-2"></i>
                                    <span><?php echo e($transfer->destinationStore->name ?? 'غير محدد'); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfer Items -->
        <div class="card shadow-sm">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-boxes"></i> المنتجات المنقولة
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية المطلوبة</th>
                                <th>الكمية المنقولة</th>
                                <th><?php echo e(auth()->user()->isSeller() ? 'سعر البيع' : 'سعر الوحدة'); ?></th>
                                <th>إجمالي القيمة</th>
                                <th>الحالة</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $transfer->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <?php
                                    // Get the appropriate price based on user role
                                    $displayPrice = $item->unit_cost ?? 0;
                                    if (auth()->user()->isSeller()) {
                                        // For sellers, try to get sale price from source branch inventory
                                        if ($transfer->source_type === 'branch') {
                                            $branchInventory = $item->product->branchInventories
                                                ->where('branch_id', $transfer->source_id)
                                                ->first();
                                            if ($branchInventory && $branchInventory->sale_price_1) {
                                                $displayPrice = $branchInventory->sale_price_1;
                                            }
                                        }
                                    }
                                    $quantity = $item->received_quantity ?? ($item->approved_quantity ?? 0);
                                    $totalValue = $quantity * $displayPrice;
                                ?>
                                <tr>
                                    <td class="fw-bold"><?php echo e($item->product->name); ?></td>
                                    <td class="text-center"><?php echo e(number_format($item->requested_quantity, 2)); ?></td>
                                    <td class="text-center">
                                        <span class="badge bg-success"><?php echo e(number_format($quantity, 2)); ?></span>
                                    </td>
                                    <td><?php echo e(number_format($displayPrice, 2)); ?> ج.م</td>
                                    <td class="fw-bold">
                                        <?php echo e(number_format($totalValue, 2)); ?>

                                        ج.م</td>
                                    <td>
                                        <?php if($item->received_quantity !== null): ?>
                                            <?php if($item->received_quantity == $item->requested_quantity): ?>
                                                <span class="badge bg-success">مطابق</span>
                                            <?php elseif($item->received_quantity < $item->requested_quantity): ?>
                                                <span class="badge bg-warning">نقص</span>
                                            <?php else: ?>
                                                <span class="badge bg-info">زيادة</span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">معلق</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($item->notes ?? '-'); ?></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد منتجات في هذا النقل</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                        <?php if($transfer->items->count() > 0): ?>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="4" class="text-end">إجمالي القيمة:</th>
                                    <th>
                                        <?php
                                            $totalValue = 0;
                                            foreach ($transfer->items as $item) {
                                                $displayPrice = $item->unit_cost ?? 0;
                                                if (auth()->user()->isSeller()) {
                                                    // For sellers, try to get sale price from source branch inventory
                                                    if ($transfer->source_type === 'branch') {
                                                        $branchInventory = $item->product->branchInventories
                                                            ->where('branch_id', $transfer->source_id)
                                                            ->first();
                                                        if ($branchInventory && $branchInventory->sale_price_1) {
                                                            $displayPrice = $branchInventory->sale_price_1;
                                                        }
                                                    }
                                                }
                                                $quantity = $item->received_quantity ?? ($item->approved_quantity ?? 0);
                                                $totalValue += $quantity * $displayPrice;
                                            }
                                        ?>
                                        <?php echo e(number_format($totalValue, 2)); ?> ج.م
                                    </th>
                                    <th colspan="2"></th>
                                </tr>
                            </tfoot>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        </div>

        <!-- Timeline -->
        <?php if($transfer->status === 'completed'): ?>
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock"></i> الجدول الزمني
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">طلب النقل</h6>
                                <p class="timeline-text"><?php echo e($transfer->requested_at->format('Y-m-d H:i')); ?></p>
                                <small class="text-muted">بواسطة: <?php echo e($transfer->requestedBy->name); ?></small>
                            </div>
                        </div>

                        <?php if($transfer->approved_at): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">تم التنفيذ</h6>
                                    <p class="timeline-text"><?php echo e($transfer->approved_at->format('Y-m-d H:i')); ?></p>
                                    <small class="text-muted">بواسطة:
                                        <?php echo e($transfer->approvedBy->name ?? 'النظام'); ?></small>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($transfer->received_at): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">تم الاستلام</h6>
                                    <p class="timeline-text"><?php echo e($transfer->received_at->format('Y-m-d H:i')); ?></p>
                                    <small class="text-muted">بواسطة:
                                        <?php echo e($transfer->receivedBy->name ?? 'النظام'); ?></small>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <?php $__env->startPush('styles'); ?>
        <style>
            .timeline {
                position: relative;
                padding-left: 30px;
            }

            .timeline::before {
                content: '';
                position: absolute;
                left: 15px;
                top: 0;
                bottom: 0;
                width: 2px;
                background: #dee2e6;
            }

            .timeline-item {
                position: relative;
                margin-bottom: 30px;
            }

            .timeline-marker {
                position: absolute;
                left: -22px;
                top: 0;
                width: 15px;
                height: 15px;
                border-radius: 50%;
                border: 3px solid #fff;
                box-shadow: 0 0 0 2px #dee2e6;
            }

            .timeline-title {
                margin-bottom: 5px;
                font-weight: 600;
            }

            .timeline-text {
                margin-bottom: 5px;
                color: #6c757d;
            }
        </style>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\transfers\show.blade.php ENDPATH**/ ?>