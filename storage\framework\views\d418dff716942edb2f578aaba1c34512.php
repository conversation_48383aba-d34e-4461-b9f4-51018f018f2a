<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-plus text-primary"></i> <?php echo e(__('إضافة منتجات للفرع')); ?>

                </h2>
                <p class="text-muted small mb-0">إضافة منتجات جديدة لمخزون فرع: <?php echo e($branch->name); ?></p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(user_route('branches.sales-inventory', $branch)); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للمخزون
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="container-fluid px-4">


        <!-- Display Validation Errors -->
        <?php if($errors->any()): ?>
            <div class="alert alert-danger mb-4">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>حدثت الأخطاء التالية:</h6>
                <ul class="mb-0">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- Display Success Messages -->
        <?php if(session('success')): ?>
            <div class="alert alert-success mb-4">
                <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <!-- Breadcrumb -->
        <div class="row align-items-center mb-4">
            <div class="col-md-8">
                <nav aria-label="breadcrumb" class="d-flex justify-content-start">
                    <ol class="breadcrumb mb-0 bg-light px-3 py-2 rounded">
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(user_route('dashboard')); ?>" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(user_route('branches.index')); ?>" class="text-decoration-none">
                                <i class="fas fa-building me-1"></i>الفروع
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(user_route('branches.show', $branch)); ?>" class="text-decoration-none">
                                <i class="fas fa-building me-1"></i><?php echo e(Str::limit($branch->name, 20)); ?>

                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(user_route('branches.sales-inventory', $branch)); ?>"
                                class="text-decoration-none">
                                <i class="fas fa-boxes me-1"></i>المخزون
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-plus me-1"></i>إضافة منتجات
                        </li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge bg-info fs-6">
                    <i class="fas fa-building me-1"></i><?php echo e($branch->name); ?>

                </span>
            </div>
        </div>

        <?php if($products->count() > 0): ?>
            <!-- Add Products Form -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow border-0">
                        <div class="card-header bg-gradient-primary text-white py-3">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-plus fa-lg me-3"></i>
                                    <h5 class="mb-0 fw-bold">اختيار المنتجات للإضافة</h5>
                                </div>
                                <div class="text-white">
                                    <span id="selectedCount" class="badge bg-warning text-dark fs-6">0 منتج محدد</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-4">
                            <form action="<?php echo e(user_route('branches.store-product', $branch)); ?>" method="POST"
                                id="addProductsForm">
                                <?php echo csrf_field(); ?>

                                <!-- Search and Filter Controls -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-search text-muted"></i>
                                            </span>
                                            <input type="text" class="form-control" id="productSearch"
                                                placeholder="البحث في المنتجات...">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <select class="form-select" id="categoryFilter">
                                            <option value="">جميع الفئات</option>
                                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-outline-secondary w-100"
                                            onclick="clearFilters()">
                                            <i class="fas fa-times"></i> مسح
                                        </button>
                                    </div>
                                </div>

                                <!-- Products Grid -->
                                <div class="row" id="productsGrid">
                                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-lg-4 col-md-6 mb-4 product-item"
                                            data-name="<?php echo e(strtolower($product->name)); ?>"
                                            data-category="<?php echo e($product->category_id ?? ''); ?>">
                                            <div class="card h-100 border-0 shadow-sm">
                                                <div class="card-body p-3">
                                                    <!-- Product Header -->
                                                    <div class="d-flex align-items-start mb-3">
                                                        <div class="form-check me-3">
                                                            <input class="form-check-input product-checkbox"
                                                                type="checkbox" id="product<?php echo e($product->id); ?>"
                                                                data-product-id="<?php echo e($product->id); ?>"
                                                                onchange="toggleProductFields(this)">
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <label class="form-check-label fw-bold text-primary"
                                                                for="product<?php echo e($product->id); ?>">
                                                                <?php echo e($product->name); ?>

                                                            </label>
                                                            <p class="text-muted small mb-1">
                                                                <i
                                                                    class="fas fa-tag me-1"></i><?php echo e($product->category->name ?? 'بدون فئة'); ?>

                                                            </p>
                                                            <?php if($product->sku): ?>
                                                                <p class="text-muted small mb-0">
                                                                    <i
                                                                        class="fas fa-barcode me-1"></i><?php echo e($product->sku); ?>

                                                                </p>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>

                                                    <!-- Product Fields -->
                                                    <div class="product-fields" id="fields<?php echo e($product->id); ?>"
                                                        style="display: none;">
                                                        <hr>
                                                        <div class="row g-2">
                                                            <!-- Quantity -->
                                                            <div class="col-6">
                                                                <label class="form-label small fw-bold">الكمية <span
                                                                        class="text-danger">*</span></label>
                                                                <input type="number" step="0.01" min="0"
                                                                    class="form-control form-control-sm"
                                                                    name="products[<?php echo e($product->id); ?>][quantity]"
                                                                    placeholder="0" disabled>
                                                                <input type="hidden"
                                                                    name="products[<?php echo e($product->id); ?>][product_id]"
                                                                    value="<?php echo e($product->id); ?>" disabled>
                                                            </div>
                                                            <!-- Cost Price -->
                                                            <div class="col-6">
                                                                <label class="form-label small fw-bold">سعر التكلفة
                                                                    <span class="text-danger">*</span></label>
                                                                <input type="number" step="0.01" min="0"
                                                                    class="form-control form-control-sm"
                                                                    name="products[<?php echo e($product->id); ?>][cost_price]"
                                                                    value="<?php echo e($product->price ?? ''); ?>"
                                                                    placeholder="0.00" disabled>
                                                            </div>
                                                            <!-- Sale Price 1 -->
                                                            <div class="col-6">
                                                                <label class="form-label small fw-bold">سعر البيع
                                                                    1</label>
                                                                <input type="number" step="0.01" min="0"
                                                                    class="form-control form-control-sm"
                                                                    name="products[<?php echo e($product->id); ?>][sale_price_1]"
                                                                    value="<?php echo e($product->selling_price ?? ''); ?>"
                                                                    placeholder="0.00" disabled>
                                                            </div>
                                                            <!-- Sale Price 2 -->
                                                            <div class="col-6">
                                                                <label class="form-label small fw-bold">سعر البيع
                                                                    2</label>
                                                                <input type="number" step="0.01" min="0"
                                                                    class="form-control form-control-sm"
                                                                    name="products[<?php echo e($product->id); ?>][sale_price_2]"
                                                                    placeholder="0.00" disabled>
                                                            </div>
                                                            <!-- Sale Price 3 -->
                                                            <div class="col-12">
                                                                <label class="form-label small fw-bold">سعر البيع
                                                                    3</label>
                                                                <input type="number" step="0.01" min="0"
                                                                    class="form-control form-control-sm"
                                                                    name="products[<?php echo e($product->id); ?>][sale_price_3]"
                                                                    placeholder="0.00" disabled>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>

                                <!-- Submit Section -->
                                <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                                    <div class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        اختر المنتجات وحدد الكميات والأسعار المطلوبة
                                    </div>
                                    <div class="d-flex gap-2">
                                        <a href="<?php echo e(user_route('branches.sales-inventory', $branch)); ?>"
                                            class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>إلغاء
                                        </a>
                                        <button type="submit" class="btn btn-success" id="submitBtn" disabled>
                                            <i class="fas fa-save me-2"></i>إضافة المنتجات المحددة
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- No Products Available -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow border-0">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-box-open fa-4x text-muted mb-4"></i>
                            <h4 class="text-muted mb-3">جميع المنتجات موجودة في المخزون</h4>
                            <p class="text-muted mb-4">
                                جميع المنتجات النشطة موجودة بالفعل في مخزون هذا الفرع.
                                <br>يمكنك إنشاء منتجات جديدة أو إدارة المخزون الحالي.
                            </p>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="<?php echo e(user_route('products.create')); ?>" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>إنشاء منتج جديد
                                </a>
                                <a href="<?php echo e(user_route('branches.sales-inventory', $branch)); ?>"
                                    class="btn btn-outline-primary">
                                    <i class="fas fa-warehouse me-2"></i>عرض المخزون الحالي
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>

<?php $__env->startPush('styles'); ?>
    <style>
        .bg-gradient-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .product-item {
            transition: transform 0.2s ease-in-out;
        }

        .product-item:hover {
            transform: translateY(-2px);
        }

        .form-check-input:checked~.card {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .product-fields {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
<?php $__env->stopPush(); ?>

<!-- Inline Script for Global Functions -->
<script>
    // Global functions - defined immediately
    window.toggleProductFields = function(checkbox) {
        console.log('toggleProductFields called!', checkbox);

        const productId = checkbox.getAttribute('data-product-id');
        console.log('Product ID:', productId);

        const fields = document.getElementById('fields' + productId);
        console.log('Fields element:', fields);

        if (!fields) {
            console.error('Fields element not found for ID: fields' + productId);
            return;
        }

        if (checkbox.checked) {
            console.log('Showing fields...');
            fields.style.display = 'block';
            // Enable all inputs and set required attributes for quantity and cost price
            fields.querySelectorAll('input').forEach(function(input) {
                input.disabled = false;
                if (input.name && (input.name.includes('[quantity]') || input.name.includes(
                        '[cost_price]'))) {
                    input.required = true;
                }
            });
        } else {
            console.log('Hiding fields...');
            fields.style.display = 'none';
            // Disable all inputs, remove required and clear values
            fields.querySelectorAll('input').forEach(function(input) {
                input.disabled = true;
                input.required = false;
                if (input.type === 'number') {
                    input.value = '';
                }
            });
        }

        window.updateSelectedCount();
    };

    window.updateSelectedCount = function() {
        const checkedCount = document.querySelectorAll('.product-checkbox:checked').length;
        const selectedCount = document.getElementById('selectedCount');
        const submitBtn = document.getElementById('submitBtn');

        if (!selectedCount || !submitBtn) {
            console.log('selectedCount or submitBtn not found');
            return;
        }

        if (checkedCount === 0) {
            selectedCount.textContent = '0 منتج محدد';
            selectedCount.className = 'badge bg-warning text-dark fs-6';
            submitBtn.disabled = true;
        } else {
            selectedCount.textContent = checkedCount + ' منتج محدد';
            selectedCount.className = 'badge bg-success text-white fs-6';
            submitBtn.disabled = false;
        }
    };
</script>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            // Set up checkbox event listeners (backup)
            document.querySelectorAll('.product-checkbox').forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    toggleProductFields(this);
                });
            });

            // Get elements for search and filter
            const productSearch = document.getElementById('productSearch');
            const categoryFilter = document.getElementById('categoryFilter');
            const productItems = document.querySelectorAll('.product-item');

            // Search functionality
            if (productSearch) {
                productSearch.addEventListener('input', function() {
                    filterProducts();
                });
            }

            // Category filter
            if (categoryFilter) {
                categoryFilter.addEventListener('change', function() {
                    filterProducts();
                });
            }

            // Filter products based on search and category
            function filterProducts() {
                const searchTerm = productSearch ? productSearch.value.toLowerCase() : '';
                const selectedCategory = categoryFilter ? categoryFilter.value : '';

                productItems.forEach(item => {
                    const productName = item.dataset.name || '';
                    const productCategory = item.dataset.category || '';

                    const matchesSearch = !searchTerm || productName.includes(searchTerm);
                    const matchesCategory = !selectedCategory || productCategory === selectedCategory;

                    if (matchesSearch && matchesCategory) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            }

            // Clear filters
            window.clearFilters = function() {
                if (productSearch) productSearch.value = '';
                if (categoryFilter) categoryFilter.value = '';
                filterProducts();
            };

            // Form validation and cleanup
            const form = document.getElementById('addProductsForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');

                    if (checkedBoxes.length === 0) {
                        e.preventDefault();
                        alert('يرجى اختيار منتج واحد على الأقل');
                        return false;
                    }

                    // Validate checked products have required fields
                    let hasValidationErrors = false;
                    let errorMessages = [];

                    checkedBoxes.forEach(function(checkbox) {
                        const productId = checkbox.getAttribute('data-product-id');
                        const productLabel = checkbox.closest('.card').querySelector(
                            '.form-check-label');
                        const productName = productLabel ? productLabel.textContent.trim() :
                            `المنتج رقم ${productId}`;

                        const quantityInput = form.querySelector(
                            `input[name="products[${productId}][quantity]"]:not([disabled])`);
                        const costPriceInput = form.querySelector(
                            `input[name="products[${productId}][cost_price]"]:not([disabled])`);

                        // Check if inputs exist and have valid values
                        if (!quantityInput || !quantityInput.value || parseFloat(quantityInput
                                .value) <= 0) {
                            hasValidationErrors = true;
                            errorMessages.push(`يرجى إدخال كمية صحيحة لـ ${productName}`);
                        }

                        if (!costPriceInput || !costPriceInput.value || parseFloat(costPriceInput
                                .value) <= 0) {
                            hasValidationErrors = true;
                            errorMessages.push(`يرجى إدخال سعر تكلفة صحيح لـ ${productName}`);
                        }
                    });

                    if (hasValidationErrors) {
                        e.preventDefault();
                        alert('حدثت الأخطاء التالية:\n\n' + errorMessages.join('\n'));
                        return false;
                    }

                    // Show loading state
                    const submitBtn = document.getElementById('submitBtn');
                    if (submitBtn) {
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';
                        submitBtn.disabled = true;
                    }

                    // Allow normal form submission to proceed
                    return true;
                });
            }

            // Auto-calculate sale prices based on cost price (simplified)
            document.addEventListener('input', function(e) {
                if (e.target.name && e.target.name.includes('[cost_price]')) {
                    const costPrice = parseFloat(e.target.value) || 0;
                    const productIdMatch = e.target.name.match(/\[(\d+)\]/);

                    if (productIdMatch) {
                        const productId = productIdMatch[1];
                        const salePrice1Input = document.querySelector(
                            `input[name="products[${productId}][sale_price_1]"]`);

                        if (salePrice1Input && costPrice > 0 && !salePrice1Input.value) {
                            // Suggest 30% markup
                            const suggestedPrice = (costPrice * 1.3).toFixed(2);
                            salePrice1Input.placeholder = `مقترح: ${suggestedPrice}`;
                        }
                    }
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\my-pos-app\resources\views\branches\add-product.blade.php ENDPATH**/ ?>