<?php $__env->startSection('title', 'تعديل مرتجع الشراء'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تعديل مرتجع الشراء - <?php echo e($purchaseReturn->return_number); ?></h3>
                    <div class="card-tools">
                        <a href="<?php echo e(route('admin.purchase-returns.show', $purchaseReturn)); ?>" class="btn btn-info">
                            <i class="fas fa-eye"></i> عرض التفاصيل
                        </a>
                        <a href="<?php echo e(route('admin.purchase-returns.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>

                <form method="POST" action="<?php echo e(route('admin.purchase-returns.update', $purchaseReturn)); ?>" id="editReturnForm">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <div class="card-body">
                        <!-- Purchase Information (Read-only) -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label class="form-label">عملية الشراء</label>
                                <input type="text" class="form-control" value="#<?php echo e($purchaseReturn->purchase_id); ?> - <?php echo e($purchaseReturn->supplier->name); ?>" readonly>
                            </div>
                            <div class="col-md-4">
                                <label for="return_date" class="form-label">تاريخ المرتجع <span class="text-danger">*</span></label>
                                <input type="date" name="return_date" id="return_date" class="form-control" value="<?php echo e(old('return_date', $purchaseReturn->return_date->format('Y-m-d'))); ?>" required>
                                <?php $__errorArgs = ['return_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">الحالة</label>
                                <input type="text" class="form-control" value="<?php echo e($purchaseReturn->status); ?>" readonly>
                            </div>
                        </div>

                        <!-- Return Details -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="return_type" class="form-label">نوع المرتجع <span class="text-danger">*</span></label>
                                <select name="return_type" id="return_type" class="form-select" required>
                                    <option value="partial" <?php echo e(old('return_type', $purchaseReturn->return_type) == 'partial' ? 'selected' : ''); ?>>مرتجع جزئي</option>
                                    <option value="full" <?php echo e(old('return_type', $purchaseReturn->return_type) == 'full' ? 'selected' : ''); ?>>مرتجع كامل</option>
                                </select>
                                <?php $__errorArgs = ['return_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-4">
                                <label for="refund_amount" class="form-label">مبلغ الاسترداد (ج.م) <span class="text-danger">*</span></label>
                                <input type="number" name="refund_amount" id="refund_amount" class="form-control" step="0.01" min="0" value="<?php echo e(old('refund_amount', $purchaseReturn->refund_amount)); ?>" required>
                                <?php $__errorArgs = ['refund_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-4">
                                <label for="reason" class="form-label">سبب المرتجع <span class="text-danger">*</span></label>
                                <select name="reason" id="reason" class="form-select" required>
                                    <option value="">اختر السبب...</option>
                                    <option value="تالف" <?php echo e(old('reason', $purchaseReturn->reason) == 'تالف' ? 'selected' : ''); ?>>تالف</option>
                                    <option value="منتهي الصلاحية" <?php echo e(old('reason', $purchaseReturn->reason) == 'منتهي الصلاحية' ? 'selected' : ''); ?>>منتهي الصلاحية</option>
                                    <option value="خطأ في الطلب" <?php echo e(old('reason', $purchaseReturn->reason) == 'خطأ في الطلب' ? 'selected' : ''); ?>>خطأ في الطلب</option>
                                    <option value="عيب في التصنيع" <?php echo e(old('reason', $purchaseReturn->reason) == 'عيب في التصنيع' ? 'selected' : ''); ?>>عيب في التصنيع</option>
                                    <option value="أخرى" <?php echo e(old('reason', $purchaseReturn->reason) == 'أخرى' ? 'selected' : ''); ?>>أخرى</option>
                                </select>
                                <?php $__errorArgs = ['reason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="ملاحظات إضافية..."><?php echo e(old('notes', $purchaseReturn->notes)); ?></textarea>
                                <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Return Items -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">أصناف المرتجع</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="itemsTable">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكمية الأصلية</th>
                                                <th>الكمية المتاحة للإرجاع</th>
                                                <th>الكمية المرتجعة</th>
                                                <th>سعر التكلفة</th>
                                                <th>الإجمالي</th>
                                                <th>حالة المنتج</th>
                                                <th>ملاحظات</th>
                                                <th>إجراء</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $purchaseReturn->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($item->product->name); ?></td>
                                                    <td><?php echo e($item->original_quantity); ?></td>
                                                    <td><?php echo e($item->available_quantity); ?></td>
                                                    <td>
                                                        <input type="number" name="items[<?php echo e($index); ?>][quantity_returned]" 
                                                               class="form-control quantity-input" 
                                                               step="0.01" min="0.01" max="<?php echo e($item->available_quantity); ?>" 
                                                               value="<?php echo e(old('items.'.$index.'.quantity_returned', $item->quantity_returned)); ?>" 
                                                               data-cost="<?php echo e($item->cost_price); ?>" required>
                                                        <input type="hidden" name="items[<?php echo e($index); ?>][id]" value="<?php echo e($item->id); ?>">
                                                        <input type="hidden" name="items[<?php echo e($index); ?>][purchase_item_id]" value="<?php echo e($item->purchase_item_id); ?>">
                                                    </td>
                                                    <td><?php echo e(number_format($item->cost_price, 2)); ?></td>
                                                    <td class="item-total"><?php echo e(number_format($item->total_cost, 2)); ?></td>
                                                    <td>
                                                        <select name="items[<?php echo e($index); ?>][condition]" class="form-select">
                                                            <option value="good" <?php echo e(old('items.'.$index.'.condition', $item->condition) == 'good' ? 'selected' : ''); ?>>جيد</option>
                                                            <option value="damaged" <?php echo e(old('items.'.$index.'.condition', $item->condition) == 'damaged' ? 'selected' : ''); ?>>تالف</option>
                                                            <option value="expired" <?php echo e(old('items.'.$index.'.condition', $item->condition) == 'expired' ? 'selected' : ''); ?>>منتهي الصلاحية</option>
                                                            <option value="defective" <?php echo e(old('items.'.$index.'.condition', $item->condition) == 'defective' ? 'selected' : ''); ?>>معيب</option>
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <input type="text" name="items[<?php echo e($index); ?>][item_notes]" 
                                                               class="form-control" placeholder="ملاحظات..." 
                                                               value="<?php echo e(old('items.'.$index.'.item_notes', $item->item_notes)); ?>">
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-danger remove-item">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <strong>إجمالي المبلغ المرتجع: <span id="totalReturnAmount"><?php echo e(number_format($purchaseReturn->total_amount, 2)); ?></span> ج.م</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التعديلات
                        </button>
                        <a href="<?php echo e(route('admin.purchase-returns.show', $purchaseReturn)); ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Calculate item total when quantity changes
    $('.quantity-input').on('input', function() {
        const row = $(this).closest('tr');
        const quantity = parseFloat($(this).val()) || 0;
        const costPrice = parseFloat($(this).data('cost')) || 0;
        const total = quantity * costPrice;
        
        row.find('.item-total').text(total.toFixed(2));
        calculateTotal();
    });

    // Remove item
    $('.remove-item').click(function() {
        if (confirm('هل أنت متأكد من حذف هذا الصنف؟')) {
            $(this).closest('tr').remove();
            calculateTotal();
        }
    });

    function calculateTotal() {
        let total = 0;
        $('.item-total').each(function() {
            const value = parseFloat($(this).text().replace(/,/g, '')) || 0;
            total += value;
        });
        
        $('#totalReturnAmount').text(total.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
        $('#refund_amount').val(total.toFixed(2));
    }

    // Form validation
    $('#editReturnForm').submit(function(e) {
        const itemsCount = $('#itemsTable tbody tr').length;
        if (itemsCount === 0) {
            e.preventDefault();
            alert('يجب إضافة صنف واحد على الأقل للمرتجع');
            return false;
        }

        // Validate quantities
        let isValid = true;
        $('.quantity-input').each(function() {
            const quantity = parseFloat($(this).val()) || 0;
            const max = parseFloat($(this).attr('max')) || 0;
            
            if (quantity <= 0) {
                isValid = false;
                $(this).addClass('is-invalid');
            } else if (quantity > max) {
                isValid = false;
                $(this).addClass('is-invalid');
                alert('الكمية المرتجعة تتجاوز الكمية المتاحة للإرجاع');
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my-pos-app\resources\views\admin\purchase-returns\edit.blade.php ENDPATH**/ ?>