<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h3 mb-0">إدارة الفروع</h2>
                        <p class="text-muted mb-0">إدارة وتتبع جميع فروع الشركة</p>
                    </div>
                    
                </div>
            </div>
        </div>

        <!-- Branch Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي الفروع</h6>
                                <h3 class="mb-0"><?php echo e($branches->count()); ?></h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-building fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">الفروع النشطة</h6>
                                <h3 class="mb-0"><?php echo e($branches->where('is_active', true)->count()); ?></h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المخازن</h6>
                                <h3 class="mb-0">
                                    <?php echo e($branches->sum(function ($branch) {return $branch->stores->count();})); ?></h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-warehouse fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي الموظفين</h6>
                                <h3 class="mb-0">
                                    <?php echo e($branches->sum(function ($branch) {return $branch->users->count();})); ?></h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" action="<?php echo e(route('admin.branches.index')); ?>">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                        value="<?php echo e(request('search')); ?>" placeholder="البحث بالاسم أو الكود...">
                                </div>
                                <div class="col-md-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">جميع الحالات</option>
                                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>نشط
                                        </option>
                                        <option value="inactive"
                                            <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>غير نشط</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="sort" class="form-label">ترتيب حسب</label>
                                    <select class="form-select" id="sort" name="sort">
                                        <option value="name" <?php echo e(request('sort') == 'name' ? 'selected' : ''); ?>>الاسم
                                        </option>
                                        <option value="created_at"
                                            <?php echo e(request('sort') == 'created_at' ? 'selected' : ''); ?>>تاريخ الإنشاء
                                        </option>
                                        <option value="sales" <?php echo e(request('sort') == 'sales' ? 'selected' : ''); ?>>
                                            المبيعات</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branches Grid -->
        <div class="row">
            <?php $__empty_1 = true; $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card h-100 <?php echo e(!$branch->is_active ? 'border-danger' : ''); ?>">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0"><?php echo e($branch->name); ?></h6>
                                <small class="text-muted"><?php echo e($branch->code); ?></small>
                            </div>
                            <div>
                                <span class="badge bg-<?php echo e($branch->is_active ? 'success' : 'danger'); ?>">
                                    <?php echo e($branch->is_active ? 'نشط' : 'غير نشط'); ?>

                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Branch Info -->
                            <div class="mb-3">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="border-end">
                                            <h6 class="text-primary mb-0"><?php echo e($branch->stores->count()); ?></h6>
                                            <small class="text-muted">مخازن</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="border-end">
                                            <h6 class="text-success mb-0"><?php echo e($branch->users->count()); ?></h6>
                                            <small class="text-muted">موظفين</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <h6 class="text-info mb-0"><?php echo e($branch->sales->count()); ?></h6>
                                        <small class="text-muted">مبيعات</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Info -->
                            <div class="mb-3">
                                <?php if($branch->address): ?>
                                    <div class="d-flex align-items-center mb-1">
                                        <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                        <small class="text-muted"><?php echo e(Str::limit($branch->address, 30)); ?></small>
                                    </div>
                                <?php endif; ?>
                                <?php if($branch->phone): ?>
                                    <div class="d-flex align-items-center mb-1">
                                        <i class="fas fa-phone text-muted me-2"></i>
                                        <small class="text-muted"><?php echo e($branch->phone); ?></small>
                                    </div>
                                <?php endif; ?>
                                <?php if($branch->email): ?>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-envelope text-muted me-2"></i>
                                        <small class="text-muted"><?php echo e($branch->email); ?></small>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Financial Summary -->
                            <div class="mb-3">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="text-center p-2 bg-light rounded">
                                            <small class="text-muted d-block">مبيعات الشهر</small>
                                            <strong
                                                class="text-success"><?php echo e(number_format($branch->sales->where('created_at', '>=', now()->startOfMonth())->sum('total_amount'), 2)); ?>

                                            </strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <a href="<?php echo e(route('admin.branches.show', $branch)); ?>"
                                    class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                                <a href="<?php echo e(route('admin.branches.edit', $branch)); ?>"
                                    class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <button type="button" class="btn btn-outline-info btn-sm" data-bs-toggle="modal"
                                    data-bs-target="#analyticsModal<?php echo e($branch->id); ?>">
                                    <i class="fas fa-chart-bar"></i> إحصائيات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد فروع</h5>
                            <p class="text-muted">ابدأ بإضافة فرع جديد لإدارة أعمالك</p>
                            
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Analytics Modals -->
        <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="modal fade" id="analyticsModal<?php echo e($branch->id); ?>" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إحصائيات فرع <?php echo e($branch->name); ?></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-primary text-white mb-3">
                                        <div class="card-body text-center">
                                            <h4><?php echo e(number_format($branch->sales->sum('total_amount'), 2)); ?> </h4>
                                            <p class="mb-0">إجمالي المبيعات</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-success text-white mb-3">
                                        <div class="card-body text-center">
                                            <h4><?php echo e(number_format($branch->purchases->sum('total_amount'), 2)); ?>

                                            </h4>
                                            <p class="mb-0">إجمالي المشتريات</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-info text-white mb-3">
                                        <div class="card-body text-center">
                                            <h4><?php echo e($branch->sales->count()); ?></h4>
                                            <p class="mb-0">عدد المبيعات</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-warning text-white mb-3">
                                        <div class="card-body text-center">
                                            <h4><?php echo e($branch->purchases->count()); ?></h4>
                                            <p class="mb-0">عدد المشتريات</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-secondary text-white mb-3">
                                        <div class="card-body text-center">
                                            <h4><?php echo e($branch->expenses->sum('amount') ?? 0); ?></h4>
                                            <p class="mb-0">إجمالي المصروفات</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-dark text-white mb-3">
                                        <div class="card-body text-center">
                                            <h4><?php echo e(number_format($branch->sales->sum('total_amount') - $branch->purchases->sum('total_amount') - ($branch->expenses->sum('amount') ?? 0), 2)); ?>

                                            </h4>
                                            <p class="mb-0">صافي الربح</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <a href="<?php echo e(route('admin.branches.show', $branch)); ?>" class="btn btn-primary">
                                عرض التفاصيل الكاملة
                            </a>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\admin\branches.blade.php ENDPATH**/ ?>