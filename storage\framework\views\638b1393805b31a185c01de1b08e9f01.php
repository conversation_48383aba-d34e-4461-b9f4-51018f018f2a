<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('تصنيفات المصروفات')); ?>

            </h2>
            <div>
                <a href="<?php echo e(route('expense-categories.export')); ?>" class="btn btn-success me-2">
                    <i class="fas fa-file-export"></i> <?php echo e(__('تصدير')); ?>

                </a>
                <a href="<?php echo e(route('expense-categories.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> <?php echo e(__('إضافة تصنيف جديد')); ?>

                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Create Button -->
                    <div class="mb-4">
                        <a href="<?php echo e(route('expense-categories.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> <?php echo e(__('إضافة تصنيف جديد')); ?>

                        </a>
                    </div>

                    <!-- Filters -->
                    <form action="<?php echo e(route('expense-categories.index')); ?>" method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search"><?php echo e(__('بحث')); ?></label>
                                    <input type="text" name="search" id="search" class="form-control" value="<?php echo e(request('search')); ?>" placeholder="<?php echo e(__('اسم التصنيف')); ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="status"><?php echo e(__('الحالة')); ?></label>
                                    <select name="status" id="status" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>><?php echo e(__('نشط')); ?></option>
                                        <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>><?php echo e(__('غير نشط')); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="type"><?php echo e(__('النوع')); ?></label>
                                    <select name="type" id="type" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <option value="fixed" <?php echo e(request('type') == 'fixed' ? 'selected' : ''); ?>><?php echo e(__('ثابت')); ?></option>
                                        <option value="variable" <?php echo e(request('type') == 'variable' ? 'selected' : ''); ?>><?php echo e(__('متغير')); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="parent"><?php echo e(__('التصنيف الأب')); ?></label>
                                    <select name="parent" id="parent" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <option value="root" <?php echo e(request('parent') == 'root' ? 'selected' : ''); ?>><?php echo e(__('التصنيفات الرئيسية')); ?></option>
                                        <?php $__currentLoopData = $parentCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parent): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($parent->id); ?>" <?php echo e(request('parent') == $parent->id ? 'selected' : ''); ?>>
                                                <?php echo e($parent->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12 text-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> <?php echo e(__('بحث')); ?>

                                </button>
                                <a href="<?php echo e(route('expense-categories.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> <?php echo e(__('إعادة تعيين')); ?>

                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Bulk Actions -->
                    <form action="<?php echo e(route('expense-categories.bulk-action')); ?>" method="POST" id="bulk-action-form" class="mb-4">
                        <?php echo csrf_field(); ?>
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select name="action" class="form-select" id="bulk-action">
                                    <option value=""><?php echo e(__('إجراءات متعددة')); ?></option>
                                    <option value="activate"><?php echo e(__('تفعيل')); ?></option>
                                    <option value="deactivate"><?php echo e(__('إيقاف')); ?></option>
                                    <option value="delete"><?php echo e(__('حذف')); ?></option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary" id="bulk-action-button" disabled>
                                    <?php echo e(__('تطبيق')); ?>

                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Categories Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="select-all">
                                    </th>
                                    <th><?php echo e(__('الاسم')); ?></th>
                                    <th><?php echo e(__('الوصف')); ?></th>
                                    <th><?php echo e(__('التصنيف الأب')); ?></th>
                                    <th><?php echo e(__('الميزانية الشهرية')); ?></th>
                                    <th><?php echo e(__('الميزانية السنوية')); ?></th>
                                    <th><?php echo e(__('عدد المصروفات')); ?></th>
                                    <th><?php echo e(__('إجمالي المصروفات')); ?></th>
                                    <th><?php echo e(__('الحالة')); ?></th>
                                    <th><?php echo e(__('الإجراءات')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="categories[]" value="<?php echo e($category->id); ?>" class="category-checkbox">
                                        </td>
                                        <td>
                                            <?php if($category->icon): ?>
                                                <img src="<?php echo e(Storage::url($category->icon)); ?>" alt="<?php echo e($category->name); ?>" class="category-icon me-2" style="width: 24px; height: 24px;">
                                            <?php endif; ?>
                                            <span style="color: <?php echo e($category->color ?: '#000'); ?>"><?php echo e($category->name); ?></span>
                                        </td>
                                        <td><?php echo e($category->description ?: '-'); ?></td>
                                        <td><?php echo e($category->parent ? $category->parent->name : '-'); ?></td>
                                        <td>
                                            <?php if($category->monthly_budget): ?>
                                                <?php echo e(number_format($category->monthly_budget, 2)); ?>

                                                <div class="progress mt-1" style="height: 5px;">
                                                    <div class="progress-bar <?php echo e($category->monthly_budget_usage > 100 ? 'bg-danger' : 'bg-success'); ?>"
                                                         role="progressbar"
                                                         style="width: <?php echo e(min($category->monthly_budget_usage, 100)); ?>%">
                                                    </div>
                                                </div>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($category->yearly_budget): ?>
                                                <?php echo e(number_format($category->yearly_budget, 2)); ?>

                                                <div class="progress mt-1" style="height: 5px;">
                                                    <div class="progress-bar <?php echo e($category->yearly_budget_usage > 100 ? 'bg-danger' : 'bg-success'); ?>"
                                                         role="progressbar"
                                                         style="width: <?php echo e(min($category->yearly_budget_usage, 100)); ?>%">
                                                    </div>
                                                </div>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($category->expenses_count); ?></td>
                                        <td><?php echo e(number_format($category->expenses_sum_amount, 2)); ?></td>
                                        <td>
                                            <?php if($category->is_active): ?>
                                                <span class="badge bg-success"><?php echo e(__('نشط')); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-danger"><?php echo e(__('غير نشط')); ?></span>
                                            <?php endif; ?>
                                            <?php if($category->is_fixed): ?>
                                                <span class="badge bg-info"><?php echo e(__('ثابت')); ?></span>
                                            <?php endif; ?>
                                            <?php if($category->requires_approval): ?>
                                                <span class="badge bg-warning"><?php echo e(__('يتطلب موافقة')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="<?php echo e(route('expense-categories.show', $category)); ?>" class="btn btn-info btn-sm" title="<?php echo e(__('عرض')); ?>">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('expense-categories.edit', $category)); ?>" class="btn btn-primary btn-sm" title="<?php echo e(__('تعديل')); ?>">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="<?php echo e(route('expense-categories.destroy', $category)); ?>" method="POST" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('<?php echo e(__('هل أنت متأكد من حذف هذا التصنيف؟')); ?>')" title="<?php echo e(__('حذف')); ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="10" class="text-center"><?php echo e(__('لا توجد تصنيفات')); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        <?php echo e($categories->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .form-select {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .btn-group {
            flex-direction: row-reverse;
        }
        .rtl .btn-group .btn {
            margin-right: 0;
            margin-left: 0.25rem;
        }
        .rtl .text-start {
            text-align: right !important;
        }
        .rtl .text-end {
            text-align: left !important;
        }
        .rtl .form-control {
            text-align: right;
        }
        .rtl .form-control::placeholder {
            text-align: right;
        }
        .category-icon {
            object-fit: cover;
            border-radius: 4px;
        }
        .progress {
            background-color: #e9ecef;
            border-radius: 0.25rem;
        }
    </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectAll = document.getElementById('select-all');
            const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
            const bulkActionButton = document.getElementById('bulk-action-button');
            const bulkAction = document.getElementById('bulk-action');

            selectAll.addEventListener('change', function() {
                categoryCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActionButton();
            });

            categoryCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateBulkActionButton();
                    selectAll.checked = Array.from(categoryCheckboxes).every(cb => cb.checked);
                });
            });

            bulkAction.addEventListener('change', function() {
                updateBulkActionButton();
            });

            function updateBulkActionButton() {
                const checkedBoxes = document.querySelectorAll('.category-checkbox:checked');
                bulkActionButton.disabled = !checkedBoxes.length || !bulkAction.value;
            }

            document.getElementById('bulk-action-form').addEventListener('submit', function(e) {
                if (bulkAction.value === 'delete') {
                    if (!confirm('<?php echo e(__("هل أنت متأكد من حذف التصنيفات المحددة؟")); ?>')) {
                        e.preventDefault();
                    }
                }
            });
        });
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\expense-categories\index.blade.php ENDPATH**/ ?>