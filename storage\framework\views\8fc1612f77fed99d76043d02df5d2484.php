<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-eye text-info me-2"></i>
                    تفاصيل الدفعة
                </h1>
                <p class="mb-0 text-muted">عرض تفاصيل دفعة العميل</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(user_route('customer-payments.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى الدفعات
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Payment Details -->
            <div class="col-md-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-info-circle me-2"></i>معلومات الدفعة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">رقم الفاتورة:</strong>
                            </div>
                            <div class="col-sm-8">
                                <a href="<?php echo e(user_route('sales.show', $payment->sale)); ?>" 
                                   class="text-decoration-none fw-bold text-primary">
                                    <?php echo e($payment->sale->invoice_number); ?>

                                </a>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">العميل:</strong>
                            </div>
                            <div class="col-sm-8">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-info text-white me-2">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold"><?php echo e($payment->sale->customer?->name ?? 'عميل نقدي'); ?></div>
                                        <?php if($payment->sale->customer?->phone): ?>
                                            <small class="text-muted"><?php echo e($payment->sale->customer->phone); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">المبلغ:</strong>
                            </div>
                            <div class="col-sm-8">
                                <span class="badge bg-success fs-6 px-3 py-2">
                                    <?php echo e(number_format($payment->amount, 2)); ?> ج.م
                                </span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">طريقة الدفع:</strong>
                            </div>
                            <div class="col-sm-8">
                                <span class="badge bg-primary">
                                    <?php echo e($payment->payment_method_label); ?>

                                </span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">رقم المرجع:</strong>
                            </div>
                            <div class="col-sm-8">
                                <?php echo e($payment->reference_number ?? 'غير محدد'); ?>

                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">تاريخ الدفع:</strong>
                            </div>
                            <div class="col-sm-8">
                                <div class="text-dark"><?php echo e($payment->payment_date->format('d/m/Y')); ?></div>
                                <small class="text-muted"><?php echo e($payment->payment_date->format('h:i A')); ?></small>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">المستخدم:</strong>
                            </div>
                            <div class="col-sm-8">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-secondary text-white me-2">
                                        <?php echo e(substr($payment->user->name, 0, 1)); ?>

                                    </div>
                                    <div>
                                        <div class="fw-bold"><?php echo e($payment->user->name); ?></div>
                                        <small class="text-muted"><?php echo e($payment->created_at->format('d/m/Y h:i A')); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php if($payment->notes): ?>
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">الملاحظات:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <div class="p-2 bg-light rounded">
                                        <?php echo e($payment->notes); ?>

                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sale Summary -->
            <div class="col-md-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-receipt me-2"></i>ملخص العملية
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-6">
                                <small class="text-muted">المبلغ الإجمالي:</small>
                            </div>
                            <div class="col-6 text-end">
                                <span class="fw-bold"><?php echo e(number_format($payment->sale->total_amount, 2)); ?> ج.م</span>
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-6">
                                <small class="text-muted">الخصم:</small>
                            </div>
                            <div class="col-6 text-end">
                                <span class="text-warning"><?php echo e(number_format($payment->sale->discount_amount, 2)); ?> ج.م</span>
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-6">
                                <small class="text-muted">المدفوع:</small>
                            </div>
                            <div class="col-6 text-end">
                                <span class="text-success"><?php echo e(number_format($payment->sale->paid_amount, 2)); ?> ج.م</span>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong class="text-muted">المتبقي:</strong>
                            </div>
                            <div class="col-6 text-end">
                                <span class="fw-bold <?php echo e($payment->sale->getActualRemainingAmountAttribute() > 0 ? 'text-danger' : 'text-success'); ?>">
                                    <?php echo e(number_format($payment->sale->getActualRemainingAmountAttribute(), 2)); ?> ج.م
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-cogs me-2"></i>الإجراءات
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(user_route('sales.show', $payment->sale)); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>عرض الفاتورة
                            </a>
                            <?php if($payment->sale->canReceivePayment()): ?>
                                <a href="<?php echo e(user_route('customer-payments.create', ['sale_id' => $payment->sale->id])); ?>" 
                                   class="btn btn-outline-success">
                                    <i class="fas fa-plus me-2"></i>إضافة دفعة أخرى
                                </a>
                            <?php endif; ?>
                            <?php if(auth()->user()->hasRole('admin')): ?>
                                <form method="POST" action="<?php echo e(user_route('customer-payments.destroy', $payment)); ?>"
                                      onsubmit="return confirm('هل أنت متأكد من حذف هذه الدفعة؟')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-outline-danger">
                                        <i class="fas fa-trash me-2"></i>حذف الدفعة
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
        <style>
            .avatar-circle {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 14px;
            }
        </style>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\customer-payments\show.blade.php ENDPATH**/ ?>