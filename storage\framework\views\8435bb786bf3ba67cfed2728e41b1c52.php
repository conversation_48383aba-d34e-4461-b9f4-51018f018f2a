<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('تفاصيل تصنيف المصروفات')); ?>

            </h2>
            <div>
                <a href="<?php echo e(route('expense-categories.edit', $expenseCategory)); ?>" class="btn btn-primary me-2">
                    <i class="fas fa-edit"></i> <?php echo e(__('تعديل')); ?>

                </a>
                <a href="<?php echo e(route('expense-categories.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> <?php echo e(__('عودة')); ?>

                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="container-fluid">
            <div class="row">
                <!-- Category Information -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo e(__('معلومات التصنيف')); ?></h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong><?php echo e(__('الاسم')); ?>:</strong>
                                </div>
                                <div class="col-md-8">
                                    <?php if($expenseCategory->icon): ?>
                                        <img src="<?php echo e(Storage::url($expenseCategory->icon)); ?>" alt="<?php echo e($expenseCategory->name); ?>" class="category-icon me-2" style="width: 24px; height: 24px;">
                                    <?php endif; ?>
                                    <span style="color: <?php echo e($expenseCategory->color ?: '#000'); ?>"><?php echo e($expenseCategory->name); ?></span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong><?php echo e(__('الوصف')); ?>:</strong>
                                </div>
                                <div class="col-md-8">
                                    <?php echo e($expenseCategory->description ?: '-'); ?>

                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong><?php echo e(__('التصنيف الأب')); ?>:</strong>
                                </div>
                                <div class="col-md-8">
                                    <?php echo e($expenseCategory->parent ? $expenseCategory->parent->name : '-'); ?>

                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong><?php echo e(__('الحالة')); ?>:</strong>
                                </div>
                                <div class="col-md-8">
                                    <?php if($expenseCategory->is_active): ?>
                                        <span class="badge bg-success"><?php echo e(__('نشط')); ?></span>
                                    <?php else: ?>
                                        <span class="badge bg-danger"><?php echo e(__('غير نشط')); ?></span>
                                    <?php endif; ?>
                                    <?php if($expenseCategory->is_fixed): ?>
                                        <span class="badge bg-info"><?php echo e(__('ثابت')); ?></span>
                                    <?php endif; ?>
                                    <?php if($expenseCategory->requires_approval): ?>
                                        <span class="badge bg-warning"><?php echo e(__('يتطلب موافقة')); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong><?php echo e(__('تاريخ الإنشاء')); ?>:</strong>
                                </div>
                                <div class="col-md-8">
                                    <?php echo e($expenseCategory->created_at->format('Y-m-d H:i')); ?>

                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong><?php echo e(__('آخر تحديث')); ?>:</strong>
                                </div>
                                <div class="col-md-8">
                                    <?php echo e($expenseCategory->updated_at->format('Y-m-d H:i')); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Budget Information -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo e(__('معلومات الميزانية')); ?></h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong><?php echo e(__('الميزانية الشهرية')); ?>:</strong>
                                </div>
                                <div class="col-md-8">
                                    <?php if($expenseCategory->monthly_budget): ?>
                                        <div class="d-flex align-items-center">
                                            <span class="me-2"><?php echo e(number_format($expenseCategory->monthly_budget, 2)); ?></span>
                                            <div class="progress flex-grow-1" style="height: 5px;">
                                                <div class="progress-bar <?php echo e($expenseCategory->monthly_budget_usage > 100 ? 'bg-danger' : 'bg-success'); ?>"
                                                     role="progressbar"
                                                     style="width: <?php echo e(min($expenseCategory->monthly_budget_usage, 100)); ?>%">
                                                </div>
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo e(__('المصروفات الشهرية')); ?>: <?php echo e(number_format($monthlyExpenses, 2)); ?>

                                            (<?php echo e(number_format($expenseCategory->monthly_budget_usage, 1)); ?>%)
                                        </small>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong><?php echo e(__('الميزانية السنوية')); ?>:</strong>
                                </div>
                                <div class="col-md-8">
                                    <?php if($expenseCategory->yearly_budget): ?>
                                        <div class="d-flex align-items-center">
                                            <span class="me-2"><?php echo e(number_format($expenseCategory->yearly_budget, 2)); ?></span>
                                            <div class="progress flex-grow-1" style="height: 5px;">
                                                <div class="progress-bar <?php echo e($expenseCategory->yearly_budget_usage > 100 ? 'bg-danger' : 'bg-success'); ?>"
                                                     role="progressbar"
                                                     style="width: <?php echo e(min($expenseCategory->yearly_budget_usage, 100)); ?>%">
                                                </div>
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo e(__('المصروفات السنوية')); ?>: <?php echo e(number_format($yearlyExpenses, 2)); ?>

                                            (<?php echo e(number_format($expenseCategory->yearly_budget_usage, 1)); ?>%)
                                        </small>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong><?php echo e(__('عدد المصروفات')); ?>:</strong>
                                </div>
                                <div class="col-md-8">
                                    <?php echo e($expenseCategory->expenses_count); ?>

                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong><?php echo e(__('إجمالي المصروفات')); ?>:</strong>
                                </div>
                                <div class="col-md-8">
                                    <?php echo e(number_format($expenseCategory->expenses_sum_amount, 2)); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subcategories -->
                <?php if($expenseCategory->children->isNotEmpty()): ?>
                    <div class="col-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><?php echo e(__('التصنيفات الفرعية')); ?></h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th><?php echo e(__('الاسم')); ?></th>
                                                <th><?php echo e(__('الوصف')); ?></th>
                                                <th><?php echo e(__('عدد المصروفات')); ?></th>
                                                <th><?php echo e(__('إجمالي المصروفات')); ?></th>
                                                <th><?php echo e(__('الحالة')); ?></th>
                                                <th><?php echo e(__('الإجراءات')); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $expenseCategory->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td>
                                                        <?php if($child->icon): ?>
                                                            <img src="<?php echo e(Storage::url($child->icon)); ?>" alt="<?php echo e($child->name); ?>" class="category-icon me-2" style="width: 24px; height: 24px;">
                                                        <?php endif; ?>
                                                        <span style="color: <?php echo e($child->color ?: '#000'); ?>"><?php echo e($child->name); ?></span>
                                                    </td>
                                                    <td><?php echo e($child->description ?: '-'); ?></td>
                                                    <td><?php echo e($child->expenses_count); ?></td>
                                                    <td><?php echo e(number_format($child->expenses_sum_amount, 2)); ?></td>
                                                    <td>
                                                        <?php if($child->is_active): ?>
                                                            <span class="badge bg-success"><?php echo e(__('نشط')); ?></span>
                                                        <?php else: ?>
                                                            <span class="badge bg-danger"><?php echo e(__('غير نشط')); ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="<?php echo e(route('expense-categories.show', $child)); ?>" class="btn btn-info btn-sm" title="<?php echo e(__('عرض')); ?>">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="<?php echo e(route('expense-categories.edit', $child)); ?>" class="btn btn-primary btn-sm" title="<?php echo e(__('تعديل')); ?>">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Expenses -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo e(__('المصروفات')); ?></h5>
                        </div>
                        <div class="card-body">
                            <?php if($expenseCategory->expenses->isNotEmpty()): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th><?php echo e(__('التاريخ')); ?></th>
                                                <th><?php echo e(__('المرجع')); ?></th>
                                                <th><?php echo e(__('الفرع')); ?></th>
                                                <th><?php echo e(__('المبلغ')); ?></th>
                                                <th><?php echo e(__('الحالة')); ?></th>
                                                <th><?php echo e(__('الإجراءات')); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__empty_1 = true; $__currentLoopData = $expenses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $expense): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <tr>
                                                    <td><?php echo e($expense->expense_date->format('Y-m-d')); ?></td>
                                                    <td><?php echo e($expense->reference_number); ?></td>
                                                    <td><?php echo e($expense->branch->name); ?></td>
                                                    <td><?php echo e(number_format($expense->amount, 2)); ?></td>
                                                    <td>
                                                        <?php if($expense->status == 'pending'): ?>
                                                            <span class="badge bg-warning"><?php echo e(__('قيد الانتظار')); ?></span>
                                                        <?php elseif($expense->status == 'approved'): ?>
                                                            <span class="badge bg-success"><?php echo e(__('تمت الموافقة')); ?></span>
                                                        <?php else: ?>
                                                            <span class="badge bg-danger"><?php echo e(__('مرفوض')); ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <a href="<?php echo e(route('expenses.show', $expense)); ?>" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <tr>
                                                    <td colspan="6" class="text-center"><?php echo e(__('لا توجد مصروفات')); ?></td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-4">
                                    <?php echo e($expenses->links()); ?>

                                </div>
                            <?php else: ?>
                                <p class="text-center mb-0"><?php echo e(__('لا توجد مصروفات لهذا التصنيف')); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .card-title {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .btn-group {
            flex-direction: row-reverse;
        }
        .rtl .btn-group .btn {
            margin-right: 0;
            margin-left: 0.25rem;
        }
        .category-icon {
            object-fit: cover;
            border-radius: 4px;
        }
        .progress {
            background-color: #e9ecef;
            border-radius: 0.25rem;
        }
    </style>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\expense-categories\show.blade.php ENDPATH**/ ?>