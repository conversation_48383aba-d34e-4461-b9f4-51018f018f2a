<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تفاصيل الفرع: <?php echo e($branch->name); ?></h5>
                <div>
                    <?php if(auth()->user()->isAdmin()): ?>
                        <a href="<?php echo e(user_route('branches.edit', $branch)); ?>" class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a href="<?php echo e(user_route('branches.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> عودة
                        </a>
                    <?php else: ?>
                        <a href="<?php echo e(user_route('dashboard')); ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> عودة للوحة التحكم
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th>الكود</th>
                                <td><?php echo e($branch->code); ?></td>
                            </tr>
                            <tr>
                                <th>الاسم</th>
                                <td><?php echo e($branch->name); ?></td>
                            </tr>
                            <tr>
                                <th>العنوان</th>
                                <td><?php echo e($branch->address ?? '-'); ?></td>
                            </tr>
                            <tr>
                                <th>الهاتف</th>
                                <td><?php echo e($branch->phone ?? '-'); ?></td>
                            </tr>
                            <tr>
                                <th>البريد الإلكتروني</th>
                                <td><?php echo e($branch->email ?? '-'); ?></td>
                            </tr>
                            <tr>
                                <th>الحالة</th>
                                <td>
                                    <span class="badge bg-<?php echo e($branch->is_active ? 'success' : 'danger'); ?>">
                                        <?php echo e($branch->is_active ? 'نشط' : 'غير نشط'); ?>

                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Quick Actions for Sales Management -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title mb-3">
                                    <i class="fas fa-store"></i> إدارة مبيعات الفرع
                                </h6>
                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <a href="<?php echo e(route('admin.branches.sales-inventory', $branch)); ?>"
                                            class="btn btn-outline-primary w-100">
                                            <i class="fas fa-boxes"></i> مخزون المبيعات
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="<?php echo e(route('admin.branches.receive-products', $branch)); ?>"
                                            class="btn btn-outline-success w-100">
                                            <i class="fas fa-truck"></i> استلام منتجات
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>?destination_type=branch&destination_id=<?php echo e($branch->id); ?>"
                                            class="btn btn-outline-warning w-100">
                                            <i class="fas fa-exchange-alt"></i> طلب نقل
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="<?php echo e(route('admin.branches.analytics', $branch)); ?>"
                                            class="btn btn-outline-info w-100">
                                            <i class="fas fa-chart-line"></i> تحليلات المبيعات
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <ul class="nav nav-tabs" id="branchTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="inventory-tab" data-bs-toggle="tab" href="#inventory"
                                    role="tab">
                                    المخزون
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="sales-tab" data-bs-toggle="tab" href="#sales" role="tab">
                                    المبيعات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="purchases-tab" data-bs-toggle="tab" href="#purchases"
                                    role="tab">
                                    المشتريات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="expenses-tab" data-bs-toggle="tab" href="#expenses"
                                    role="tab">
                                    المصروفات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="cash-tab" data-bs-toggle="tab" href="#cash" role="tab">
                                    المعاملات النقدية
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="users-tab" data-bs-toggle="tab" href="#users" role="tab">
                                    المستخدمين
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content mt-3" id="branchTabsContent">
                            <div class="tab-pane fade show active" id="inventory" role="tabpanel">
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body text-center">
                                                <h5><?php echo e($branch->branchInventories->count()); ?></h5>
                                                <small>إجمالي المنتجات</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body text-center">
                                                <h5><?php echo e($branch->branchInventories->where('quantity', '<=', 10)->count()); ?>

                                                </h5>
                                                <small>منتجات قليلة المخزون</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-danger text-white">
                                            <div class="card-body text-center">
                                                <h5><?php echo e($branch->branchInventories->where('quantity', '<=', 0)->count()); ?>

                                                </h5>
                                                <small>منتجات نفدت</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body text-center">
                                                <h5><?php echo e(format_currency($branch->branchInventories->sum(function ($item) {return $item->quantity * ($item->cost_price ?? 0);}))); ?>

                                                </h5>
                                                <small>قيمة المخزون</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكمية</th>
                                                <th>سعر التكلفة</th>
                                                <th>سعر البيع 1</th>
                                                <th>سعر البيع 2</th>
                                                <th>سعر البيع 3</th>
                                                <th>إجمالي القيمة</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__empty_1 = true; $__currentLoopData = $branch->branchInventories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $inventory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <tr
                                                    class="<?php echo e($inventory->quantity <= 0 ? 'table-danger' : ($inventory->quantity <= 10 ? 'table-warning' : '')); ?>">
                                                    <td>
                                                        <strong><?php echo e($inventory->product->name); ?></strong>
                                                        <br><small
                                                            class="text-muted"><?php echo e($inventory->product->sku); ?></small>
                                                    </td>
                                                    <td>
                                                        <span
                                                            class="badge bg-<?php echo e($inventory->quantity > 10 ? 'success' : ($inventory->quantity > 0 ? 'warning' : 'danger')); ?>">
                                                            <?php echo e($inventory->quantity); ?>

                                                        </span>
                                                    </td>
                                                    <td><?php echo e(format_currency($inventory->cost_price ?? 0)); ?></td>
                                                    <td><?php echo e(format_currency($inventory->sale_price_1 ?? 0)); ?></td>
                                                    <td><?php echo e($inventory->sale_price_2 ? format_currency($inventory->sale_price_2) : '-'); ?>

                                                    </td>
                                                    <td><?php echo e($inventory->sale_price_3 ? format_currency($inventory->sale_price_3) : '-'); ?>

                                                    </td>
                                                    <td><?php echo e(format_currency(($inventory->cost_price ?? 0) * $inventory->quantity)); ?>

                                                    </td>
                                                    <td>
                                                        <?php if($inventory->quantity <= 0): ?>
                                                            <span class="badge bg-danger">نفد المخزون</span>
                                                        <?php elseif($inventory->quantity <= 10): ?>
                                                            <span class="badge bg-warning">مخزون قليل</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-success">متوفر</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <tr>
                                                    <td colspan="8" class="text-center py-4">
                                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                                        <p class="text-muted">لا يوجد مخزون في هذا الفرع</p>
                                                        <a href="<?php echo e(route('admin.branches.receive-products', $branch)); ?>"
                                                            class="btn btn-primary">
                                                            <i class="fas fa-plus"></i> إضافة منتجات
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="sales" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>التاريخ</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__empty_1 = true; $__currentLoopData = $branch->sales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <tr>
                                                    <td><?php echo e($sale->invoice_number); ?></td>
                                                    <td><?php echo e($sale->sale_date); ?></td>
                                                    <td><?php echo e(number_format($sale->total_amount, 2)); ?></td>
                                                    <td>
                                                        <span
                                                            class="badge bg-<?php echo e($sale->status === 'completed' ? 'success' : ($sale->status === 'pending' ? 'warning' : 'danger')); ?>">
                                                            <?php echo e($sale->status === 'completed' ? 'مكتمل' : ($sale->status === 'pending' ? 'قيد الانتظار' : 'ملغي')); ?>

                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">لا توجد مبيعات</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="purchases" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>المورد</th>
                                                <th>التاريخ</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__empty_1 = true; $__currentLoopData = $branch->purchases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $purchase): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <tr>
                                                    <td><?php echo e($purchase->invoice_number); ?></td>
                                                    <td><?php echo e($purchase->supplier->name); ?></td>
                                                    <td><?php echo e($purchase->purchase_date); ?></td>
                                                    <td><?php echo e(number_format($purchase->total_amount, 2)); ?></td>
                                                    <td>
                                                        <span
                                                            class="badge bg-<?php echo e($purchase->status === 'completed' ? 'success' : ($purchase->status === 'pending' ? 'warning' : 'danger')); ?>">
                                                            <?php echo e($purchase->status === 'completed' ? 'مكتمل' : ($purchase->status === 'pending' ? 'قيد الانتظار' : 'ملغي')); ?>

                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <tr>
                                                    <td colspan="5" class="text-center">لا توجد مشتريات</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="expenses" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>رقم المرجع</th>
                                                <th>التاريخ</th>
                                                <th>الفئة</th>
                                                <th>المبلغ</th>
                                                <th>طريقة الدفع</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__empty_1 = true; $__currentLoopData = $branch->expenses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $expense): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <tr>
                                                    <td><?php echo e($expense->reference_number); ?></td>
                                                    <td><?php echo e($expense->expense_date->format('Y-m-d')); ?></td>
                                                    <td><?php echo e($expense->category->name); ?></td>
                                                    <td><?php echo e(number_format($expense->amount, 2)); ?></td>
                                                    <td><?php echo e($expense->payment_method === 'cash' ? 'نقدي' : 'بنكي'); ?>

                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <tr>
                                                    <td colspan="5" class="text-center">لا توجد مصروفات</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="cash" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>النوع</th>
                                                <th>المبلغ</th>
                                                <th>الملاحظات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__empty_1 = true; $__currentLoopData = $branch->cashTransactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <tr>
                                                    <td><?php echo e($transaction->created_at->format('Y-m-d H:i')); ?></td>
                                                    <td>
                                                        <?php switch($transaction->type):
                                                            case ('opening'): ?>
                                                                رصيد افتتاحي
                                                            <?php break; ?>

                                                            <?php case ('closing'): ?>
                                                                رصيد إغلاق
                                                            <?php break; ?>

                                                            <?php case ('sale'): ?>
                                                                مبيعات
                                                            <?php break; ?>

                                                            <?php case ('purchase'): ?>
                                                                مشتريات
                                                            <?php break; ?>

                                                            <?php case ('expense'): ?>
                                                                مصروفات
                                                            <?php break; ?>

                                                            <?php case ('transfer'): ?>
                                                                تحويل
                                                            <?php break; ?>
                                                        <?php endswitch; ?>
                                                    </td>
                                                    <td
                                                        class="<?php echo e($transaction->amount >= 0 ? 'text-success' : 'text-danger'); ?>">
                                                        <?php echo e(number_format($transaction->amount, 2)); ?>

                                                    </td>
                                                    <td><?php echo e($transaction->notes); ?></td>
                                                </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                    <tr>
                                                        <td colspan="4" class="text-center">لا توجد معاملات نقدية</td>
                                                    </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="tab-pane fade" id="users" role="tabpanel">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>الاسم</th>
                                                    <th>البريد الإلكتروني</th>
                                                    <th>الدور</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__empty_1 = true; $__currentLoopData = $branch->users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                    <tr>
                                                        <td><?php echo e($user->name); ?></td>
                                                        <td><?php echo e($user->email); ?></td>
                                                        <td>
                                                            <?php switch($user->role):
                                                                case ('admin'): ?>
                                                                    مدير النظام
                                                                <?php break; ?>

                                                                <?php case ('manager'): ?>
                                                                    مدير فرع
                                                                <?php break; ?>

                                                                <?php case ('staff'): ?>
                                                                    موظف
                                                                <?php break; ?>
                                                            <?php endswitch; ?>
                                                        </td>
                                                        <td>
                                                            <span
                                                                class="badge bg-<?php echo e($user->is_active ? 'success' : 'danger'); ?>">
                                                                <?php echo e($user->is_active ? 'نشط' : 'غير نشط'); ?>

                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                        <tr>
                                                            <td colspan="4" class="text-center">لا يوجد مستخدمين</td>
                                                        </tr>
                                                    <?php endif; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\branches\show.blade.php ENDPATH**/ ?>