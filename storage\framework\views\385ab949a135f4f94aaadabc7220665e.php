<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">تاريخ مدفوعات عملية الشراء</h1>
                <p class="text-muted mb-0">عملية شراء رقم #<?php echo e($purchase->id); ?> - <?php echo e($purchase->supplier->name); ?></p>
            </div>
            <div>
                <a href="<?php echo e(user_route('supplier-payments.index')); ?>" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
                <?php if($purchase->actual_remaining_amount > 0): ?>
                    <a href="<?php echo e(user_route('supplier-payments.create', $purchase)); ?>" class="btn btn-success">
                        <i class="fas fa-money-bill-wave me-2"></i>دفع مبلغ
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <div class="row">
            <!-- Purchase Summary -->
            <div class="col-lg-4 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">ملخص عملية الشراء</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">رقم العملية</label>
                            <div class="fw-bold">#<?php echo e($purchase->id); ?></div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">المورد</label>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle bg-primary text-white me-2">
                                    <?php echo e(substr($purchase->supplier->name, 0, 1)); ?>

                                </div>
                                <div>
                                    <div class="fw-bold"><?php echo e($purchase->supplier->name); ?></div>
                                    <?php if($purchase->supplier->phone): ?>
                                        <small class="text-muted"><?php echo e($purchase->supplier->phone); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">تاريخ الشراء</label>
                            <div class="fw-bold"><?php echo e($purchase->purchase_date); ?></div>
                        </div>

                        <hr>

                        <div class="mb-3">
                            <label class="form-label text-muted">إجمالي المبلغ</label>
                            <div class="fw-bold text-primary fs-5"><?php echo e(format_currency($purchase->total_amount)); ?></div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">المبلغ المدفوع</label>
                            <div class="fw-bold text-success fs-5"><?php echo e(format_currency($purchase->paid_amount)); ?></div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">المبلغ المتبقي</label>
                            <div
                                class="fw-bold text-<?php echo e($purchase->actual_remaining_amount > 0 ? 'danger' : 'success'); ?> fs-5">
                                <?php echo e(format_currency($purchase->actual_remaining_amount)); ?>

                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">حالة الدفع</label>
                            <div>
                                <span class="badge bg-<?php echo e($purchase->payment_status_color); ?> fs-6">
                                    <?php echo e($purchase->payment_status); ?>

                                </span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">نسبة الدفع</label>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-<?php echo e($purchase->payment_status_color); ?>" role="progressbar"
                                    style="width: <?php echo e(($purchase->paid_amount / $purchase->total_amount) * 100); ?>%">
                                    <?php echo e(number_format(($purchase->paid_amount / $purchase->total_amount) * 100, 1)); ?>%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment History -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">تاريخ المدفوعات</h6>
                        <span class="badge bg-info"><?php echo e($paymentTransactions->count()); ?> دفعة</span>
                    </div>
                    <div class="card-body">
                        <?php if($paymentTransactions->count() > 0): ?>
                            <div class="timeline">
                                <?php $__currentLoopData = $paymentTransactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-success"></div>
                                        <div class="timeline-content">
                                            <div class="card border-left-success">
                                                <div class="card-body py-3">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-8">
                                                            <div
                                                                class="d-flex justify-content-between align-items-start mb-2">
                                                                <div>
                                                                    <h6 class="mb-1 text-success fw-bold">
                                                                        <?php echo e(format_currency($transaction->amount)); ?>

                                                                    </h6>
                                                                    <small class="text-muted">
                                                                        <?php echo e($transaction->created_at->format('Y-m-d H:i')); ?>

                                                                    </small>
                                                                </div>
                                                                <span class="badge bg-success">دفعة</span>
                                                            </div>

                                                            <?php if($transaction->description): ?>
                                                                <p class="mb-2 text-muted small">
                                                                    <?php echo e($transaction->description); ?></p>
                                                            <?php endif; ?>

                                                            <div class="d-flex align-items-center">
                                                                <i class="fas fa-user fa-sm text-muted me-1"></i>
                                                                <small class="text-muted">
                                                                    <?php echo e($transaction->user->name ?? 'غير محدد'); ?>

                                                                </small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4 text-end">
                                                            <div class="text-muted small">
                                                                <div>رقم المرجع:</div>
                                                                <div class="fw-bold">
                                                                    <?php echo e($transaction->reference_number); ?>

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                <h5>لا توجد مدفوعات</h5>
                                <p class="text-muted">لم يتم تسجيل أي مدفوعات لهذه العملية بعد</p>
                                <?php if($purchase->actual_remaining_amount > 0): ?>
                                    <a href="<?php echo e(user_route('supplier-payments.create', $purchase)); ?>"
                                        class="btn btn-success">
                                        <i class="fas fa-money-bill-wave me-2"></i>تسجيل أول دفعة
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e3e6f0;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #e3e6f0;
        }

        .timeline-content {
            margin-left: 10px;
        }

        .progress {
            height: 8px;
        }
    </style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\supplier-payments\show.blade.php ENDPATH**/ ?>