<x-app-layout>
    <div class="container-fluid px-4">
        <!-- <PERSON> Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-receipt text-primary me-2"></i>
                    تفاصيل المشتريات #{{ $purchase->invoice_number }}
                </h1>
                <p class="text-muted mb-0">عرض تفاصيل عملية الشراء والمنتجات المرتبطة</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ user_route('purchases.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة إلى المشتريات
                </a>
                @if ($purchase->status === 'pending')
                    <a href="{{ user_route('purchases.edit', $purchase) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </a>
                @endif
                @if ($purchase->status === 'completed' && $purchase->actual_remaining_amount > 0)
                    <a href="{{ user_route('supplier-payments.create', $purchase) }}" class="btn btn-warning">
                        <i class="fas fa-money-bill-wave me-2"></i>دفع مبلغ
                    </a>
                    <a href="{{ user_route('supplier-payments.show', $purchase) }}" class="btn btn-outline-warning">
                        <i class="fas fa-history me-2"></i>تاريخ المدفوعات
                    </a>
                @endif
                @if ($purchase->status === 'completed')
                    <a href="{{ route('admin.purchase-returns.create', ['purchase_id' => $purchase->id]) }}"
                        class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-2"></i>إرجاع منتجات
                    </a>
                @endif
                <a href="{{ user_route('purchases.print', $purchase) }}" class="btn btn-outline-info" target="_blank">
                    <i class="fas fa-print me-2"></i>طباعة
                </a>
            </div>
        </div>

        <!-- Distribution Status Alert -->
        @if (!$purchase->is_distributed && $purchase->status === 'pending_distribution')
            <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle me-3 fs-4"></i>
                    <div class="flex-grow-1">
                        <h5 class="alert-heading mb-1">يتطلب توزيع المنتجات</h5>
                        <p class="mb-2">هذه المشتريات تحتاج إلى توزيع المنتجات على الفروع والمخازن لإكمال العملية.</p>
                        <a href="{{ route('admin.purchases.distribute', $purchase) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-boxes me-2"></i>توزيع المنتجات الآن
                        </a>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        <!-- Status Alert -->
        @if ($purchase->status === 'pending')
            <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> هذه العملية قيد الانتظار ويمكن تعديلها أو إكمالها.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @elseif($purchase->status === 'completed')
            <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <strong>مكتمل:</strong> تم إكمال هذه العملية بنجاح.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            @if ($purchase->actual_remaining_amount > 0)
                <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه دفع:</strong> يوجد مبلغ متبقي
                    {{ format_currency($purchase->actual_remaining_amount) }} لهذه العملية.
                    <a href="{{ user_route('supplier-payments.create', $purchase) }}"
                        class="btn btn-warning btn-sm ms-2">
                        <i class="fas fa-money-bill-wave me-1"></i>دفع الآن
                    </a>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif
        @elseif($purchase->status === 'cancelled')
            <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-times-circle me-2"></i>
                <strong>ملغي:</strong> تم إلغاء هذه العملية.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    عدد المنتجات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $purchase->items->count() }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-boxes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    إجمالي الكمية
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ $purchase->items->sum('quantity') }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-cubes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    إجمالي القيمة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ format_currency($purchase->total_amount ?? 0) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    المبلغ المتبقي
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ format_currency($purchase->actual_remaining_amount ?? 0) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchase Details -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-info-circle me-2"></i>معلومات الشراء
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">رقم الفاتورة:</strong>
                            </div>
                            <div class="col-sm-8">
                                <span class="badge bg-primary px-3 py-2">{{ $purchase->invoice_number }}</span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">المورد:</strong>
                            </div>
                            <div class="col-sm-8">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-info text-white me-2">
                                        <i class="fas fa-truck"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ $purchase->supplier->name }}</div>
                                        <small
                                            class="text-muted">{{ $purchase->supplier->phone ?? 'لا يوجد هاتف' }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">الفرع:</strong>
                            </div>
                            <div class="col-sm-8">
                                <span
                                    class="badge bg-light text-dark border">{{ $purchase->branch ? $purchase->branch->name : 'غير محدد' }}</span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">التاريخ:</strong>
                            </div>
                            <div class="col-sm-8">
                                <div class="text-dark">
                                    {{ \Carbon\Carbon::parse($purchase->purchase_date)->format('d/m/Y') }}</div>
                                <small
                                    class="text-muted">{{ \Carbon\Carbon::parse($purchase->purchase_date)->format('h:i A') }}</small>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4">
                                <strong class="text-muted">الحالة:</strong>
                            </div>
                            <div class="col-sm-8">
                                @if ($purchase->status === 'completed')
                                    <span class="badge bg-success-soft text-success px-3 py-2">
                                        <i class="fas fa-check-circle me-1"></i>مكتمل
                                    </span>
                                    @if ($purchase->is_distributed && $purchase->distributed_at)
                                        <div class="small text-muted mt-1">
                                            تم التوزيع: {{ $purchase->distributed_at }}
                                        </div>
                                    @endif
                                @elseif($purchase->status === 'cancelled')
                                    <span class="badge bg-danger-soft text-danger px-3 py-2">
                                        <i class="fas fa-times-circle me-1"></i>ملغي
                                    </span>
                                @elseif($purchase->status === 'pending_distribution')
                                    <span class="badge bg-info-soft text-info px-3 py-2">
                                        <i class="fas fa-boxes me-1"></i>في انتظار التوزيع
                                    </span>
                                @else
                                    <span class="badge bg-warning-soft text-warning px-3 py-2">
                                        <i class="fas fa-clock me-1"></i>قيد الانتظار
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-calculator me-2"></i>ملخص مالي
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-6">
                                <strong class="text-muted">المجموع الفرعي:</strong>
                            </div>
                            <div class="col-sm-6 text-end">
                                <span
                                    class="fw-bold text-dark">{{ format_currency($purchase->total_amount ?? 0) }}</span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-6">
                                <strong class="text-muted">الخصم:</strong>
                            </div>
                            <div class="col-sm-6 text-end">
                                <span class="text-danger">
                                    @if ($purchase->discount_type === 'percentage')
                                        {{ $purchase->discount_value }}%
                                        ({{ format_currency($purchase->discount_amount) }})
                                    @else
                                        {{ format_currency($purchase->discount_amount ?? 0) }}
                                    @endif
                                </span>
                            </div>
                        </div>
                        <hr>
                        <div class="row mb-3">
                            <div class="col-sm-6">
                                <strong class="text-primary">المجموع النهائي:</strong>
                            </div>
                            <div class="col-sm-6 text-end">
                                <span
                                    class="fw-bold text-primary h5">{{ format_currency($purchase->final_amount) }}</span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-6">
                                <strong class="text-success">المبلغ المدفوع:</strong>
                            </div>
                            <div class="col-sm-6 text-end">
                                <span
                                    class="fw-bold text-success">{{ format_currency($purchase->paid_amount ?? 0) }}</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <strong class="text-warning">المبلغ المتبقي:</strong>
                            </div>
                            <div class="col-sm-6 text-end">
                                <span
                                    class="fw-bold text-warning">{{ format_currency($purchase->actual_remaining_amount ?? 0) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-boxes me-2"></i>المنتجات المشتراة
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th class="border-0 fw-bold">المنتج</th>
                                <th class="border-0 fw-bold text-center">الكمية</th>
                                <th class="border-0 fw-bold text-center">سعر التكلفة</th>
                                <th class="border-0 fw-bold text-center">المجموع</th>
                                @if ($purchase->is_distributed)
                                    <th class="border-0 fw-bold text-center">موزع إلى</th>
                                @endif
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($purchase->items as $item)
                                <tr class="border-bottom">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-secondary text-white me-3">
                                                <i class="fas fa-box"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold text-dark">{{ $item->product->name }}</div>
                                                <small
                                                    class="text-muted">{{ $item->product->sku ?? 'لا يوجد رمز' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span
                                            class="badge bg-light text-dark border px-3 py-2">{{ $item->quantity }}</span>
                                    </td>
                                    <td class="text-center">
                                        <span
                                            class="fw-semibold text-info">{{ format_currency($item->cost_price ?? 0) }}</span>
                                    </td>
                                    <td class="text-center">
                                        <span
                                            class="fw-bold text-success">{{ format_currency($item->total_price ?? 0) }}</span>
                                    </td>
                                    @if ($purchase->is_distributed)
                                        <td class="text-center">
                                            @if ($item->is_distributed && $item->distribution_location_type && $item->distribution_location_id)
                                                @if ($item->distribution_location_type === 'branch')
                                                    @php
                                                        $branch = \App\Models\Branch::find(
                                                            $item->distribution_location_id,
                                                        );
                                                    @endphp
                                                    <span class="badge bg-primary-soft text-primary px-3 py-2">
                                                        <i
                                                            class="fas fa-building me-1"></i>{{ $branch->name ?? 'فرع غير معروف' }}
                                                    </span>
                                                @else
                                                    @php
                                                        $store = \App\Models\Store::find(
                                                            $item->distribution_location_id,
                                                        );
                                                    @endphp
                                                    <span class="badge bg-info-soft text-info px-3 py-2">
                                                        <i
                                                            class="fas fa-warehouse me-1"></i>{{ $store->name ?? 'مخزن غير معروف' }}
                                                    </span>
                                                @endif
                                            @else
                                                <span class="badge bg-warning-soft text-warning px-3 py-2">
                                                    <i class="fas fa-clock me-1"></i>لم يتم التوزيع
                                                </span>
                                            @endif
                                        </td>
                                    @endif
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="{{ $purchase->is_distributed ? '5' : '4' }}"
                                        class="text-center py-5">
                                        <div class="empty-state">
                                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">لا توجد منتجات</h5>
                                            <p class="text-muted">لم يتم إضافة أي منتجات لهذه العملية</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="{{ $purchase->is_distributed ? '4' : '3' }}"
                                    class="text-end fw-bold text-primary">المجموع الكلي:</th>
                                <th class="text-center">
                                    <span
                                        class="fw-bold text-primary h5">{{ format_currency($purchase->total_amount ?? 0) }}</span>
                                </th>
                                @if ($purchase->is_distributed)
                                    <th></th>
                                @endif
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- Actions -->
        @if ($purchase->status === 'pending')
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-cogs me-2"></i>إجراءات العملية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-3 justify-content-center">
                        <button type="button" class="btn btn-success btn-lg"
                            onclick="completePurchase({{ $purchase->id }})">
                            <i class="fas fa-check-circle me-2"></i>إكمال الشراء
                        </button>
                        <button type="button" class="btn btn-danger btn-lg"
                            onclick="cancelPurchase({{ $purchase->id }})">
                            <i class="fas fa-times-circle me-2"></i>إلغاء الشراء
                        </button>
                    </div>
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            بعد إكمال العملية لن يمكن تعديلها أو إلغاؤها
                        </small>
                    </div>
                </div>
            </div>
        @endif

        <!-- Purchase Returns -->
        @if ($purchase->status === 'completed' && $purchase->returns->count() > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-secondary">
                        <i class="fas fa-undo me-2"></i>مرتجعات هذه العملية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>رقم المرتجع</th>
                                    <th>تاريخ المرتجع</th>
                                    <th>نوع المرتجع</th>
                                    <th>المبلغ المرتجع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($purchase->returns as $return)
                                    <tr>
                                        <td>
                                            <a href="{{ route('admin.purchase-returns.show', $return) }}"
                                                class="text-decoration-none">
                                                {{ $return->return_number }}
                                            </a>
                                        </td>
                                        <td>{{ $return->return_date->format('Y-m-d') }}</td>
                                        <td>
                                            @if ($return->return_type == 'full')
                                                <span class="badge bg-primary">مرتجع كامل</span>
                                            @else
                                                <span class="badge bg-secondary">مرتجع جزئي</span>
                                            @endif
                                        </td>
                                        <td>{{ number_format($return->refund_amount, 2) }} ج.م</td>
                                        <td>
                                            @switch($return->status)
                                                @case('pending')
                                                    <span class="badge bg-warning">في الانتظار</span>
                                                @break

                                                @case('approved')
                                                    <span class="badge bg-info">معتمد</span>
                                                @break

                                                @case('completed')
                                                    <span class="badge bg-success">مكتمل</span>
                                                @break

                                                @case('cancelled')
                                                    <span class="badge bg-danger">ملغي</span>
                                                @break
                                            @endswitch
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.purchase-returns.show', $return) }}"
                                                class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif
    </div>

    @push('scripts')
        <script>
            function completePurchase(purchaseId) {
                if (confirm('هل أنت متأكد من إكمال هذه العملية؟\nلن يمكن التراجع عن هذا الإجراء.')) {
                    fetch(`{{ url('') }}/admin/purchases/${purchaseId}/complete`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                'Content-Type': 'application/json',
                            },
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('حدث خطأ أثناء إكمال العملية');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('حدث خطأ أثناء إكمال العملية');
                        });
                }
            }

            function cancelPurchase(purchaseId) {
                if (confirm('هل أنت متأكد من إلغاء هذه العملية؟\nلن يمكن التراجع عن هذا الإجراء.')) {
                    fetch(`{{ url('') }}/admin/purchases/${purchaseId}/cancel`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                'Content-Type': 'application/json',
                            },
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('حدث خطأ أثناء إلغاء العملية');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('حدث خطأ أثناء إلغاء العملية');
                        });
                }
            }
        </script>
    @endpush

    @push('styles')
        <style>
            .avatar-circle {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
            }

            .border-left-primary {
                border-left: 0.25rem solid #4e73df !important;
            }

            .border-left-success {
                border-left: 0.25rem solid #1cc88a !important;
            }

            .border-left-warning {
                border-left: 0.25rem solid #f6c23e !important;
            }

            .border-left-info {
                border-left: 0.25rem solid #36b9cc !important;
            }

            .bg-success-soft {
                background-color: rgba(28, 200, 138, 0.1) !important;
            }

            .bg-danger-soft {
                background-color: rgba(231, 74, 59, 0.1) !important;
            }

            .bg-warning-soft {
                background-color: rgba(246, 194, 62, 0.1) !important;
            }

            .empty-state {
                padding: 3rem 1rem;
            }

            .table-hover tbody tr:hover {
                background-color: rgba(0, 0, 0, 0.02);
            }

            @media print {

                .btn,
                .card-header,
                .alert {
                    display: none !important;
                }

                .card {
                    border: none !important;
                    box-shadow: none !important;
                }
            }
        </style>
    @endpush
</x-app-layout>
