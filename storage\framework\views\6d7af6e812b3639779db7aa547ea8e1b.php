<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['product', 'showTransferButton' => false, 'compact' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['product', 'showTransferButton' => false, 'compact' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="product-availability <?php echo e($compact ? 'compact' : ''); ?>">
    <!-- Main availability status -->
    <div class="availability-status mb-2">
        <span class="badge bg-<?php echo e($product->availability_class); ?> <?php echo e($compact ? 'small' : ''); ?>">
            <i
                class="fas <?php echo e($product->availability_status === 'local' ? 'fa-check' : ($product->availability_status === 'transferable' ? 'fa-exchange-alt' : 'fa-times')); ?>"></i>
            <?php echo e($product->availability_text); ?>

        </span>
    </div>

    <?php if(!$compact): ?>
        <!-- Detailed availability breakdown -->
        <div class="availability-details">
            <!-- Local availability -->
            <?php if($product->local_quantity > 0): ?>
                <div class="location-availability local mb-1">
                    <small class="text-success">
                        <i class="fas fa-map-marker-alt"></i>
                        <strong>محلياً:</strong> <?php echo e($product->local_quantity); ?> قطعة
                    </small>
                </div>
            <?php endif; ?>

            <!-- Available locations -->
            <?php if($product->all_available_locations && $product->all_available_locations->count() > 0): ?>
                <div class="available-locations mb-1">
                    <small class="text-warning">
                        <i class="fas fa-map-marker-alt"></i>
                        <strong>متوفر في:</strong>
                    </small>
                    <div class="ms-3">
                        <?php $__currentLoopData = $product->all_available_locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="small text-muted d-flex justify-content-between align-items-center">
                                <span>
                                    <?php if($location['location_type'] === 'branch'): ?>
                                        <i class="fas fa-building text-primary"></i>
                                    <?php else: ?>
                                        <i class="fas fa-warehouse text-info"></i>
                                    <?php endif; ?>
                                    <?php echo e($location['location_display']); ?>

                                </span>
                                <span class="badge bg-light text-dark"><?php echo e($location['quantity']); ?></span>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Total quantity -->
            <?php if($product->total_quantity > 0): ?>
                <div class="total-availability">
                    <small class="text-primary">
                        <i class="fas fa-calculator"></i>
                        <strong>الإجمالي:</strong> <?php echo e($product->total_quantity); ?> قطعة
                    </small>
                </div>
            <?php endif; ?>
        </div>

        <!-- Transfer button -->
        <?php if($showTransferButton && $product->availability_status === 'transferable' && $product->local_quantity == 0): ?>
            <div class="transfer-action mt-2">
                <button class="btn btn-sm btn-outline-warning" onclick="requestTransfer(<?php echo e($product->id); ?>, 1)"
                    title="طلب نقل من موقع آخر">
                    <i class="fas fa-exchange-alt"></i>
                    طلب نقل
                </button>
            </div>
        <?php endif; ?>
    <?php else: ?>
        <!-- Compact view - just show quantities -->
        <div class="availability-compact">
            <?php if($product->local_quantity > 0): ?>
                <small class="text-success">محلياً: <?php echo e($product->local_quantity); ?></small>
            <?php endif; ?>
            <?php if($product->availability_status === 'transferable'): ?>
                <small class="text-warning">| إجمالي: <?php echo e($product->total_quantity); ?></small>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<?php if(!$compact): ?>
    <style>
        .product-availability {
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 0.75rem;
            background-color: #f8f9fa;
        }

        .product-availability.compact {
            border: none;
            padding: 0.25rem;
            background-color: transparent;
        }

        .location-availability.local {
            border-left: 3px solid #198754;
            padding-left: 0.5rem;
        }

        .other-branches-availability {
            border-left: 3px solid #ffc107;
            padding-left: 0.5rem;
        }

        .stores-availability {
            border-left: 3px solid #0dcaf0;
            padding-left: 0.5rem;
        }

        .total-availability {
            border-top: 1px solid #dee2e6;
            padding-top: 0.5rem;
            margin-top: 0.5rem;
        }
    </style>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\components\product-availability.blade.php ENDPATH**/ ?>