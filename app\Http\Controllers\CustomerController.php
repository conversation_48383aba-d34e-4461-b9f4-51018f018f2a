<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Helpers\AlertHelper;

class CustomerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Customer::withCount('sales')
            ->with(['sales' => function ($q) {
                $q->select('customer_id', 'total_amount', 'discount_amount', 'paid_amount');
            }]);

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        $customers = $query->latest()->paginate(15);

        // Calculate remaining amounts for each customer
        foreach ($customers as $customer) {
            $customer->total_sales_amount = $customer->sales->sum('total_amount');
            $customer->total_paid_amount = $customer->sales->sum('paid_amount');
            $customer->total_discount_amount = $customer->sales->sum('discount_amount');
            $customer->total_remaining_amount = $customer->sales->sum(function ($sale) {
                return ($sale->total_amount - $sale->discount_amount) - $sale->paid_amount;
            });
        }

        return view('customers.index', compact('customers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('customers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:customers',
            'contact_person' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'opening_balance' => 'nullable|numeric|min:0',
        ]);

        DB::beginTransaction();
        try {
            $customer = Customer::create($validated);

            // Create account for the customer
            $account = $customer->account()->create([
                'name' => $customer->name,
                'code' => $customer->code ?? 'CUS-' . str_pad($customer->id, 6, '0', STR_PAD_LEFT),
                'type' => 'customer',
                'opening_balance' => $customer->opening_balance ?? 0,
                'current_balance' => $customer->opening_balance ?? 0,
            ]);

            DB::commit();
            AlertHelper::success('تم إضافة العميل بنجاح');

            // Redirect to appropriate customers index based on user role
            if (auth()->user()->isAdmin()) {
                return redirect()->route('admin.customers.index');
            } else {
                return redirect()->route('seller.customers.index');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            AlertHelper::error('حدث خطأ أثناء إضافة العميل');
            return back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, Customer $customer)
    {
        $customer->load(['sales' => function ($query) {
            $query->latest()->with(['items.product', 'branch', 'payments']);
        }]);

        // Get all products purchased by this customer for search
        $productSearch = $request->get('product_search');
        $purchasedProducts = collect();

        if ($productSearch) {
            // Search in products purchased by this customer
            $purchasedProducts = $customer->sales()
                ->with(['items.product'])
                ->get()
                ->flatMap(function ($sale) {
                    return $sale->items;
                })
                ->filter(function ($item) use ($productSearch) {
                    return stripos($item->product->name, $productSearch) !== false ||
                        stripos($item->product->sku, $productSearch) !== false;
                })
                ->groupBy('product_id')
                ->map(function ($items) {
                    $product = $items->first()->product;
                    $totalQuantity = $items->sum('quantity');
                    $totalAmount = $items->sum('subtotal');
                    $avgPrice = $items->avg('price');
                    $lastPurchase = $items->sortByDesc('created_at')->first();

                    return (object) [
                        'product' => $product,
                        'total_quantity' => $totalQuantity,
                        'total_amount' => $totalAmount,
                        'average_price' => $avgPrice,
                        'last_purchase_date' => $lastPurchase->created_at,
                        'purchase_count' => $items->count()
                    ];
                })
                ->sortByDesc('total_amount');
        }

        return view('customers.show', compact('customer', 'purchasedProducts', 'productSearch'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Customer $customer)
    {
        return view('customers.edit', compact('customer'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Customer $customer)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email,' . $customer->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string'
        ]);

        $customer->update($validated);

        return redirect()->route('customers.index')
            ->with('success', 'تم تحديث بيانات العميل بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Customer $customer)
    {
        // Only admin users can delete customers
        if (!auth()->user()->isAdmin()) {
            abort(403, 'غير مصرح لك بحذف العملاء');
        }

        if ($customer->sales()->count() > 0) {
            AlertHelper::error('لا يمكن حذف العميل لوجود مبيعات مرتبطة به');
            return back();
        }

        $customer->delete();

        AlertHelper::success('تم حذف العميل بنجاح');

        // Use user_route to redirect to appropriate customers index
        if (auth()->user()->isAdmin()) {
            return redirect()->route('admin.customers.index');
        }

        return redirect()->route('customers.index');
    }
}
