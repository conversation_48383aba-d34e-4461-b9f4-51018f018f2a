<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">العملاء</h5>
                        <a href="<?php echo e(user_route('customers.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة عميل
                        </a>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo e(user_route('customers.index')); ?>" method="GET" class="mb-4">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <input type="text" name="search" class="form-control" placeholder="بحث..."
                                            value="<?php echo e(request('search')); ?>">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>رقم الهاتف</th>
                                        <th>عدد المشتريات</th>
                                        <th>إجمالي المشتريات</th>
                                        <th>المبلغ المتبقي</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle bg-primary text-white me-2 d-flex align-items-center justify-content-center"
                                                        style="width: 40px; height: 40px; border-radius: 50%; font-weight: bold;">
                                                        <?php echo e(strtoupper(substr($customer->name, 0, 1))); ?>

                                                    </div>
                                                    <div>
                                                        <strong><?php echo e($customer->name); ?></strong>
                                                        <?php if($customer->address): ?>
                                                            <br><small
                                                                class="text-muted"><?php echo e(Str::limit($customer->address, 30)); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if($customer->email): ?>
                                                    <a href="mailto:<?php echo e($customer->email); ?>"
                                                        class="text-decoration-none">
                                                        <?php echo e($customer->email); ?>

                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($customer->phone): ?>
                                                    <a href="tel:<?php echo e($customer->phone); ?>" class="text-decoration-none">
                                                        <i class="fas fa-phone text-success me-1"></i>
                                                        <?php echo e($customer->phone); ?>

                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info fs-6"><?php echo e($customer->sales_count); ?></span>
                                            </td>
                                            <td>
                                                <strong
                                                    class="text-primary"><?php echo e(number_format($customer->total_sales_amount ?? 0, 2)); ?>

                                                    ج.م</strong>
                                            </td>
                                            <td>
                                                <?php if(($customer->total_remaining_amount ?? 0) > 0): ?>
                                                    <span
                                                        class="badge bg-warning fs-6"><?php echo e(number_format($customer->total_remaining_amount, 2)); ?>

                                                        ج.م</span>
                                                <?php else: ?>
                                                    <span class="badge bg-success fs-6">مسدد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo e(user_route('customers.show', $customer)); ?>"
                                                        class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(user_route('customers.edit', $customer)); ?>"
                                                        class="btn btn-sm btn-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if(($customer->total_remaining_amount ?? 0) > 0): ?>
                                                        <a href="<?php echo e(user_route('customer-payments.create', ['customer_id' => $customer->id])); ?>"
                                                            class="btn btn-sm btn-warning" title="تسجيل دفعة">
                                                            <i class="fas fa-money-bill-wave"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if(auth()->user()->isAdmin()): ?>
                                                        <form action="<?php echo e(user_route('customers.destroy', $customer)); ?>"
                                                            method="POST" class="d-inline">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit" class="btn btn-sm btn-danger"
                                                                onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')"
                                                                title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="fas fa-users fa-3x mb-3"></i>
                                                    <p>لا يوجد عملاء</p>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-4">
                            <?php echo e($customers->links()); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\customers\index.blade.php ENDPATH**/ ?>