<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة شراء - <?php echo e($purchase->invoice_number); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            body {
                padding: 15px;
                font-size: 14px;
            }

            .no-print {
                display: none !important;
            }

            .page-break {
                page-break-before: always;
            }
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.4;
            color: #000;
        }

        .invoice-header {
            border-bottom: 2px solid #000;
            margin-bottom: 20px;
            padding-bottom: 15px;
        }

        .company-info {
            text-align: center;
            margin-bottom: 20px;
        }

        .company-name {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .invoice-title {
            background: #000;
            color: white;
            padding: 8px 15px;
            display: inline-block;
            font-weight: bold;
            margin: 15px 0;
        }

        .invoice-details {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #000;
        }

        .table {
            border-collapse: collapse;
            width: 100%;
        }

        .table th {
            background: #000 !important;
            color: white !important;
            font-weight: bold;
            text-align: center;
            padding: 10px 8px;
            border: 1px solid #000;
        }

        .table td {
            padding: 8px;
            border: 1px solid #000;
            vertical-align: middle;
        }

        .total-row {
            font-weight: bold;
            background: #000 !important;
            color: white !important;
        }

        .total-row td {
            border: 1px solid #000 !important;
            padding: 10px 8px;
        }

        .footer-note {
            margin-top: 20px;
            padding: 15px;
            border-top: 2px solid #000;
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="no-print mb-3">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i> طباعة
            </button>
            <a href="<?php echo e(user_route('purchases.show', $purchase)); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> عودة
            </a>
        </div>

        <div class="invoice-header">
            <div class="company-info">
                <?php
                    $settings = \App\Models\Setting::first();
                ?>
                <div class="company-name"><?php echo e($settings->company_name ?? 'نظام نقاط البيع'); ?></div>
                <?php if($settings->company_address): ?>
                    <p class="mb-2"><?php echo e($settings->company_address); ?></p>
                <?php endif; ?>
                <div class="d-flex justify-content-center gap-3 mb-2">
                    <?php if($settings->company_phone): ?>
                        <span><?php echo e($settings->company_phone); ?></span>
                    <?php endif; ?>
                    <?php if($settings->company_email): ?>
                        <span><?php echo e($settings->company_email); ?></span>
                    <?php endif; ?>
                </div>
                <?php if($settings->company_tax_number): ?>
                    <p class="mb-0">الرقم الضريبي: <?php echo e($settings->company_tax_number); ?></p>
                <?php endif; ?>

                <div class="invoice-title">
                    فاتورة شراء رقم: <?php echo e($purchase->invoice_number); ?>

                </div>
            </div>
            <div class="row invoice-details">
                <div class="col-6">
                    <h5 class="mb-3">معلومات الفاتورة</h5>
                    <div class="mb-2"><strong>رقم الفاتورة:</strong> <?php echo e($purchase->invoice_number); ?></div>
                    <div class="mb-2"><strong>التاريخ:</strong> <?php echo e($purchase->purchase_date); ?></div>
                </div>
                <div class="col-6">
                    <h5 class="mb-3">معلومات المورد</h5>
                    <div class="mb-2"><strong>الاسم:</strong> <?php echo e($purchase->supplier->name); ?></div>
                    <div class="mb-2"><strong>البريد الإلكتروني:</strong>
                        <?php echo e($purchase->supplier->email ?: 'غير متوفر'); ?></div>
                    <div class="mb-2"><strong>رقم الهاتف:</strong> <?php echo e($purchase->supplier->phone ?: 'غير متوفر'); ?>

                    </div>
                    <div class="mb-2"><strong>العنوان:</strong> <?php echo e($purchase->supplier->address ?: 'غير متوفر'); ?>

                    </div>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>سعر التكلفة</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $purchase->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($index + 1); ?></td>
                            <td><?php echo e($item->product->name); ?></td>
                            <td><?php echo e($item->quantity); ?></td>
                            <td><?php echo e(number_format($item->cost_price, 2)); ?></td>
                            <td><?php echo e(number_format($item->total_price, 2)); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4" class="text-start">المجموع</td>
                        <td><?php echo e(number_format($purchase->total_amount, 2)); ?></td>
                    </tr>
                    <?php if($purchase->discount_amount > 0): ?>
                        <tr>
                            <td colspan="4" class="text-start">الخصم</td>
                            <td>
                                <?php if($purchase->discount_type === 'percentage'): ?>
                                    <?php echo e($purchase->discount_value); ?>%
                                    (<?php echo e(number_format($purchase->discount_amount, 2)); ?>)
                                <?php else: ?>
                                    <?php echo e(number_format($purchase->discount_amount, 2)); ?>

                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                    <tr>
                        <td colspan="4" class="text-start">المبلغ المدفوع</td>
                        <td><?php echo e(number_format($purchase->paid_amount, 2)); ?></td>
                    </tr>
                    <tr class="total-row">
                        <td colspan="4" class="text-start">المبلغ المتبقي</td>
                        <td><?php echo e(number_format($purchase->remaining_amount, 2)); ?></td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <?php if($purchase->notes): ?>
            <div style="margin-top: 20px; padding: 15px; border: 1px solid #000;">
                <h6 class="mb-2">ملاحظات</h6>
                <p class="mb-0"><?php echo e($purchase->notes); ?></p>
            </div>
        <?php endif; ?>

        <div class="footer-note">
            <div class="row align-items-center">
                <div class="col-6">
                    <strong>شكراً لتعاملكم معنا</strong><br>
                    <small>تم إنشاء هذه الفاتورة إلكترونياً</small>
                </div>
                <div class="col-6 text-end">
                    <div class="d-inline-block">
                        <strong>التوقيع</strong><br>
                        <small><?php echo e(auth()->user()->name); ?></small><br>
                        <div style="border-top: 2px solid #000; width: 150px; margin-top: 20px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
<?php /**PATH D:\my-pos-app\resources\views/purchases/print.blade.php ENDPATH**/ ?>