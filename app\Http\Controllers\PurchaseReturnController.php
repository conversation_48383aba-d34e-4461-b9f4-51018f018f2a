<?php

namespace App\Http\Controllers;

use App\Models\Purchase;
use App\Models\PurchaseReturn;
use App\Models\PurchaseReturnItem;
use App\Models\Supplier;
use App\Models\Branch;
use App\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class PurchaseReturnController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = PurchaseReturn::with(['purchase', 'supplier', 'branch', 'store', 'user'])
            ->orderBy('created_at', 'desc');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by supplier
        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('return_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('return_date', '<=', $request->date_to);
        }

        // Search by return number or purchase ID
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('return_number', 'like', "%{$search}%")
                    ->orWhereHas('purchase', function ($purchaseQuery) use ($search) {
                        $purchaseQuery->where('id', 'like', "%{$search}%");
                    });
            });
        }

        $returns = $query->paginate(15);
        $suppliers = Supplier::where('is_active', true)->get();

        return view('admin.purchase-returns.index', compact('returns', 'suppliers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $purchase = null;

        if ($request->filled('purchase_id')) {
            $purchase = Purchase::with(['items.product', 'supplier', 'branch', 'store'])
                ->findOrFail($request->purchase_id);
        }

        $suppliers = Supplier::where('is_active', true)->get();
        $branches = Branch::where('is_active', true)->get();
        $stores = Store::where('is_active', true)->get();

        return view('admin.purchase-returns.create', compact('purchase', 'suppliers', 'branches', 'stores'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'purchase_id' => 'required|exists:purchases,id',
            'return_date' => 'required|date',
            'return_type' => 'required|in:full,partial',
            'reason' => 'required|string|max:1000',
            'notes' => 'nullable|string|max:1000',
            'refund_amount' => 'required|numeric|min:0',
            'items' => 'required|array|min:1',
            'items.*.purchase_item_id' => 'required|exists:purchase_items,id',
            'items.*.quantity_returned' => 'required|numeric|min:0.01',
            'items.*.condition' => 'required|in:good,damaged,expired,defective',
            'items.*.item_notes' => 'nullable|string|max:500'
        ]);

        try {
            DB::beginTransaction();

            $purchase = Purchase::with(['items', 'supplier', 'branch', 'store'])->findOrFail($validated['purchase_id']);

            // Create the purchase return
            $purchaseReturn = PurchaseReturn::create([
                'purchase_id' => $purchase->id,
                'supplier_id' => $purchase->supplier_id,
                'branch_id' => $purchase->branch_id,
                'store_id' => $purchase->store_id,
                'user_id' => Auth::id(),
                'total_amount' => 0, // Will be calculated below
                'refund_amount' => $validated['refund_amount'],
                'status' => 'pending',
                'return_type' => $validated['return_type'],
                'reason' => $validated['reason'],
                'notes' => $validated['notes'],
                'return_date' => $validated['return_date']
            ]);

            $totalAmount = 0;

            // Create return items
            foreach ($validated['items'] as $itemData) {
                $purchaseItem = $purchase->items()->findOrFail($itemData['purchase_item_id']);

                // Validate return quantity
                $alreadyReturned = PurchaseReturnItem::where('purchase_item_id', $purchaseItem->id)
                    ->sum('quantity_returned');
                $availableQuantity = $purchaseItem->quantity - $alreadyReturned;

                if ($itemData['quantity_returned'] > $availableQuantity) {
                    throw new \Exception("الكمية المرتجعة ({$itemData['quantity_returned']}) تتجاوز الكمية المتاحة ({$availableQuantity}) للمنتج {$purchaseItem->product->name}");
                }

                $totalCost = $itemData['quantity_returned'] * $purchaseItem->cost_price;
                $totalAmount += $totalCost;

                PurchaseReturnItem::create([
                    'purchase_return_id' => $purchaseReturn->id,
                    'purchase_item_id' => $purchaseItem->id,
                    'product_id' => $purchaseItem->product_id,
                    'quantity_returned' => $itemData['quantity_returned'],
                    'original_quantity' => $purchaseItem->quantity,
                    'cost_price' => $purchaseItem->cost_price,
                    'total_cost' => $totalCost,
                    'condition' => $itemData['condition'],
                    'item_notes' => $itemData['item_notes'] ?? null,
                    'inventory_adjusted' => false
                ]);
            }

            // Update total amount
            $purchaseReturn->update(['total_amount' => $totalAmount]);

            DB::commit();

            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('success', 'تم إنشاء مرتجع الشراء بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(PurchaseReturn $purchaseReturn)
    {
        $purchaseReturn->load([
            'purchase.items.product',
            'supplier',
            'branch',
            'store',
            'user',
            'approvedBy',
            'items.product',
            'items.purchaseItem',
            'transactions'
        ]);

        return view('admin.purchase-returns.show', compact('purchaseReturn'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PurchaseReturn $purchaseReturn)
    {
        if (!$purchaseReturn->canBeEdited()) {
            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('error', 'لا يمكن تعديل هذا المرتجع في حالته الحالية');
        }

        $purchaseReturn->load(['purchase.items.product', 'items']);
        $suppliers = Supplier::where('is_active', true)->get();
        $branches = Branch::where('is_active', true)->get();
        $stores = Store::where('is_active', true)->get();

        return view('admin.purchase-returns.edit', compact('purchaseReturn', 'suppliers', 'branches', 'stores'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PurchaseReturn $purchaseReturn)
    {
        if (!$purchaseReturn->canBeEdited()) {
            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('error', 'لا يمكن تعديل هذا المرتجع في حالته الحالية');
        }

        $validated = $request->validate([
            'return_date' => 'required|date',
            'return_type' => 'required|in:full,partial',
            'reason' => 'required|string|max:1000',
            'notes' => 'nullable|string|max:1000',
            'refund_amount' => 'required|numeric|min:0',
            'items' => 'required|array|min:1',
            'items.*.purchase_item_id' => 'required|exists:purchase_items,id',
            'items.*.quantity_returned' => 'required|numeric|min:0.01',
            'items.*.condition' => 'required|in:good,damaged,expired,defective',
            'items.*.item_notes' => 'nullable|string|max:500'
        ]);

        try {
            DB::beginTransaction();

            // Update purchase return basic info
            $purchaseReturn->update([
                'return_date' => $validated['return_date'],
                'return_type' => $validated['return_type'],
                'reason' => $validated['reason'],
                'notes' => $validated['notes'],
                'refund_amount' => $validated['refund_amount']
            ]);

            // Delete existing items and recreate them
            $purchaseReturn->items()->delete();

            $totalAmount = 0;
            $purchase = $purchaseReturn->purchase;

            // Create updated return items
            foreach ($validated['items'] as $itemData) {
                $purchaseItem = $purchase->items()->findOrFail($itemData['purchase_item_id']);

                // Validate return quantity (excluding current return)
                $alreadyReturned = PurchaseReturnItem::where('purchase_item_id', $purchaseItem->id)
                    ->whereHas('purchaseReturn', function ($q) use ($purchaseReturn) {
                        $q->where('id', '!=', $purchaseReturn->id);
                    })
                    ->sum('quantity_returned');
                $availableQuantity = $purchaseItem->quantity - $alreadyReturned;

                if ($itemData['quantity_returned'] > $availableQuantity) {
                    throw new \Exception("الكمية المرتجعة ({$itemData['quantity_returned']}) تتجاوز الكمية المتاحة ({$availableQuantity}) للمنتج {$purchaseItem->product->name}");
                }

                $totalCost = $itemData['quantity_returned'] * $purchaseItem->cost_price;
                $totalAmount += $totalCost;

                PurchaseReturnItem::create([
                    'purchase_return_id' => $purchaseReturn->id,
                    'purchase_item_id' => $purchaseItem->id,
                    'product_id' => $purchaseItem->product_id,
                    'quantity_returned' => $itemData['quantity_returned'],
                    'original_quantity' => $purchaseItem->quantity,
                    'cost_price' => $purchaseItem->cost_price,
                    'total_cost' => $totalCost,
                    'condition' => $itemData['condition'],
                    'item_notes' => $itemData['item_notes'] ?? null,
                    'inventory_adjusted' => false
                ]);
            }

            // Update total amount
            $purchaseReturn->update(['total_amount' => $totalAmount]);

            DB::commit();

            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('success', 'تم تحديث مرتجع الشراء بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PurchaseReturn $purchaseReturn)
    {
        if (!$purchaseReturn->canBeEdited()) {
            return redirect()->route('admin.purchase-returns.index')
                ->with('error', 'لا يمكن حذف هذا المرتجع في حالته الحالية');
        }

        try {
            DB::beginTransaction();

            // Delete return items first
            $purchaseReturn->items()->delete();

            // Delete the return
            $purchaseReturn->delete();

            DB::commit();

            return redirect()->route('admin.purchase-returns.index')
                ->with('success', 'تم حذف مرتجع الشراء بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('admin.purchase-returns.index')
                ->with('error', 'حدث خطأ أثناء حذف المرتجع: ' . $e->getMessage());
        }
    }

    /**
     * Approve a purchase return.
     */
    public function approve(PurchaseReturn $purchaseReturn)
    {
        if (!$purchaseReturn->canBeApproved()) {
            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('error', 'لا يمكن اعتماد هذا المرتجع في حالته الحالية');
        }

        try {
            $purchaseReturn->approve(Auth::user());

            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('success', 'تم اعتماد مرتجع الشراء بنجاح');
        } catch (\Exception $e) {
            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('error', 'حدث خطأ أثناء اعتماد المرتجع: ' . $e->getMessage());
        }
    }

    /**
     * Complete a purchase return (process inventory and financial adjustments).
     */
    public function complete(PurchaseReturn $purchaseReturn)
    {
        if (!$purchaseReturn->canBeCompleted()) {
            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('error', 'لا يمكن إتمام هذا المرتجع في حالته الحالية');
        }

        try {
            $purchaseReturn->complete();

            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('success', 'تم إتمام مرتجع الشراء بنجاح وتم تعديل المخزون والحسابات');
        } catch (\Exception $e) {
            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('error', 'حدث خطأ أثناء إتمام المرتجع: ' . $e->getMessage());
        }
    }

    /**
     * Cancel a purchase return.
     */
    public function cancel(Request $request, PurchaseReturn $purchaseReturn)
    {
        if (!$purchaseReturn->canBeCancelled()) {
            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('error', 'لا يمكن إلغاء هذا المرتجع في حالته الحالية');
        }

        $validated = $request->validate([
            'cancellation_reason' => 'required|string|max:500'
        ]);

        try {
            $purchaseReturn->cancel($validated['cancellation_reason']);

            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('success', 'تم إلغاء مرتجع الشراء بنجاح');
        } catch (\Exception $e) {
            return redirect()->route('admin.purchase-returns.show', $purchaseReturn)
                ->with('error', 'حدث خطأ أثناء إلغاء المرتجع: ' . $e->getMessage());
        }
    }

    /**
     * Get purchase details for return creation (AJAX).
     */
    public function getPurchaseDetails(Purchase $purchase)
    {
        $purchase->load(['items.product', 'supplier', 'branch', 'store']);

        // Calculate already returned quantities for each item
        $purchaseItems = $purchase->items->map(function ($item) {
            $alreadyReturned = PurchaseReturnItem::where('purchase_item_id', $item->id)
                ->sum('quantity_returned');

            $item->already_returned = $alreadyReturned;
            $item->available_for_return = $item->quantity - $alreadyReturned;

            return $item;
        });

        return response()->json([
            'purchase' => $purchase,
            'items' => $purchaseItems
        ]);
    }
}
