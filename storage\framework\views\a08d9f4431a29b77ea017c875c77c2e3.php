<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">تعديل عملية البيع: <?php echo e($sale->invoice_number); ?></h5>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo e(user_route('sales.update', $sale)); ?>" method="POST" id="saleForm">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="customer_id" class="form-label">العميل</label>
                                    <select class="form-select <?php $__errorArgs = ['customer_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="customer_id" name="customer_id" required>
                                        <option value="">اختر العميل</option>
                                        <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($customer->id); ?>"
                                                <?php echo e(old('customer_id', $sale->customer_id) == $customer->id ? 'selected' : ''); ?>>
                                                <?php echo e($customer->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['customer_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="status"
                                        name="status" required>
                                        <option value="pending"
                                            <?php echo e(old('status', $sale->status) == 'pending' ? 'selected' : ''); ?>>قيد
                                            الانتظار</option>
                                        <option value="completed"
                                            <?php echo e(old('status', $sale->status) == 'completed' ? 'selected' : ''); ?>>مكتمل
                                        </option>
                                    </select>
                                    <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-12">
                                    <h6>المنتجات</h6>
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="productsTable">
                                            <thead>
                                                <tr>
                                                    <th>المنتج</th>
                                                    <th>الكمية</th>
                                                    <th>السعر</th>
                                                    <th>المجموع</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $sale->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td>
                                                            <select class="form-select product-select"
                                                                name="products[<?php echo e($index); ?>][id]" required>
                                                                <option value="">اختر المنتج</option>
                                                                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <option value="<?php echo e($p->id); ?>"
                                                                        data-price="<?php echo e($p->price); ?>"
                                                                        data-stock="<?php echo e($p->stock); ?>"
                                                                        <?php echo e($product->id == $p->id ? 'selected' : ''); ?>>
                                                                        <?php echo e($p->name); ?> (المخزون:
                                                                        <?php echo e($p->stock); ?>)
                                                                    </option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                                        </td>
                                                        <td>
                                                            <input type="number" class="form-control quantity-input"
                                                                name="products[<?php echo e($index); ?>][quantity]"
                                                                min="1" value="<?php echo e($product->pivot->quantity); ?>"
                                                                required>
                                                        </td>
                                                        <td>
                                                            <input type="number" class="form-control price-input"
                                                                name="products[<?php echo e($index); ?>][price]"
                                                                step="0.01" value="<?php echo e($product->pivot->price); ?>"
                                                                readonly>
                                                        </td>
                                                        <td>
                                                            <input type="number" class="form-control subtotal-input"
                                                                value="<?php echo e($product->pivot->quantity * $product->pivot->price); ?>"
                                                                readonly>
                                                        </td>
                                                        <td>
                                                            <button type="button"
                                                                class="btn btn-danger remove-product">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="5">
                                                        <button type="button" class="btn btn-success" id="addProduct">
                                                            <i class="fas fa-plus"></i> إضافة منتج
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="3" class="text-end"><strong>الإجمالي:</strong></td>
                                                    <td colspan="2">
                                                        <input type="number" class="form-control" id="total"
                                                            name="total" value="<?php echo e($sale->total); ?>" readonly>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                                <a href="<?php echo e(route('sales.index')); ?>" class="btn btn-secondary">إلغاء</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const productsTable = document.getElementById('productsTable');
                const addProductBtn = document.getElementById('addProduct');
                let productCount = <?php echo e(count($sale->products)); ?>;

                // Add new product row
                addProductBtn.addEventListener('click', function() {
                    const newRow = productsTable.querySelector('tbody tr').cloneNode(true);
                    const inputs = newRow.querySelectorAll('input, select');
                    inputs.forEach(input => {
                        input.value = '';
                        input.name = input.name.replace(/\[\d+\]/, `[${productCount}]`);
                    });
                    productsTable.querySelector('tbody').appendChild(newRow);
                    productCount++;
                    updateEventListeners();
                });

                // Remove product row
                function updateEventListeners() {
                    document.querySelectorAll('.remove-product').forEach(button => {
                        button.addEventListener('click', function() {
                            if (productsTable.querySelectorAll('tbody tr').length > 1) {
                                this.closest('tr').remove();
                                calculateTotal();
                            }
                        });
                    });

                    document.querySelectorAll('.product-select').forEach(select => {
                        select.addEventListener('change', function() {
                            const option = this.options[this.selectedIndex];
                            const price = option.dataset.price;
                            const stock = option.dataset.stock;
                            const row = this.closest('tr');
                            row.querySelector('.price-input').value = price;
                            row.querySelector('.quantity-input').max = stock;
                            calculateRowTotal(row);
                        });
                    });

                    document.querySelectorAll('.quantity-input').forEach(input => {
                        input.addEventListener('input', function() {
                            calculateRowTotal(this.closest('tr'));
                        });
                    });
                }

                // Calculate row total
                function calculateRowTotal(row) {
                    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
                    const price = parseFloat(row.querySelector('.price-input').value) || 0;
                    const subtotal = quantity * price;
                    row.querySelector('.subtotal-input').value = subtotal.toFixed(2);
                    calculateTotal();
                }

                // Calculate total
                function calculateTotal() {
                    const subtotals = Array.from(document.querySelectorAll('.subtotal-input')).map(input => parseFloat(
                        input.value) || 0);
                    const total = subtotals.reduce((sum, subtotal) => sum + subtotal, 0);
                    document.getElementById('total').value = total.toFixed(2);
                }

                // Form validation
                document.getElementById('saleForm').addEventListener('submit', function(e) {
                    const products = document.querySelectorAll('.product-select');
                    let hasProducts = false;

                    products.forEach(select => {
                        if (select.value) {
                            hasProducts = true;
                        }
                    });

                    if (!hasProducts) {
                        e.preventDefault();
                        alert('يجب إضافة منتج واحد على الأقل');
                    }
                });

                // Initial event listeners
                updateEventListeners();
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\sales\edit.blade.php ENDPATH**/ ?>