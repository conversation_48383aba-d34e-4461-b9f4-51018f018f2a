<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight mb-1">
                    <?php echo e(__('إدارة المستخدمين')); ?>

                </h2>
                <p class="text-sm text-gray-600"><?php echo e(__('إدارة حسابات المستخدمين والصلاحيات')); ?></p>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal"
                    data-bs-target="#bulkActionsModal">
                    <i class="fas fa-tasks"></i> <?php echo e(__('إجراءات متعددة')); ?>

                </button>
                <a href="<?php echo e(route('admin.settings.users.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> <?php echo e(__('إضافة مستخدم جديد')); ?>

                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-6">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                        <i class="fas fa-users text-primary fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small"><?php echo e(__('إجمالي المستخدمين')); ?></div>
                                    <div class="h4 mb-0"><?php echo e($users->total()); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                        <i class="fas fa-user-check text-success fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small"><?php echo e(__('المستخدمين النشطين')); ?></div>
                                    <div class="h4 mb-0"><?php echo e($users->where('is_active', true)->count()); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                        <i class="fas fa-user-times text-warning fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small"><?php echo e(__('المستخدمين غير النشطين')); ?></div>
                                    <div class="h4 mb-0"><?php echo e($users->where('is_active', false)->count()); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                        <i class="fas fa-user-shield text-info fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small"><?php echo e(__('المديرين')); ?></div>
                                    <div class="h4 mb-0">
                                        <?php echo e($users->filter(function ($user) {return $user->role && $user->role->name === 'admin';})->count()); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Card -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0"><?php echo e(__('قائمة المستخدمين')); ?></h5>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="collapse"
                                data-bs-target="#filtersCollapse">
                                <i class="fas fa-filter"></i> <?php echo e(__('تصفية النتائج')); ?>

                            </button>
                        </div>
                    </div>
                </div>

                <!-- Advanced Filters -->
                <div class="collapse" id="filtersCollapse">
                    <div class="card-body border-bottom bg-light">
                        <form action="<?php echo e(route('admin.settings.users')); ?>" method="GET" id="filtersForm">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="search" class="form-label"><?php echo e(__('البحث العام')); ?></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" name="search" id="search" class="form-control"
                                            value="<?php echo e(request('search')); ?>"
                                            placeholder="<?php echo e(__('الاسم، البريد، الهاتف...')); ?>">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <label for="role" class="form-label"><?php echo e(__('الدور')); ?></label>
                                    <select name="role" id="role" class="form-select">
                                        <option value=""><?php echo e(__('جميع الأدوار')); ?></option>
                                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($role->id); ?>"
                                                <?php echo e(request('role') == $role->id ? 'selected' : ''); ?>>
                                                <?php echo e($role->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="branch" class="form-label"><?php echo e(__('الفرع')); ?></label>
                                    <select name="branch" id="branch" class="form-select">
                                        <option value=""><?php echo e(__('جميع الفروع')); ?></option>
                                        <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($branch->id); ?>"
                                                <?php echo e(request('branch') == $branch->id ? 'selected' : ''); ?>>
                                                <?php echo e($branch->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="status" class="form-label"><?php echo e(__('الحالة')); ?></label>
                                    <select name="status" id="status" class="form-select">
                                        <option value=""><?php echo e(__('جميع الحالات')); ?></option>
                                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>
                                            <?php echo e(__('نشط')); ?>

                                        </option>
                                        <option value="inactive"
                                            <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>
                                            <?php echo e(__('غير نشط')); ?>

                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label"><?php echo e(__('الإجراءات')); ?></label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary flex-fill">
                                            <i class="fas fa-search"></i> <?php echo e(__('تطبيق')); ?>

                                        </button>
                                        <a href="<?php echo e(route('admin.settings.users')); ?>"
                                            class="btn btn-outline-secondary">
                                            <i class="fas fa-undo"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th width="40">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAll">
                                        </div>
                                    </th>
                                    <th><?php echo e(__('المستخدم')); ?></th>
                                    <th><?php echo e(__('معلومات الاتصال')); ?></th>
                                    <th><?php echo e(__('الدور والفرع')); ?></th>
                                    <th><?php echo e(__('الحالة')); ?></th>
                                    <th><?php echo e(__('آخر نشاط')); ?></th>
                                    <th width="120"><?php echo e(__('الإجراءات')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input user-checkbox" type="checkbox"
                                                    value="<?php echo e($user->id); ?>">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div
                                                    class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                                    <i class="fas fa-user text-primary"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-semibold"><?php echo e($user->name); ?></div>
                                                    <div class="text-muted small"><?php echo e(__('معرف')); ?>:
                                                        <?php echo e($user->id); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="fas fa-envelope text-muted me-2"></i>
                                                    <span class="small"><?php echo e($user->email); ?></span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-phone text-muted me-2"></i>
                                                    <span class="small"><?php echo e($user->phone); ?></span>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <?php if($user->role): ?>
                                                    <span
                                                        class="badge bg-info bg-opacity-10 text-info border border-info mb-1">
                                                        <i class="fas fa-user-tag me-1"></i><?php echo e($user->role->name); ?>

                                                    </span>
                                                <?php endif; ?>
                                                <div class="text-muted small">
                                                    <i class="fas fa-building me-1"></i>
                                                    <?php echo e($user->branch ? $user->branch->name : __('غير محدد')); ?>

                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if($user->is_active): ?>
                                                <span
                                                    class="badge bg-success bg-opacity-10 text-success border border-success">
                                                    <i class="fas fa-check-circle me-1"></i><?php echo e(__('نشط')); ?>

                                                </span>
                                            <?php else: ?>
                                                <span
                                                    class="badge bg-danger bg-opacity-10 text-danger border border-danger">
                                                    <i class="fas fa-times-circle me-1"></i><?php echo e(__('غير نشط')); ?>

                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="text-muted small">
                                                <?php if($user->last_login_at): ?>
                                                    <div><?php echo e($user->last_login_at->format('Y-m-d')); ?></div>
                                                    <div><?php echo e($user->last_login_at->format('H:i')); ?></div>
                                                <?php else: ?>
                                                    <span class="text-muted"><?php echo e(__('لم يسجل دخول')); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.settings.users.show', $user)); ?>"
                                                    class="btn btn-sm btn-outline-info" title="<?php echo e(__('عرض')); ?>">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.settings.users.edit', $user)); ?>"
                                                    class="btn btn-sm btn-outline-primary"
                                                    title="<?php echo e(__('تعديل')); ?>">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if($user->id !== auth()->id()): ?>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="confirmDelete('<?php echo e(route('admin.settings.users.destroy', $user)); ?>')"
                                                        title="<?php echo e(__('حذف')); ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-5">
                                            <div class="text-muted">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <div class="h5"><?php echo e(__('لا توجد مستخدمين')); ?></div>
                                                <p><?php echo e(__('لم يتم العثور على أي مستخدمين مطابقين للبحث')); ?></p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if($users->hasPages()): ?>
                    <div class="card-footer bg-white border-top">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted small">
                                <?php echo e(__('عرض')); ?> <?php echo e($users->firstItem()); ?> <?php echo e(__('إلى')); ?>

                                <?php echo e($users->lastItem()); ?>

                                <?php echo e(__('من أصل')); ?> <?php echo e($users->total()); ?> <?php echo e(__('نتيجة')); ?>

                            </div>
                            <div>
                                <?php echo e($users->links()); ?>

                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bulk Actions Modal -->
    <div class="modal fade" id="bulkActionsModal" tabindex="-1" aria-labelledby="bulkActionsModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkActionsModalLabel"><?php echo e(__('الإجراءات المتعددة')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo e(__('لم يتم تحديد أي مستخدمين')); ?>

                    </div>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success" onclick="bulkAction('activate')">
                            <i class="fas fa-check-circle me-2"></i><?php echo e(__('تفعيل المستخدمين المحددين')); ?>

                        </button>
                        <button type="button" class="btn btn-warning" onclick="bulkAction('deactivate')">
                            <i class="fas fa-times-circle me-2"></i><?php echo e(__('إلغاء تفعيل المستخدمين المحددين')); ?>

                        </button>
                        <button type="button" class="btn btn-danger" onclick="bulkAction('delete')">
                            <i class="fas fa-trash me-2"></i><?php echo e(__('حذف المستخدمين المحددين')); ?>

                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel"><?php echo e(__('تأكيد الحذف')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                        <h5><?php echo e(__('هل أنت متأكد من حذف هذا المستخدم؟')); ?></h5>
                        <p class="text-muted"><?php echo e(__('لا يمكن التراجع عن هذا الإجراء')); ?></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"
                        data-bs-dismiss="modal"><?php echo e(__('إلغاء')); ?></button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger"><?php echo e(__('حذف')); ?></button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            // Select All functionality
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.user-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActionsButton();
            });

            // Individual checkbox change
            document.querySelectorAll('.user-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateBulkActionsButton();
                    updateSelectAllState();
                });
            });

            function updateSelectAllState() {
                const checkboxes = document.querySelectorAll('.user-checkbox');
                const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
                const selectAll = document.getElementById('selectAll');

                if (checkedBoxes.length === 0) {
                    selectAll.indeterminate = false;
                    selectAll.checked = false;
                } else if (checkedBoxes.length === checkboxes.length) {
                    selectAll.indeterminate = false;
                    selectAll.checked = true;
                } else {
                    selectAll.indeterminate = true;
                }
            }

            function updateBulkActionsButton() {
                const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
                const bulkButton = document.querySelector('[data-bs-target="#bulkActionsModal"]');
                const alertInfo = document.querySelector('#bulkActionsModal .alert-info');

                if (checkedBoxes.length > 0) {
                    bulkButton.disabled = false;
                    alertInfo.innerHTML =
                        `<i class="fas fa-info-circle me-2"></i><?php echo e(__('تم تحديد')); ?> ${checkedBoxes.length} <?php echo e(__('مستخدم')); ?>`;
                    alertInfo.className = 'alert alert-success';
                } else {
                    bulkButton.disabled = false;
                    alertInfo.innerHTML = '<i class="fas fa-info-circle me-2"></i><?php echo e(__('لم يتم تحديد أي مستخدمين')); ?>';
                    alertInfo.className = 'alert alert-info';
                }
            }

            function confirmDelete(url) {
                document.getElementById('deleteForm').action = url;
                new bootstrap.Modal(document.getElementById('deleteModal')).show();
            }

            function bulkAction(action) {
                const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
                if (checkedBoxes.length === 0) {
                    alert('<?php echo e(__('يرجى تحديد مستخدم واحد على الأقل')); ?>');
                    return;
                }

                const userIds = Array.from(checkedBoxes).map(cb => cb.value);

                if (confirm(`<?php echo e(__('هل أنت متأكد من تنفيذ هذا الإجراء على')); ?> ${userIds.length} <?php echo e(__('مستخدم؟')); ?>`)) {
                    // Create form and submit
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '<?php echo e(route('admin.settings.users')); ?>/bulk-action';

                    // CSRF token
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_token';
                    csrfInput.value = '<?php echo e(csrf_token()); ?>';
                    form.appendChild(csrfInput);

                    // Action input
                    const actionInput = document.createElement('input');
                    actionInput.type = 'hidden';
                    actionInput.name = 'action';
                    actionInput.value = action;
                    form.appendChild(actionInput);

                    // User IDs
                    userIds.forEach(id => {
                        const idInput = document.createElement('input');
                        idInput.type = 'hidden';
                        idInput.name = 'user_ids[]';
                        idInput.value = id;
                        form.appendChild(idInput);
                    });

                    document.body.appendChild(form);
                    form.submit();
                }
            }

            // Auto-submit filters on change
            document.querySelectorAll('#filtersForm select').forEach(select => {
                select.addEventListener('change', function() {
                    document.getElementById('filtersForm').submit();
                });
            });

            // Initialize on page load
            document.addEventListener('DOMContentLoaded', function() {
                updateBulkActionsButton();
                updateSelectAllState();
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\users\index.blade.php ENDPATH**/ ?>