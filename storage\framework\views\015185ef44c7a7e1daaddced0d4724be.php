<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-box text-primary me-2"></i>
                    تفاصيل المنتج: <?php echo e($product->name); ?>

                </h1>
                <p class="text-muted mb-0">عرض شامل لمعلومات المنتج والمخزون والحركات</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(user_route('products.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للمنتجات
                </a>
                <?php if(auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(user_route('products.edit', $product)); ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <div class="row">
            <!-- Product Information -->
            <div class="col-lg-4">
                <!-- Basic Info -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">معلومات أساسية</h6>
                    </div>
                    <div class="card-body text-center">
                        <?php if($product->image_path): ?>
                            <img src="<?php echo e(asset('storage/' . $product->image_path)); ?>" alt="<?php echo e($product->name); ?>"
                                class="img-fluid rounded mb-3" style="max-height: 200px;">
                        <?php else: ?>
                            <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3"
                                style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        <?php endif; ?>

                        <h5 class="fw-bold"><?php echo e($product->name); ?></h5>
                        <p class="text-muted mb-2"><?php echo e($product->sku ?: 'بدون رمز'); ?></p>

                        <div class="d-flex justify-content-center gap-2 mb-3">
                            <?php if($product->is_active): ?>
                                <span class="badge bg-success">نشط</span>
                            <?php else: ?>
                                <span class="badge bg-danger">غير نشط</span>
                            <?php endif; ?>
                            <span class="badge bg-info"><?php echo e($product->category->name ?? 'بدون فئة'); ?></span>
                        </div>

                        <?php if($product->description): ?>
                            <div class="text-start">
                                <h6 class="fw-bold">الوصف:</h6>
                                <p class="text-muted small"><?php echo e($product->description); ?></p>
                            </div>
                        <?php endif; ?>

                        <div class="text-start">
                            <small class="text-muted">
                                <strong>تاريخ الإنشاء:</strong> <?php echo e($product->created_at->format('Y-m-d H:i')); ?><br>
                                <strong>آخر تحديث:</strong> <?php echo e($product->updated_at->format('Y-m-d H:i')); ?>

                            </small>
                        </div>
                    </div>
                </div>

                <!-- Inventory Summary -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">ملخص المخزون</h6>
                    </div>
                    <div class="card-body">
                        <?php
                            $totalBranchStock = $product->branchInventories->sum('quantity');
                            $totalStoreStock = $product->storeInventories->sum('quantity');
                            $totalStock = $totalBranchStock + $totalStoreStock;
                        ?>

                        <div class="row text-center">
                            <div class="col-12 mb-3">
                                <div class="border-bottom pb-2">
                                    <h4 class="fw-bold text-primary"><?php echo e(number_format($totalStock, 2)); ?></h4>
                                    <small class="text-muted">إجمالي المخزون</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="fw-bold text-info"><?php echo e(number_format($totalBranchStock, 2)); ?></h6>
                                <small class="text-muted">في الفروع</small>
                            </div>
                            <div class="col-6">
                                <h6 class="fw-bold text-warning"><?php echo e(number_format($totalStoreStock, 2)); ?></h6>
                                <small class="text-muted">في المخازن</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Information -->
            <div class="col-lg-8">
                <!-- Inventory by Location -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">المخزون حسب الموقع</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Branch Inventory -->
                            <div class="col-md-6">
                                <h6 class="fw-bold text-info mb-3">الفروع</h6>
                                <?php if($product->branchInventories->count() > 0): ?>
                                    <?php $__currentLoopData = $product->branchInventories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $inventory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div
                                            class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                            <div>
                                                <span class="fw-bold"><?php echo e($inventory->branch->name); ?></span>
                                                <?php if($inventory->selling_price): ?>
                                                    <br><small class="text-muted">سعر البيع:
                                                        <?php echo e(format_currency($inventory->selling_price)); ?></small>
                                                <?php endif; ?>
                                            </div>
                                            <span
                                                class="badge bg-info"><?php echo e(number_format($inventory->quantity, 2)); ?></span>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <p class="text-muted">لا يوجد مخزون في الفروع</p>
                                <?php endif; ?>
                            </div>

                            <!-- Store Inventory -->
                            <div class="col-md-6">
                                <h6 class="fw-bold text-warning mb-3">المخازن</h6>
                                <?php if($product->storeInventories->count() > 0): ?>
                                    <?php $__currentLoopData = $product->storeInventories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $inventory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div
                                            class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                            <div>
                                                <span class="fw-bold"><?php echo e($inventory->store->name); ?></span>
                                                <?php if($inventory->store->isIndependent()): ?>
                                                    <small class="text-success">(مستقل)</small>
                                                <?php else: ?>
                                                    <small
                                                        class="text-muted">(<?php echo e($inventory->store->branch->name ?? 'فرع غير محدد'); ?>)</small>
                                                <?php endif; ?>
                                            </div>
                                            <span
                                                class="badge bg-warning"><?php echo e(number_format($inventory->quantity, 2)); ?></span>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <p class="text-muted">لا يوجد مخزون في المخازن</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transfer History -->
                <?php if(isset($transferHistory) && $transferHistory->count() > 0): ?>
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-warning">سجل النقل</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم النقل</th>
                                            <th>النوع</th>
                                            <th>من</th>
                                            <th>إلى</th>
                                            <th>الكمية</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $transferHistory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transfer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <a href="<?php echo e(user_route('inventory-transfers.show', $transfer->inventoryTransfer)); ?>"
                                                        class="text-primary text-decoration-none">
                                                        <?php echo e($transfer->inventoryTransfer->transfer_number); ?>

                                                    </a>
                                                </td>
                                                <td>
                                                    <?php
                                                        $typeLabels = [
                                                            'store_to_branch' => 'مخزن → فرع',
                                                            'branch_to_store' => 'فرع → مخزن',
                                                            'store_to_store' => 'مخزن → مخزن',
                                                            'branch_to_branch' => 'فرع → فرع',
                                                        ];
                                                    ?>
                                                    <span
                                                        class="badge bg-info"><?php echo e($typeLabels[$transfer->inventoryTransfer->type] ?? $transfer->inventoryTransfer->type); ?></span>
                                                </td>
                                                <td><?php echo e($transfer->inventoryTransfer->source->name ?? 'غير محدد'); ?></td>
                                                <td><?php echo e($transfer->inventoryTransfer->destination->name ?? 'غير محدد'); ?>

                                                </td>
                                                <td><?php echo e(number_format($transfer->requested_quantity, 2)); ?></td>
                                                <td>
                                                    <?php
                                                        $statusColors = [
                                                            'pending' => 'warning',
                                                            'approved' => 'info',
                                                            'in_transit' => 'secondary',
                                                            'completed' => 'success',
                                                            'cancelled' => 'danger',
                                                        ];
                                                        $statusLabels = [
                                                            'pending' => 'في الانتظار',
                                                            'approved' => 'موافق عليه',
                                                            'in_transit' => 'في الطريق',
                                                            'completed' => 'مكتمل',
                                                            'cancelled' => 'ملغي',
                                                        ];
                                                    ?>
                                                    <span
                                                        class="badge bg-<?php echo e($statusColors[$transfer->inventoryTransfer->status] ?? 'secondary'); ?>">
                                                        <?php echo e($statusLabels[$transfer->inventoryTransfer->status] ?? $transfer->inventoryTransfer->status); ?>

                                                    </span>
                                                </td>
                                                <td><?php echo e($transfer->inventoryTransfer->created_at->format('Y-m-d H:i')); ?>

                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Sales History -->
                <?php if($product->saleItems->count() > 0): ?>
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-success">سجل المبيعات</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>الفرع</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>الإجمالي</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $product->saleItems->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $saleItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <a href="<?php echo e(user_route('sales.show', $saleItem->sale)); ?>"
                                                        class="text-primary text-decoration-none">
                                                        <?php echo e($saleItem->sale->invoice_number); ?>

                                                    </a>
                                                </td>
                                                <td><?php echo e($saleItem->sale->customer->name ?? 'عميل نقدي'); ?></td>
                                                <td><?php echo e($saleItem->sale->branch->name ?? 'غير محدد'); ?></td>
                                                <td><?php echo e(number_format($saleItem->quantity, 2)); ?></td>
                                                <td><?php echo e(format_currency($saleItem->price)); ?></td>
                                                <td><?php echo e(format_currency($saleItem->subtotal)); ?></td>
                                                <td><?php echo e($saleItem->created_at->format('Y-m-d H:i')); ?></td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                                <?php if($product->saleItems->count() > 10): ?>
                                    <div class="text-center mt-3">
                                        <small class="text-muted">عرض آخر 10 مبيعات من أصل
                                            <?php echo e($product->saleItems->count()); ?> مبيعة</small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Purchase History -->
                <?php if(isset($purchaseHistory) && $purchaseHistory->count() > 0): ?>
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">سجل المشتريات</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم الشراء</th>
                                            <th>المورد</th>
                                            <th>الكمية</th>
                                            <th>سعر الشراء</th>
                                            <th>الإجمالي</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $purchaseHistory->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $purchase): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if(isset($purchase->purchase_item)): ?>
                                                <tr>
                                                    <td>
                                                        <a href="<?php echo e(user_route('purchases.show', $purchase)); ?>"
                                                            class="text-primary text-decoration-none">
                                                            <?php echo e($purchase->invoice_number); ?>

                                                        </a>
                                                    </td>
                                                    <td><?php echo e($purchase->supplier->name ?? 'غير محدد'); ?></td>
                                                    <td><?php echo e(number_format($purchase->purchase_item->quantity, 2)); ?></td>
                                                    <td><?php echo e(number_format($purchase->purchase_item->cost_price, 2)); ?>

                                                        ج.م</td>
                                                    <td><?php echo e(number_format($purchase->purchase_item->total_price, 2)); ?>

                                                        ج.م</td>
                                                    <td><?php echo e($purchase->created_at->format('Y-m-d H:i')); ?></td>
                                                </tr>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                                <?php if($purchaseHistory->count() > 10): ?>
                                    <div class="text-center mt-3">
                                        <small class="text-muted">عرض آخر 10 مشتريات من أصل
                                            <?php echo e($purchaseHistory->count()); ?> مشترية</small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views/products/show.blade.php ENDPATH**/ ?>