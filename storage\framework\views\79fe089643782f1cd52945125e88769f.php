<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-truck text-primary me-2"></i>
                    إدارة الموردين
                </h1>
                <p class="text-muted mb-0">إدارة وتتبع جميع الموردين والشركاء التجاريين</p>
            </div>
            <?php if(auth()->user()->isAdmin()): ?>
                <a href="<?php echo e(user_route('suppliers.create')); ?>" class="btn btn-primary shadow-sm">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مورد جديد
                </a>
            <?php endif; ?>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي الموردين
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo e($suppliers->total()); ?>

                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    الموردين النشطين
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo e($suppliers->where('is_active', true)->count()); ?>

                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    المشتريات هذا الشهر
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo e($suppliers->sum('purchases_count')); ?>

                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    الموردين غير النشطين
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo e($suppliers->where('is_active', false)->count()); ?>

                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter me-2"></i>
                    البحث والتصفية
                </h6>
                <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse"
                    data-bs-target="#filtersCollapse">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
            <div class="collapse show" id="filtersCollapse">
                <div class="card-body">
                    <form action="<?php echo e(user_route('suppliers.index')); ?>" method="GET" class="rtl">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">
                                    <i class="fas fa-search me-1"></i>
                                    البحث السريع
                                </label>
                                <input type="text" name="search" id="search" class="form-control"
                                    value="<?php echo e(request('search')); ?>"
                                    placeholder="اسم المورد، رقم الهاتف، أو البريد الإلكتروني">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">
                                    <i class="fas fa-toggle-on me-1"></i>
                                    حالة المورد
                                </label>
                                <select name="status" id="status" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>
                                        نشط
                                    </option>
                                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>
                                        غير نشط
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="sort" class="form-label">
                                    <i class="fas fa-sort me-1"></i>
                                    ترتيب النتائج
                                </label>
                                <select name="sort" id="sort" class="form-select">
                                    <option value="name_asc" <?php echo e(request('sort') == 'name_asc' ? 'selected' : ''); ?>>
                                        الاسم (أ-ي)
                                    </option>
                                    <option value="name_desc" <?php echo e(request('sort') == 'name_desc' ? 'selected' : ''); ?>>
                                        الاسم (ي-أ)
                                    </option>
                                    <option value="created_desc"
                                        <?php echo e(request('sort') == 'created_desc' ? 'selected' : ''); ?>>
                                        الأحدث أولاً
                                    </option>
                                    <option value="created_asc"
                                        <?php echo e(request('sort') == 'created_asc' ? 'selected' : ''); ?>>
                                        الأقدم أولاً
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <div class="btn-group w-100" role="group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <a href="<?php echo e(user_route('suppliers.index')); ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Suppliers Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table me-2"></i>
                    قائمة الموردين
                    <span class="badge bg-primary ms-2"><?php echo e($suppliers->total()); ?> مورد</span>
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover rtl" id="suppliersTable">
                        <thead class="table-light">
                            <tr>
                                <th class="border-0">
                                    <i class="fas fa-user me-1"></i>
                                    اسم المورد
                                </th>
                                <th class="border-0">
                                    <i class="fas fa-phone me-1"></i>
                                    رقم الهاتف
                                </th>
                                <th class="border-0">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني
                                </th>
                                <th class="border-0">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    العنوان
                                </th>
                                <th class="border-0">
                                    <i class="fas fa-toggle-on me-1"></i>
                                    الحالة
                                </th>
                                <th class="border-0">
                                    <i class="fas fa-shopping-cart me-1"></i>
                                    المشتريات
                                </th>
                                <th class="border-0 text-center">
                                    <i class="fas fa-cogs me-1"></i>
                                    الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr class="supplier-row">
                                    <td class="align-middle">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-primary text-white me-3">
                                                <?php echo e(substr($supplier->name, 0, 1)); ?>

                                            </div>
                                            <div>
                                                <div class="fw-bold"><?php echo e($supplier->name); ?></div>
                                                <?php if($supplier->tax_number): ?>
                                                    <small class="text-muted">ض.ر: <?php echo e($supplier->tax_number); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <?php if($supplier->phone): ?>
                                            <a href="tel:<?php echo e($supplier->phone); ?>" class="text-decoration-none">
                                                <i class="fas fa-phone-alt text-success me-1"></i>
                                                <?php echo e($supplier->phone); ?>

                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">غير متوفر</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="align-middle">
                                        <?php if($supplier->email): ?>
                                            <a href="mailto:<?php echo e($supplier->email); ?>" class="text-decoration-none">
                                                <i class="fas fa-envelope text-info me-1"></i>
                                                <?php echo e($supplier->email); ?>

                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">غير متوفر</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="align-middle">
                                        <?php if($supplier->address): ?>
                                            <span class="text-truncate d-inline-block" style="max-width: 150px;"
                                                title="<?php echo e($supplier->address); ?>">
                                                <?php echo e($supplier->address); ?>

                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="align-middle">
                                        <?php if($supplier->is_active): ?>
                                            <span class="badge bg-success-soft text-success px-3 py-2">
                                                <i class="fas fa-check-circle me-1"></i>
                                                نشط
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-danger-soft text-danger px-3 py-2">
                                                <i class="fas fa-times-circle me-1"></i>
                                                غير نشط
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="align-middle">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-info-soft text-info px-2 py-1 me-2">
                                                <?php echo e($supplier->purchases_count); ?>

                                            </span>
                                            <?php if($supplier->purchases_count > 0): ?>
                                                <small class="text-muted">عملية</small>
                                            <?php else: ?>
                                                <small class="text-muted">لا توجد</small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(user_route('suppliers.show', $supplier)); ?>"
                                                class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if(auth()->user()->isAdmin()): ?>
                                                <a href="<?php echo e(user_route('suppliers.edit', $supplier)); ?>"
                                                    class="btn btn-outline-primary btn-sm" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button"
                                                    class="btn btn-outline-danger btn-sm delete-supplier"
                                                    data-supplier-id="<?php echo e($supplier->id); ?>"
                                                    data-supplier-name="<?php echo e($supplier->name); ?>" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Hidden delete form -->
                                        <?php if(auth()->user()->isAdmin()): ?>
                                            <form id="delete-form-<?php echo e($supplier->id); ?>"
                                                action="<?php echo e(user_route('suppliers.destroy', $supplier)); ?>"
                                                method="POST" class="d-none">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                            </form>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="empty-state">
                                            <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">لا يوجد موردين</h5>
                                            <p class="text-muted">لم يتم العثور على أي موردين مطابقين لمعايير البحث</p>
                                            <?php if(auth()->user()->isAdmin()): ?>
                                                <a href="<?php echo e(user_route('suppliers.create')); ?>"
                                                    class="btn btn-primary">
                                                    <i class="fas fa-plus me-2"></i>
                                                    إضافة مورد جديد
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            <?php if($suppliers->hasPages()): ?>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            عرض <?php echo e($suppliers->firstItem()); ?> إلى <?php echo e($suppliers->lastItem()); ?>

                            من أصل <?php echo e($suppliers->total()); ?> مورد
                        </div>
                        <div>
                            <?php echo e($suppliers->links()); ?>

                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
        <style>
            .avatar-circle {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 16px;
            }

            .border-left-primary {
                border-left: 0.25rem solid #4e73df !important;
            }

            .border-left-success {
                border-left: 0.25rem solid #1cc88a !important;
            }

            .border-left-info {
                border-left: 0.25rem solid #36b9cc !important;
            }

            .border-left-warning {
                border-left: 0.25rem solid #f6c23e !important;
            }

            .bg-success-soft {
                background-color: rgba(28, 200, 138, 0.1) !important;
            }

            .bg-danger-soft {
                background-color: rgba(231, 74, 59, 0.1) !important;
            }

            .bg-info-soft {
                background-color: rgba(54, 185, 204, 0.1) !important;
            }

            .supplier-row:hover {
                background-color: rgba(0, 123, 255, 0.05);
                transition: background-color 0.2s ease;
            }

            .empty-state {
                padding: 2rem;
            }

            .text-gray-800 {
                color: #5a5c69 !important;
            }

            .text-gray-300 {
                color: #dddfeb !important;
            }

            .rtl {
                direction: rtl;
                text-align: right;
            }

            .rtl .me-1 {
                margin-left: 0.25rem !important;
                margin-right: 0 !important;
            }

            .rtl .me-2 {
                margin-left: 0.5rem !important;
                margin-right: 0 !important;
            }

            .rtl .me-3 {
                margin-left: 1rem !important;
                margin-right: 0 !important;
            }

            .rtl .ms-2 {
                margin-right: 0.5rem !important;
                margin-left: 0 !important;
            }
        </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Delete supplier confirmation with SweetAlert
                document.querySelectorAll('.delete-supplier').forEach(function(button) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();

                        const supplierId = this.getAttribute('data-supplier-id');
                        const supplierName = this.getAttribute('data-supplier-name');

                        Swal.fire({
                            title: 'تأكيد الحذف',
                            text: `هل أنت متأكد من حذف المورد "${supplierName}"؟`,
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#d33',
                            cancelButtonColor: '#3085d6',
                            confirmButtonText: 'نعم، احذف',
                            cancelButtonText: 'إلغاء',
                            reverseButtons: true
                        }).then((result) => {
                            if (result.isConfirmed) {
                                document.getElementById(`delete-form-${supplierId}`).submit();
                            }
                        });
                    });
                });

                // Auto-submit search form on filter change
                document.getElementById('status').addEventListener('change', function() {
                    this.closest('form').submit();
                });

                document.getElementById('sort').addEventListener('change', function() {
                    this.closest('form').submit();
                });

                // Add loading state to search button
                document.querySelector('form button[type="submit"]').addEventListener('click', function() {
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    this.disabled = true;
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\suppliers\index.blade.php ENDPATH**/ ?>