<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('تقرير المشتريات')); ?>

            </h2>
            <div>
                <button type="button" class="btn btn-success" onclick="printReport()">
                    <i class="fas fa-print"></i> <?php echo e(__('طباعة')); ?>

                </button>
                <button type="button" class="btn btn-primary" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> <?php echo e(__('تصدير إلى Excel')); ?>

                </button>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Filters -->
                    <form action="<?php echo e(route('reports.purchases')); ?>" method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="supplier"><?php echo e(__('المورد')); ?></label>
                                    <select name="supplier" id="supplier" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($supplier->id); ?>" <?php echo e(request('supplier') == $supplier->id ? 'selected' : ''); ?>>
                                                <?php echo e($supplier->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="branch"><?php echo e(__('الفرع')); ?></label>
                                    <select name="branch" id="branch" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($branch->id); ?>" <?php echo e(request('branch') == $branch->id ? 'selected' : ''); ?>>
                                                <?php echo e($branch->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_range"><?php echo e(__('الفترة')); ?></label>
                                    <input type="text" name="date_range" id="date_range" class="form-control" value="<?php echo e(request('date_range')); ?>" placeholder="<?php echo e(__('اختر الفترة')); ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="status"><?php echo e(__('الحالة')); ?></label>
                                    <select name="status" id="status" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>><?php echo e(__('مكتمل')); ?></option>
                                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>><?php echo e(__('قيد الانتظار')); ?></option>
                                        <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>><?php echo e(__('ملغي')); ?></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="group_by"><?php echo e(__('تجميع حسب')); ?></label>
                                    <select name="group_by" id="group_by" class="form-select">
                                        <option value="day" <?php echo e(request('group_by') == 'day' ? 'selected' : ''); ?>><?php echo e(__('يومي')); ?></option>
                                        <option value="week" <?php echo e(request('group_by') == 'week' ? 'selected' : ''); ?>><?php echo e(__('أسبوعي')); ?></option>
                                        <option value="month" <?php echo e(request('group_by') == 'month' ? 'selected' : ''); ?>><?php echo e(__('شهري')); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="sort"><?php echo e(__('ترتيب حسب')); ?></label>
                                    <select name="sort" id="sort" class="form-select">
                                        <option value="date_asc" <?php echo e(request('sort') == 'date_asc' ? 'selected' : ''); ?>><?php echo e(__('التاريخ (تصاعدي)')); ?></option>
                                        <option value="date_desc" <?php echo e(request('sort') == 'date_desc' ? 'selected' : ''); ?>><?php echo e(__('التاريخ (تنازلي)')); ?></option>
                                        <option value="amount_desc" <?php echo e(request('sort') == 'amount_desc' ? 'selected' : ''); ?>><?php echo e(__('المبلغ (تنازلي)')); ?></option>
                                        <option value="amount_asc" <?php echo e(request('sort') == 'amount_asc' ? 'selected' : ''); ?>><?php echo e(__('المبلغ (تصاعدي)')); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 text-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> <?php echo e(__('عرض التقرير')); ?>

                                </button>
                                <a href="<?php echo e(route('reports.purchases')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> <?php echo e(__('إعادة تعيين')); ?>

                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('إجمالي المشتريات')); ?></h5>
                                    <h3 class="mb-0"><?php echo e(number_format($summary['total_purchases'], 2)); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('عدد المشتريات')); ?></h5>
                                    <h3 class="mb-0"><?php echo e($summary['total_orders']); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('متوسط قيمة الشراء')); ?></h5>
                                    <h3 class="mb-0"><?php echo e(number_format($summary['average_order'], 2)); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('أعلى قيمة شراء')); ?></h5>
                                    <h3 class="mb-0"><?php echo e(number_format($summary['highest_order'], 2)); ?></h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Purchases Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('الفترة')); ?></th>
                                    <th><?php echo e(__('عدد المشتريات')); ?></th>
                                    <th><?php echo e(__('إجمالي المشتريات')); ?></th>
                                    <th><?php echo e(__('متوسط قيمة الشراء')); ?></th>
                                    <th><?php echo e(__('أعلى قيمة شراء')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $purchases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $purchase): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($purchase->period); ?></td>
                                        <td><?php echo e($purchase->orders_count); ?></td>
                                        <td><?php echo e(number_format($purchase->total_purchases, 2)); ?></td>
                                        <td><?php echo e(number_format($purchase->average_order, 2)); ?></td>
                                        <td><?php echo e(number_format($purchase->highest_order, 2)); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="5" class="text-center"><?php echo e(__('لا توجد بيانات')); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        <?php echo e($purchases->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .form-select {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .text-start {
            text-align: right !important;
        }
        .rtl .text-end {
            text-align: left !important;
        }
        .rtl .form-control {
            text-align: right;
        }
        .rtl .form-control::placeholder {
            text-align: right;
        }
        .rtl .card-title {
            text-align: right;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .container-fluid {
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }
        }
    </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#date_range').daterangepicker({
                locale: {
                    format: 'YYYY-MM-DD',
                    applyLabel: 'تطبيق',
                    cancelLabel: 'إلغاء',
                    fromLabel: 'من',
                    toLabel: 'إلى',
                    customRangeLabel: 'مخصص',
                    daysOfWeek: ['أحد', 'اثن', 'ثلا', 'أرب', 'خمي', 'جمع', 'سبت'],
                    monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
                }
            });
        });

        function printReport() {
            window.print();
        }

        function exportToExcel() {
            // Add Excel export functionality here
            alert('سيتم إضافة وظيفة التصدير إلى Excel قريباً');
        }
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\reports\purchases.blade.php ENDPATH**/ ?>