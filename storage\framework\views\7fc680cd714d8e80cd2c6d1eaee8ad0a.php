<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-history text-info"></i> <?php echo e(__('سجل عمليات النقل المباشر')); ?>

                </h2>
                <p class="text-muted small mb-0">سجل جميع عمليات النقل المباشر المنفذة فوريًا</p>
            </div>
            <div>
                <a href="<?php echo e(user_route('transfers.direct.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> نقل مباشر جديد
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="container-fluid">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    عمليات النقل المباشر
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['total_completed']); ?>

                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    نقل فوري ومباشر
                                </div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">بدون انتظار موافقة
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-bolt fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    إجمالي الكمية المنقولة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo e(number_format($stats['total_quantity'], 2)); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-boxes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter"></i> البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(user_route('transfers.history')); ?>">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search"
                                value="<?php echo e(request('search')); ?>" placeholder="اسم المنتج أو الكود">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>مكتمل
                                </option>
                                <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>ملغي
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                value="<?php echo e(request('date_from')); ?>">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                value="<?php echo e(request('date_to')); ?>">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Transfer History Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-table"></i> سجل عمليات النقل المباشر
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>رقم النقل</th>
                                <th>النوع</th>
                                <th>من</th>
                                <th>إلى</th>
                                <th>المنتجات</th>
                                <th>إجمالي الكمية</th>
                                <th>نفذ بواسطة</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $transfers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transfer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td class="fw-bold">
                                        <a href="<?php echo e(user_route('transfers.show', $transfer)); ?>" class="text-primary">
                                            #<?php echo e($transfer->transfer_number); ?>

                                        </a>
                                    </td>
                                    <td>
                                        <span
                                            class="badge bg-info"><?php echo e(ucfirst(str_replace('_', ' إلى ', $transfer->type))); ?></span>
                                    </td>
                                    <td>
                                        <?php if($transfer->source_type == 'branch'): ?>
                                            <span class="badge bg-primary">فرع</span>
                                            <?php echo e($transfer->sourceBranch->name ?? 'غير محدد'); ?>

                                        <?php else: ?>
                                            <span class="badge bg-success">مخزن</span>
                                            <?php echo e($transfer->sourceStore->name ?? 'غير محدد'); ?>

                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($transfer->destination_type == 'branch'): ?>
                                            <span class="badge bg-primary">فرع</span>
                                            <?php echo e($transfer->destinationBranch->name ?? 'غير محدد'); ?>

                                        <?php else: ?>
                                            <span class="badge bg-success">مخزن</span>
                                            <?php echo e($transfer->destinationStore->name ?? 'غير محدد'); ?>

                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center"><?php echo e($transfer->items->count()); ?></td>
                                    <td class="text-center">
                                        <?php echo e(number_format($transfer->items->sum('received_quantity'), 2)); ?>

                                    </td>
                                    <td><?php echo e($transfer->requestedBy->name); ?></td>
                                    <td><?php echo e($transfer->created_at->format('Y-m-d H:i')); ?></td>
                                    <td>
                                        <?php if($transfer->status == 'completed'): ?>
                                            <span class="badge bg-success">مكتمل</span>
                                        <?php elseif($transfer->status == 'cancelled'): ?>
                                            <span class="badge bg-danger">ملغي</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">معلق</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo e(user_route('transfers.show', $transfer)); ?>"
                                                class="btn btn-outline-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد عمليات نقل مباشر في السجل</p>
                                        <a href="<?php echo e(user_route('transfers.direct.create')); ?>"
                                            class="btn btn-primary">
                                            <i class="fas fa-plus"></i> إنشاء عملية نقل مباشر
                                        </a>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if($transfers->hasPages()): ?>
                    <div class="mt-4">
                        <?php echo e($transfers->appends(request()->query())->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\transfers\history.blade.php ENDPATH**/ ?>