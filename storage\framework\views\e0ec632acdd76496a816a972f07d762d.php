<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-store text-success"></i> <?php echo e(__('مخزون مخزن')); ?> - <?php echo e($store->name); ?>

                </h2>
                <p class="text-muted small mb-0">تفاصيل مخزون مخزن <?php echo e($store->name); ?> (<?php echo e($store->code); ?>)</p>
            </div>
            <div>
                <a href="<?php echo e(route('admin.inventory.stores')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للمخازن
                </a>
                <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>?source_type=store&source_id=<?php echo e($store->id); ?>" class="btn btn-success">
                    <i class="fas fa-exchange-alt"></i> نقل من هذا المخزن
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="container-fluid">
        <!-- Store Info Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-left-success shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-info-circle"></i> معلومات المخزن
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 text-success"><?php echo e($inventory->total()); ?></div>
                                    <div class="small text-muted">إجمالي المنتجات</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 text-primary">
                                        <?php echo e(number_format($inventory->sum(function($item) { return $item->quantity * ($item->product->price ?? 0); }), 2)); ?> ج.م
                                    </div>
                                    <div class="small text-muted">إجمالي القيمة</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 text-warning">
                                        <?php echo e($inventory->filter(function($item) { return $item->quantity <= $item->minimum_stock; })->count()); ?>

                                    </div>
                                    <div class="small text-muted">مخزون منخفض</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 text-info"><?php echo e($store->code); ?></div>
                                    <div class="small text-muted">كود المخزن</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 text-secondary"><?php echo e($store->branch->name ?? 'مستقل'); ?></div>
                                    <div class="small text-muted">الفرع التابع له</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-search"></i> البحث في المخزون
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('admin.inventory.store-details', $store)); ?>">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <input type="text" class="form-control" name="search" 
                                   value="<?php echo e(request('search')); ?>" placeholder="البحث عن منتج بالاسم أو الكود">
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Inventory Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-table"></i> تفاصيل المخزون
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكود</th>
                                <th>التصنيف</th>
                                <th>الكمية الحالية</th>
                                <th>الحد الأدنى</th>
                                <th>الحد الأقصى</th>
                                <th>سعر المنتج</th>
                                <th>إجمالي القيمة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $inventory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td class="fw-bold"><?php echo e($item->product->name); ?></td>
                                    <td><?php echo e($item->product->sku ?? '-'); ?></td>
                                    <td><?php echo e($item->product->category->name ?? 'غير محدد'); ?></td>
                                    <td class="text-center">
                                        <span class="badge <?php echo e($item->quantity <= $item->minimum_stock ? 'bg-warning' : 'bg-success'); ?>">
                                            <?php echo e(number_format($item->quantity, 2)); ?>

                                        </span>
                                    </td>
                                    <td class="text-center"><?php echo e(number_format($item->minimum_stock, 2)); ?></td>
                                    <td class="text-center"><?php echo e(number_format($item->maximum_stock ?? 0, 2)); ?></td>
                                    <td><?php echo e(number_format($item->product->price ?? 0, 2)); ?> ج.م</td>
                                    <td class="fw-bold"><?php echo e(number_format($item->quantity * ($item->product->price ?? 0), 2)); ?> ج.م</td>
                                    <td>
                                        <?php if($item->quantity <= 0): ?>
                                            <span class="badge bg-danger">نفد المخزون</span>
                                        <?php elseif($item->quantity <= $item->minimum_stock): ?>
                                            <span class="badge bg-warning">مخزون منخفض</span>
                                        <?php elseif($item->maximum_stock && $item->quantity >= $item->maximum_stock): ?>
                                            <span class="badge bg-info">مخزون مرتفع</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">متوفر</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo e(route('admin.products.show', $item->product)); ?>" 
                                               class="btn btn-outline-info" title="عرض المنتج">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>?source_type=store&source_id=<?php echo e($store->id); ?>&product_id=<?php echo e($item->product->id); ?>" 
                                               class="btn btn-outline-success" title="نقل">
                                                <i class="fas fa-exchange-alt"></i>
                                            </a>
                                            <?php if($item->quantity <= $item->minimum_stock): ?>
                                                <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>?destination_type=store&destination_id=<?php echo e($store->id); ?>&product_id=<?php echo e($item->product->id); ?>" 
                                                   class="btn btn-outline-primary" title="تموين">
                                                    <i class="fas fa-plus"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد منتجات في هذا المخزن</p>
                                        <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>?destination_type=store&destination_id=<?php echo e($store->id); ?>" 
                                           class="btn btn-success">
                                            <i class="fas fa-plus"></i> نقل منتجات إلى هذا المخزن
                                        </a>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if($inventory->hasPages()): ?>
                    <div class="mt-4">
                        <?php echo e($inventory->appends(request()->query())->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card border-left-info shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-bolt"></i> إجراءات سريعة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>?source_type=store&source_id=<?php echo e($store->id); ?>" 
                               class="btn btn-success">
                                <i class="fas fa-arrow-right"></i> نقل من هذا المخزن
                            </a>
                            <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>?destination_type=store&destination_id=<?php echo e($store->id); ?>" 
                               class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> نقل إلى هذا المخزن
                            </a>
                            <a href="<?php echo e(route('admin.stores.inventory', $store->id)); ?>" 
                               class="btn btn-info">
                                <i class="fas fa-cog"></i> إدارة المخزون
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-left-warning shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-exclamation-triangle"></i> تنبيهات المخزون
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php
                            $lowStockCount = $inventory->filter(function($item) { return $item->quantity <= $item->minimum_stock; })->count();
                            $outOfStockCount = $inventory->where('quantity', '<=', 0)->count();
                            $highStockCount = $inventory->filter(function($item) { return $item->maximum_stock && $item->quantity >= $item->maximum_stock; })->count();
                        ?>
                        
                        <?php if($lowStockCount > 0): ?>
                            <div class="alert alert-warning mb-2">
                                <i class="fas fa-exclamation-triangle"></i>
                                <?php echo e($lowStockCount); ?> منتج بمخزون منخفض
                            </div>
                        <?php endif; ?>
                        
                        <?php if($outOfStockCount > 0): ?>
                            <div class="alert alert-danger mb-2">
                                <i class="fas fa-times-circle"></i>
                                <?php echo e($outOfStockCount); ?> منتج نفد مخزونه
                            </div>
                        <?php endif; ?>
                        
                        <?php if($highStockCount > 0): ?>
                            <div class="alert alert-info mb-2">
                                <i class="fas fa-arrow-up"></i>
                                <?php echo e($highStockCount); ?> منتج بمخزون مرتفع
                            </div>
                        <?php endif; ?>
                        
                        <?php if($lowStockCount == 0 && $outOfStockCount == 0): ?>
                            <div class="alert alert-success mb-0">
                                <i class="fas fa-check-circle"></i>
                                جميع المنتجات ضمن المستويات المطلوبة
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\admin\inventory\store-details.blade.php ENDPATH**/ ?>