<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('تقرير المخزون')); ?>

            </h2>
            <div>
                <button type="button" class="btn btn-success" onclick="printReport()">
                    <i class="fas fa-print"></i> <?php echo e(__('طباعة')); ?>

                </button>
                <button type="button" class="btn btn-primary" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> <?php echo e(__('تصدير إلى Excel')); ?>

                </button>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <!-- Filters -->
                    <form action="<?php echo e(route('reports.inventory')); ?>" method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search"><?php echo e(__('بحث')); ?></label>
                                    <input type="text" name="search" id="search" class="form-control" value="<?php echo e(request('search')); ?>" placeholder="<?php echo e(__('اسم المنتج أو الرمز')); ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="category"><?php echo e(__('التصنيف')); ?></label>
                                    <select name="category" id="category" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                                <?php echo e($category->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="branch"><?php echo e(__('الفرع')); ?></label>
                                    <select name="branch" id="branch" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($branch->id); ?>" <?php echo e(request('branch') == $branch->id ? 'selected' : ''); ?>>
                                                <?php echo e($branch->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="stock_status"><?php echo e(__('حالة المخزون')); ?></label>
                                    <select name="stock_status" id="stock_status" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <option value="available" <?php echo e(request('stock_status') == 'available' ? 'selected' : ''); ?>><?php echo e(__('متوفر')); ?></option>
                                        <option value="low" <?php echo e(request('stock_status') == 'low' ? 'selected' : ''); ?>><?php echo e(__('منخفض')); ?></option>
                                        <option value="out" <?php echo e(request('stock_status') == 'out' ? 'selected' : ''); ?>><?php echo e(__('غير متوفر')); ?></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="sort_by"><?php echo e(__('ترتيب حسب')); ?></label>
                                    <select name="sort_by" id="sort_by" class="form-select">
                                        <option value="name_asc" <?php echo e(request('sort_by') == 'name_asc' ? 'selected' : ''); ?>><?php echo e(__('الاسم (أ-ي)')); ?></option>
                                        <option value="name_desc" <?php echo e(request('sort_by') == 'name_desc' ? 'selected' : ''); ?>><?php echo e(__('الاسم (ي-أ)')); ?></option>
                                        <option value="stock_asc" <?php echo e(request('sort_by') == 'stock_asc' ? 'selected' : ''); ?>><?php echo e(__('المخزون (تصاعدي)')); ?></option>
                                        <option value="stock_desc" <?php echo e(request('sort_by') == 'stock_desc' ? 'selected' : ''); ?>><?php echo e(__('المخزون (تنازلي)')); ?></option>
                                        <option value="value_asc" <?php echo e(request('sort_by') == 'value_asc' ? 'selected' : ''); ?>><?php echo e(__('القيمة (تصاعدي)')); ?></option>
                                        <option value="value_desc" <?php echo e(request('sort_by') == 'value_desc' ? 'selected' : ''); ?>><?php echo e(__('القيمة (تنازلي)')); ?></option>
                                        <option value="category_asc" <?php echo e(request('sort_by') == 'category_asc' ? 'selected' : ''); ?>><?php echo e(__('التصنيف (أ-ي)')); ?></option>
                                        <option value="category_desc" <?php echo e(request('sort_by') == 'category_desc' ? 'selected' : ''); ?>><?php echo e(__('التصنيف (ي-أ)')); ?></option>
                                        <option value="branch_asc" <?php echo e(request('sort_by') == 'branch_asc' ? 'selected' : ''); ?>><?php echo e(__('الفرع (أ-ي)')); ?></option>
                                        <option value="branch_desc" <?php echo e(request('sort_by') == 'branch_desc' ? 'selected' : ''); ?>><?php echo e(__('الفرع (ي-أ)')); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-9 text-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> <?php echo e(__('عرض التقرير')); ?>

                                </button>
                                <a href="<?php echo e(route('reports.inventory')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> <?php echo e(__('إعادة تعيين')); ?>

                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('إجمالي المنتجات')); ?></h5>
                                    <h3 class="mb-0"><?php echo e($summary['total_products']); ?></h3>
                                    <small><?php echo e(__('في')); ?> <?php echo e($summary['total_categories']); ?> <?php echo e(__('تصنيف')); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('قيمة المخزون')); ?></h5>
                                    <h3 class="mb-0"><?php echo e(number_format($summary['total_value'], 2)); ?></h3>
                                    <small><?php echo e(__('قيمة البيع')); ?>: <?php echo e(number_format($summary['total_sale_value'], 2)); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('حالة المخزون')); ?></h5>
                                    <h3 class="mb-0"><?php echo e($summary['total_stock']); ?></h3>
                                    <small><?php echo e(__('متوسط')); ?>: <?php echo e(number_format($summary['average_stock'], 1)); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('تنبيهات')); ?></h5>
                                    <h3 class="mb-0"><?php echo e($summary['low_stock_count'] + $summary['out_of_stock_count']); ?></h3>
                                    <small><?php echo e(__('منخفض')); ?>: <?php echo e($summary['low_stock_count']); ?> | <?php echo e(__('غير متوفر')); ?>: <?php echo e($summary['out_of_stock_count']); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Top Categories and Branches -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0"><?php echo e(__('أعلى التصنيفات')); ?></h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th><?php echo e(__('التصنيف')); ?></th>
                                                    <th><?php echo e(__('المخزون')); ?></th>
                                                    <th><?php echo e(__('القيمة')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $topCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($category['name']); ?></td>
                                                        <td><?php echo e($category['total_stock']); ?></td>
                                                        <td><?php echo e(number_format($category['total_value'], 2)); ?></td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0"><?php echo e(__('أعلى الفروع')); ?></h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th><?php echo e(__('الفرع')); ?></th>
                                                    <th><?php echo e(__('المخزون')); ?></th>
                                                    <th><?php echo e(__('القيمة')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $topBranches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($branch['name']); ?></td>
                                                        <td><?php echo e($branch['total_stock']); ?></td>
                                                        <td><?php echo e(number_format($branch['total_value'], 2)); ?></td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('المنتج')); ?></th>
                                    <th><?php echo e(__('التصنيف')); ?></th>
                                    <th><?php echo e(__('الفرع')); ?></th>
                                    <th><?php echo e(__('المخزون')); ?></th>
                                    <th><?php echo e(__('الحد الأدنى')); ?></th>
                                    <th><?php echo e(__('سعر الشراء')); ?></th>
                                    <th><?php echo e(__('سعر البيع')); ?></th>
                                    <th><?php echo e(__('قيمة المخزون')); ?></th>
                                    <th><?php echo e(__('الحالة')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <?php echo e($product->name); ?>

                                            <?php if($product->sku): ?>
                                                <br><small class="text-muted"><?php echo e($product->sku); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($product->category->name); ?></td>
                                        <td>
                                            <?php $__currentLoopData = $product->branchInventories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $inventory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php echo e($inventory->branch->name); ?>: <?php echo e($inventory->quantity); ?><br>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </td>
                                        <td><?php echo e($product->branchInventories->sum('quantity')); ?></td>
                                        <td><?php echo e($product->minimum_stock); ?></td>
                                        <td><?php echo e(number_format($product->cost_price, 2)); ?></td>
                                        <td><?php echo e(number_format($product->sale_price, 2)); ?></td>
                                        <td><?php echo e(number_format($product->branchInventories->sum(function($inventory) use ($product) {
                                            return $inventory->quantity * $product->cost_price;
                                        }), 2)); ?></td>
                                        <td>
                                            <?php
                                                $totalStock = $product->branchInventories->sum('quantity');
                                                $minStock = $product->minimum_stock;
                                            ?>
                                            <?php if($totalStock <= 0): ?>
                                                <span class="badge bg-danger"><?php echo e(__('غير متوفر')); ?></span>
                                            <?php elseif($totalStock <= $minStock): ?>
                                                <span class="badge bg-warning"><?php echo e(__('منخفض')); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-success"><?php echo e(__('متوفر')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="9" class="text-center"><?php echo e(__('لا توجد بيانات')); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        <?php echo e($products->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .form-select {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .text-start {
            text-align: right !important;
        }
        .rtl .text-end {
            text-align: left !important;
        }
        .rtl .form-control {
            text-align: right;
        }
        .rtl .form-control::placeholder {
            text-align: right;
        }
        .rtl .card-title {
            text-align: right;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .container-fluid {
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }
        }
    </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
    <script>
        function printReport() {
            window.print();
        }

        function exportToExcel() {
            // Add Excel export functionality here
            alert('سيتم إضافة وظيفة التصدير إلى Excel قريباً');
        }
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\reports\inventory.blade.php ENDPATH**/ ?>