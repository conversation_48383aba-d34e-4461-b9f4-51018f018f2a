<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">معاملات الحسابات</h2>
                    <a href="<?php echo e(route('account-transactions.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة معاملة جديدة
                    </a>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" action="<?php echo e(route('account-transactions.index')); ?>">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                        value="<?php echo e(request('search')); ?>" placeholder="رقم المرجع، الوصف...">
                                </div>
                                <div class="col-md-2">
                                    <label for="account_id" class="form-label">الحساب</label>
                                    <select class="form-select" id="account_id" name="account_id">
                                        <option value="">جميع الحسابات</option>
                                        <?php $__currentLoopData = $accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($account->id); ?>"
                                                <?php echo e(request('account_id') == $account->id ? 'selected' : ''); ?>>
                                                <?php echo e($account->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="type" class="form-label">النوع</label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="">جميع الأنواع</option>
                                        <option value="credit" <?php echo e(request('type') == 'credit' ? 'selected' : ''); ?>>دائن
                                        </option>
                                        <option value="debit" <?php echo e(request('type') == 'debit' ? 'selected' : ''); ?>>مدين
                                        </option>
                                        <option value="transfer" <?php echo e(request('type') == 'transfer' ? 'selected' : ''); ?>>
                                            تحويل</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from"
                                        value="<?php echo e(request('date_from')); ?>">
                                </div>
                                <div class="col-md-2">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to"
                                        value="<?php echo e(request('date_to')); ?>">
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <?php if($transactions->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم المرجع</th>
                                            <th>الحساب</th>
                                            <th>النوع</th>
                                            <th>المبلغ</th>
                                            <th>الرصيد قبل</th>
                                            <th>الرصيد بعد</th>
                                            <th>التاريخ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <span
                                                        class="badge bg-secondary"><?php echo e($transaction->reference_number); ?></span>
                                                </td>
                                                <td>
                                                    <div class="fw-bold"><?php echo e($transaction->account->name); ?></div>
                                                    <small class="text-muted"><?php echo e($transaction->account->type); ?></small>
                                                </td>
                                                <td>
                                                    <?php if($transaction->type === 'credit'): ?>
                                                        <span class="badge bg-success">دائن</span>
                                                    <?php elseif($transaction->type === 'debit'): ?>
                                                        <span class="badge bg-danger">مدين</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-info">تحويل</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="fw-bold"><?php echo e(number_format($transaction->amount, 2)); ?>

                                                        ريال</span>
                                                </td>
                                                <td><?php echo e(number_format($transaction->balance_before, 2)); ?> ريال</td>
                                                <td><?php echo e(number_format($transaction->balance_after, 2)); ?> ريال</td>
                                                <td>
                                                    <div><?php echo e($transaction->created_at->format('Y-m-d')); ?></div>
                                                    <small
                                                        class="text-muted"><?php echo e($transaction->created_at->format('H:i')); ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="<?php echo e(route('account-transactions.show', $transaction)); ?>"
                                                            class="btn btn-sm btn-outline-info" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="<?php echo e(route('account-transactions.edit', $transaction)); ?>"
                                                            class="btn btn-sm btn-outline-primary" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="d-flex justify-content-center mt-4">
                                <?php echo e($transactions->withQueryString()->links()); ?>

                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد معاملات</h5>
                                <p class="text-muted">لم يتم العثور على أي معاملات مطابقة للبحث</p>
                                <a href="<?php echo e(route('account-transactions.create')); ?>" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة معاملة جديدة
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\account_transactions\index.blade.php ENDPATH**/ ?>