<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-exchange-alt text-primary me-2"></i>
                    تفاصيل طلب النقل <?php echo e($inventoryTransfer->transfer_number); ?>

                </h1>
                <p class="text-muted mb-0">عرض تفاصيل طلب نقل المخزون والمنتجات المرتبطة</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(user_route('inventory-transfers.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
                
                <?php if($inventoryTransfer->canBeApproved() && auth()->user()->isAdmin()): ?>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#approveModal">
                        <i class="fas fa-check me-2"></i>موافقة
                    </button>
                <?php endif; ?>
                
                <?php if($inventoryTransfer->canBeShipped() && auth()->user()->isAdmin()): ?>
                    <form method="POST" action="<?php echo e(user_route('inventory-transfers.ship', $inventoryTransfer)); ?>" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn btn-info" onclick="return confirm('هل أنت متأكد من شحن هذا الطلب؟')">
                            <i class="fas fa-shipping-fast me-2"></i>شحن
                        </button>
                    </form>
                <?php endif; ?>
                
                <?php if($inventoryTransfer->canBeReceived()): ?>
                    <form method="POST" action="<?php echo e(user_route('inventory-transfers.receive', $inventoryTransfer)); ?>" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn btn-primary" onclick="return confirm('هل أنت متأكد من استلام هذا الطلب؟')">
                            <i class="fas fa-check-circle me-2"></i>استلام
                        </button>
                    </form>
                <?php endif; ?>
                
                <?php if($inventoryTransfer->canBeCancelled()): ?>
                    <form method="POST" action="<?php echo e(user_route('inventory-transfers.cancel', $inventoryTransfer)); ?>" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من إلغاء هذا الطلب؟')">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>

        <div class="row">
            <!-- Transfer Information -->
            <div class="col-lg-8">
                <!-- Basic Info -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">معلومات النقل</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">رقم النقل:</td>
                                        <td><?php echo e($inventoryTransfer->transfer_number); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">نوع النقل:</td>
                                        <td>
                                            <?php
                                                $typeLabels = [
                                                    'store_to_branch' => 'من مخزن إلى فرع',
                                                    'branch_to_store' => 'من فرع إلى مخزن',
                                                    'store_to_store' => 'من مخزن إلى مخزن',
                                                    'branch_to_branch' => 'من فرع إلى فرع'
                                                ];
                                            ?>
                                            <span class="badge bg-info"><?php echo e($typeLabels[$inventoryTransfer->type] ?? $inventoryTransfer->type); ?></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">الحالة:</td>
                                        <td>
                                            <?php
                                                $statusColors = [
                                                    'pending' => 'warning',
                                                    'approved' => 'info',
                                                    'in_transit' => 'secondary',
                                                    'completed' => 'success',
                                                    'cancelled' => 'danger'
                                                ];
                                                $statusLabels = [
                                                    'pending' => 'في الانتظار',
                                                    'approved' => 'موافق عليه',
                                                    'in_transit' => 'في الطريق',
                                                    'completed' => 'مكتمل',
                                                    'cancelled' => 'ملغي'
                                                ];
                                            ?>
                                            <span class="badge bg-<?php echo e($statusColors[$inventoryTransfer->status] ?? 'secondary'); ?>">
                                                <?php echo e($statusLabels[$inventoryTransfer->status] ?? $inventoryTransfer->status); ?>

                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">طلب بواسطة:</td>
                                        <td><?php echo e($inventoryTransfer->requestedBy->name ?? 'غير محدد'); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">تاريخ الطلب:</td>
                                        <td><?php echo e($inventoryTransfer->requested_at ? $inventoryTransfer->requested_at->format('Y-m-d H:i') : $inventoryTransfer->created_at->format('Y-m-d H:i')); ?></td>
                                    </tr>
                                    <?php if($inventoryTransfer->approved_at): ?>
                                        <tr>
                                            <td class="fw-bold">تاريخ الموافقة:</td>
                                            <td><?php echo e($inventoryTransfer->approved_at->format('Y-m-d H:i')); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">موافق بواسطة:</td>
                                            <td><?php echo e($inventoryTransfer->approvedBy->name ?? 'غير محدد'); ?></td>
                                        </tr>
                                    <?php endif; ?>
                                    <?php if($inventoryTransfer->received_at): ?>
                                        <tr>
                                            <td class="fw-bold">تاريخ الاستلام:</td>
                                            <td><?php echo e($inventoryTransfer->received_at->format('Y-m-d H:i')); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">مستلم بواسطة:</td>
                                            <td><?php echo e($inventoryTransfer->receivedBy->name ?? 'غير محدد'); ?></td>
                                        </tr>
                                    <?php endif; ?>
                                </table>
                            </div>
                        </div>
                        
                        <?php if($inventoryTransfer->notes): ?>
                            <div class="mt-3">
                                <h6 class="fw-bold">ملاحظات:</h6>
                                <p class="text-muted"><?php echo e($inventoryTransfer->notes); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Transfer Items -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">المنتجات</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية المطلوبة</th>
                                        <?php if($inventoryTransfer->status !== 'pending'): ?>
                                            <th>الكمية الموافق عليها</th>
                                        <?php endif; ?>
                                        <?php if($inventoryTransfer->status === 'completed'): ?>
                                            <th>الكمية المستلمة</th>
                                        <?php endif; ?>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $inventoryTransfer->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle bg-primary text-white me-2">
                                                        <?php echo e(substr($item->product->name, 0, 1)); ?>

                                                    </div>
                                                    <div>
                                                        <div class="fw-bold"><?php echo e($item->product->name); ?></div>
                                                        <small class="text-muted"><?php echo e($item->product->sku ?? 'بدون رمز'); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo e(number_format($item->requested_quantity, 2)); ?></td>
                                            <?php if($inventoryTransfer->status !== 'pending'): ?>
                                                <td><?php echo e(number_format($item->approved_quantity ?? 0, 2)); ?></td>
                                            <?php endif; ?>
                                            <?php if($inventoryTransfer->status === 'completed'): ?>
                                                <td><?php echo e(number_format($item->received_quantity ?? 0, 2)); ?></td>
                                            <?php endif; ?>
                                            <td><?php echo e($item->notes ?? '-'); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Information -->
            <div class="col-lg-4">
                <!-- Source Location -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-secondary">المصدر</h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="avatar-circle bg-secondary text-white mx-auto mb-3" style="width: 60px; height: 60px; font-size: 24px;">
                            <?php echo e(substr($inventoryTransfer->source->name ?? 'غير محدد', 0, 1)); ?>

                        </div>
                        <h5 class="fw-bold"><?php echo e($inventoryTransfer->source->name ?? 'غير محدد'); ?></h5>
                        <p class="text-muted mb-0">
                            <?php echo e($inventoryTransfer->source_type === 'store' ? 'مخزن' : 'فرع'); ?>

                            <?php if($inventoryTransfer->source_type === 'store' && $inventoryTransfer->source): ?>
                                <?php if($inventoryTransfer->source->isIndependent()): ?>
                                    (مستقل)
                                <?php else: ?>
                                    (<?php echo e($inventoryTransfer->source->branch->name ?? 'فرع غير محدد'); ?>)
                                <?php endif; ?>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>

                <!-- Destination Location -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">الوجهة</h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="avatar-circle bg-success text-white mx-auto mb-3" style="width: 60px; height: 60px; font-size: 24px;">
                            <?php echo e(substr($inventoryTransfer->destination->name ?? 'غير محدد', 0, 1)); ?>

                        </div>
                        <h5 class="fw-bold"><?php echo e($inventoryTransfer->destination->name ?? 'غير محدد'); ?></h5>
                        <p class="text-muted mb-0">
                            <?php echo e($inventoryTransfer->destination_type === 'store' ? 'مخزن' : 'فرع'); ?>

                            <?php if($inventoryTransfer->destination_type === 'store' && $inventoryTransfer->destination): ?>
                                <?php if($inventoryTransfer->destination->isIndependent()): ?>
                                    (مستقل)
                                <?php else: ?>
                                    (<?php echo e($inventoryTransfer->destination->branch->name ?? 'فرع غير محدد'); ?>)
                                <?php endif; ?>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
    </style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\inventory-transfers\show.blade.php ENDPATH**/ ?>