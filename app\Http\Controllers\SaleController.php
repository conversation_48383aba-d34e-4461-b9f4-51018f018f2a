<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Category;
use App\Models\Branch;
use App\Models\BranchInventory;
use App\Models\Account;
use App\Models\AccountTransaction;
use App\Services\PriceResolutionService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SaleController extends Controller
{
    protected $priceResolutionService;

    public function __construct(PriceResolutionService $priceResolutionService)
    {
        $this->priceResolutionService = $priceResolutionService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $query = Sale::with(['customer', 'items.product', 'branch', 'store', 'payments'])
            ->accessibleBy($user);

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                    ->orWhereHas('customer', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Customer filter
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // Date range filter
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->whereBetween('created_at', [
                $request->start_date,
                $request->end_date . ' 23:59:59'
            ]);
        } elseif ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        } elseif ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Branch filter (for admin users)
        if ($request->filled('branch_id') && $user->canAccessAllBranches()) {
            $query->where('branch_id', $request->branch_id);
        }

        // Sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');

        // Validate sort fields
        $allowedSortFields = ['created_at', 'invoice_number', 'total_amount', 'customer_name'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'created_at';
        }

        if ($sortField === 'customer_name') {
            $query->leftJoin('customers', 'sales.customer_id', '=', 'customers.id')
                ->orderBy('customers.name', $sortDirection)
                ->select('sales.*');
        } else {
            $query->orderBy($sortField, $sortDirection);
        }

        // Handle pagination
        $perPage = $request->get('per_page', 10);
        $sales = $query->paginate($perPage);

        // Get branches for filter (admin only)
        $branches = $user->canAccessAllBranches()
            ? Branch::get()
            : collect();

        // Get customers for filter
        $customers = Customer::orderBy('name')->get();

        // Calculate statistics
        $statsQuery = Sale::accessibleBy($user);

        $stats = [
            'today_sales' => (clone $statsQuery)->whereDate('created_at', today())->sum('total_amount'),
            'today_sales_count' => (clone $statsQuery)->whereDate('created_at', today())->count(),
            'month_sales' => (clone $statsQuery)->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)->sum('total_amount'),
            'month_sales_count' => (clone $statsQuery)->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)->count(),
            'avg_sale_value' => (clone $statsQuery)->avg('total_amount') ?? 0,
            'pending_sales' => (clone $statsQuery)->where('status', 'pending')->count(),
        ];

        return view('sales.index', compact('sales', 'branches', 'customers', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $user = $request->user();

        // Get accessible branches
        $branches = $user->canAccessAllBranches()
            ? Branch::get()
            : Branch::where('id', $user->branch_id)->get();

        // Get products with inventory in accessible branches
        $products = Product::whereHas('branchInventories', function ($query) use ($user) {
            $query->where('quantity', '>', 0);
            if (!$user->canAccessAllBranches()) {
                $query->where('branch_id', $user->branch_id);
            }
        })->with(['branchInventories' => function ($query) use ($user) {
            $query->where('quantity', '>', 0);
            if (!$user->canAccessAllBranches()) {
                $query->where('branch_id', $user->branch_id);
            }
        }])->get()->map(function ($product) {
            $inventory = $product->branchInventories->first();
            $product->sale_price_1 = $inventory?->sale_price_1 ?? $product->selling_price ?? 0;
            $product->sale_price_2 = $inventory?->sale_price_2 ?? 0;
            $product->sale_price_3 = $inventory?->sale_price_3 ?? 0;
            return $product;
        });

        $customers = Customer::all();
        $categories = Category::all();

        // Check if this is a seller and use seller-specific view
        if ($user->hasRole('seller')) {
            return view('seller.sales.create', compact('products', 'customers', 'branches', 'categories'));
        }

        return view('sales.create', compact('products', 'customers', 'branches', 'categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Add debugging at the start
        if ($request->expectsJson()) {
            Log::info('Quick sale request received', [
                'data' => $request->all(),
                'user' => $request->user()->id ?? 'unknown'
            ]);
        }

        $user = $request->user();

        $validated = $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'branch_id' => 'required|exists:branches,id',
            'discount_amount' => 'nullable|numeric|min:0',
            'discount_type' => 'nullable|in:amount,percentage',
            'paid_amount' => 'required|numeric|min:0',
            'remaining_money' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'products' => 'required|array|min:1',
            'products.*.product_id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
            'products.*.sale_price' => 'required|numeric|min:0',
            'products.*.discount' => 'nullable|numeric|min:0',
            'print_after_save' => 'nullable|boolean',
        ]);

        // Validate branch access
        if (!$user->canAccessBranch($validated['branch_id'])) {
            abort(403, 'You do not have access to this branch.');
        }

        try {
            DB::beginTransaction();

            // Generate invoice number
            $invoiceNumber = 'INV-' . date('Ymd') . '-' . Str::random(6);

            // Transform products data for PriceResolutionService and handle individual discounts
            $transformedProducts = [];
            $totalProductDiscounts = 0;

            foreach ($validated['products'] as $productData) {
                // Calculate individual product discount
                $quantity = $productData['quantity'];
                $salePrice = $productData['sale_price'];
                $discount = $productData['discount'] ?? 0;

                // Apply individual product discount to get final price
                $finalPrice = $salePrice - ($discount / $quantity); // Distribute discount across quantity

                $transformedProducts[] = [
                    'product_id' => $productData['product_id'],
                    'quantity' => $quantity,
                    'price' => $finalPrice, // Use final price after individual discount
                ];

                // Track total product-level discounts for record keeping
                $totalProductDiscounts += $discount;
            }

            // Validate prices and calculate total using PriceResolutionService
            $saleCalculation = $this->priceResolutionService->calculateSaleTotal(
                $transformedProducts,
                $validated['branch_id'],
                $user
            );

            // Check inventory availability and update quantities
            foreach ($validated['products'] as $productData) {
                $productModel = Product::findOrFail($productData['product_id']);
                $branchInventory = BranchInventory::where('branch_id', $validated['branch_id'])
                    ->where('product_id', $productData['product_id'])
                    ->first();

                if (!$branchInventory || $branchInventory->quantity < $productData['quantity']) {
                    throw new \Exception("المنتج {$productModel->name} غير متوفر بالكمية المطلوبة في هذا الفرع");
                }

                // Update branch inventory
                $branchInventory->decrement('quantity', $productData['quantity']);
            }

            $total = $saleCalculation['total'];

            // Apply discount based on type
            $discountAmount = $validated['discount_amount'] ?? 0;
            $discountType = $validated['discount_type'] ?? 'amount';

            if ($discountType === 'percentage') {
                $discountValue = ($total * $discountAmount) / 100;
            } else {
                $discountValue = $discountAmount;
            }

            // Calculate final amount after discount
            $finalAmount = max(0, $total - $discountValue);

            // Calculate remaining money
            $remainingMoney = max(0, $finalAmount - $validated['paid_amount']);

            // Create sale
            $sale = Sale::create([
                'invoice_number' => $invoiceNumber,
                'customer_id' => $validated['customer_id'],
                'branch_id' => $validated['branch_id'],
                'user_id' => $user->id,
                'total_amount' => $total, // Store original total before discount
                'discount_amount' => $discountValue,
                'paid_amount' => $validated['paid_amount'],
                'notes' => $validated['notes'],
                'status' => 'completed',
            ]);

            // Create sale items with final pricing (including individual discounts)
            $saleItems = [];
            foreach ($validated['products'] as $productData) {
                $quantity = $productData['quantity'];
                $salePrice = $productData['sale_price'];
                $discount = $productData['discount'] ?? 0;

                // Calculate final subtotal after individual product discount
                $subtotal = ($salePrice * $quantity) - $discount;

                $saleItems[] = [
                    'product_id' => $productData['product_id'],
                    'quantity' => $quantity,
                    'price' => $salePrice, // Store original sale price
                    'subtotal' => $subtotal, // Final subtotal after discount
                ];
            }
            $sale->items()->createMany($saleItems);

            // Log price warnings if any
            if (!empty($saleCalculation['warnings'])) {
                Log::warning('Sale created with price warnings', [
                    'sale_id' => $sale->id,
                    'warnings' => $saleCalculation['warnings'],
                    'user_id' => $user->id
                ]);
            }

            // Handle customer account transactions if customer exists
            if ($validated['customer_id']) {
                try {
                    $customer = Customer::findOrFail($validated['customer_id']);
                    $customerAccount = $customer->account;

                    if ($customerAccount) {
                        // Create transaction for paid amount
                        if ($validated['paid_amount'] > 0) {
                            AccountTransaction::create([
                                'account_id' => $customerAccount->id,
                                'reference_number' => 'SAL-' . $sale->id . '-PAY',
                                'type' => 'withdrawal',
                                'amount' => -1 * $validated['paid_amount'],
                                'description' => 'دفعة مقابل عملية بيع رقم ' . $sale->id,
                                'transactionable_type' => Sale::class,
                                'transactionable_id' => $sale->id,
                                'user_id' => $user->id
                            ]);
                        }

                        // Create transaction for remaining amount
                        if ($finalAmount - $validated['paid_amount'] > 0) {
                            AccountTransaction::create([
                                'account_id' => $customerAccount->id,
                                'reference_number' => 'SAL-' . $sale->id . '-DUE',
                                'type' => 'credit',
                                'amount' => -1 * ($finalAmount - $validated['paid_amount']),
                                'description' => 'مبلغ متبقي مقابل عملية بيع رقم ' . $sale->id,
                                'transactionable_type' => Sale::class,
                                'transactionable_id' => $sale->id,
                                'user_id' => $user->id
                            ]);
                        }
                    }
                } catch (\Exception $e) {
                    // Log the error but don't fail the sale
                    Log::error('Failed to create customer account transaction: ' . $e->getMessage());
                }
            }



            DB::commit();

            // Prepare success message with warnings
            $successMessage = 'تم إنشاء عملية البيع بنجاح';
            $warningsMessage = '';

            if (!empty($saleCalculation['warnings'])) {
                $warningsMessage = ' - تحذيرات: ' . implode(', ', $saleCalculation['warnings']);
            }

            // Handle JSON requests (from quick sale)
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $successMessage,
                    'warnings' => $saleCalculation['warnings'] ?? [],
                    'sale_id' => $sale->id
                ]);
            }

            // Handle regular form requests
            $fullMessage = $successMessage . $warningsMessage;

            // Check if print was requested
            if ($validated['print_after_save'] ?? false) {
                if ($user->hasRole('admin')) {
                    return redirect()->route('admin.sales.print', $sale)
                        ->with('success', $fullMessage);
                } else {
                    return redirect()->route('seller.sales.print', $sale)
                        ->with('success', $fullMessage);
                }
            }

            // Regular redirect to show page
            if ($user->hasRole('admin')) {
                return redirect()->route('admin.sales.show', $sale)
                    ->with('success', $fullMessage);
            } else {
                return redirect()->route('seller.sales.show', $sale)
                    ->with('success', $fullMessage);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            // Handle JSON requests (from quick sale)
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 422);
            }

            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Sale $sale)
    {
        $sale->load(['customer', 'items.product', 'branch', 'payments.user']);
        return view('sales.show', compact('sale'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Sale $sale)
    {
        if ($sale->status !== 'pending') {
            return redirect()->route('sales.index')
                ->with('error', 'لا يمكن تعديل عملية بيع مكتملة');
        }

        $products = Product::whereHas('branchInventories', function ($query) use ($sale) {
            $query->where('branch_id', $sale->branch_id)
                ->where('quantity', '>', 0);
        })->get();
        $customers = Customer::all();
        $branches = Branch::all();
        $sale->load(['items.product']);

        return view('sales.edit', compact('sale', 'products', 'customers', 'branches'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Sale $sale)
    {
        $user = $request->user();

        if ($sale->status !== 'pending') {
            return redirect()->route('sales.index')
                ->with('error', 'لا يمكن تعديل عملية بيع مكتملة');
        }

        // Check if user can access this sale
        if (!$sale->canBeAccessedBy($user)) {
            abort(403, 'You do not have access to this sale.');
        }

        $validated = $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'branch_id' => 'required|exists:branches,id',
            'discount_amount' => 'nullable|numeric|min:0',
            'paid_amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string',
            'products' => 'required|array|min:1',
            'products.*.product_id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
            'products.*.price' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            // Restore old inventory
            foreach ($sale->items as $item) {
                $branchInventory = BranchInventory::where('branch_id', $sale->branch_id)
                    ->where('product_id', $item->product_id)
                    ->first();
                if ($branchInventory) {
                    $branchInventory->increment('quantity', $item->quantity);
                }
            }

            // Validate prices and calculate new total using PriceResolutionService
            $saleCalculation = $this->priceResolutionService->calculateSaleTotal(
                $validated['products'],
                $validated['branch_id'],
                $user
            );

            // Check inventory availability and update quantities
            foreach ($validated['products'] as $product) {
                $productModel = Product::findOrFail($product['product_id']);
                $branchInventory = BranchInventory::where('branch_id', $validated['branch_id'])
                    ->where('product_id', $product['product_id'])
                    ->first();

                if (!$branchInventory || $branchInventory->quantity < $product['quantity']) {
                    throw new \Exception("المنتج {$productModel->name} غير متوفر بالكمية المطلوبة في هذا الفرع");
                }

                // Update branch inventory
                $branchInventory->decrement('quantity', $product['quantity']);
            }

            $total = $saleCalculation['total'];
            $items = $saleCalculation['items'];

            // Store original total and discount amount
            $discountAmount = $validated['discount_amount'] ?? 0;

            // Update sale
            $sale->update([
                'customer_id' => $validated['customer_id'],
                'branch_id' => $validated['branch_id'],
                'total_amount' => $total, // Store original total before discount
                'discount_amount' => $discountAmount,
                'paid_amount' => $validated['paid_amount'],
                'notes' => $validated['notes']
            ]);

            // Delete old items and create new ones
            $sale->items()->delete();
            $sale->items()->createMany($items);

            // Handle customer account transactions
            if ($validated['customer_id']) {
                $customer = Customer::findOrFail($validated['customer_id']);
                $customerAccount = $customer->account;

                if ($customerAccount) {
                    // Delete old transactions
                    AccountTransaction::where('transactionable_type', Sale::class)
                        ->where('transactionable_id', $sale->id)
                        ->delete();

                    // Create new transactions
                    if ($validated['paid_amount'] > 0) {
                        AccountTransaction::create([
                            'account_id' => $customerAccount->id,
                            'amount' => -1 * $validated['paid_amount'],
                            'description' => 'دفعة مقابل عملية بيع رقم ' . $sale->id,
                            'transactionable_type' => Sale::class,
                            'transactionable_id' => $sale->id,
                            'user_id' => $user->id
                        ]);
                    }

                    // Calculate final amount after discount
                    $finalAmount = $total - $discountAmount;

                    if ($finalAmount - $validated['paid_amount'] > 0) {
                        AccountTransaction::create([
                            'account_id' => $customerAccount->id,
                            'type' => 'credit',
                            'amount' => -1 * ($finalAmount - $validated['paid_amount']),
                            'description' => 'مبلغ متبقي مقابل عملية بيع رقم ' . $sale->id,
                            'transactionable_type' => Sale::class,
                            'transactionable_id' => $sale->id,
                            'user_id' => $user->id
                        ]);
                    }
                }
            }

            DB::commit();

            return redirect()->route('sales.show', $sale)
                ->with('success', 'تم تحديث عملية البيع بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Sale $sale)
    {
        if ($sale->status !== 'pending') {
            return redirect()->route('sales.index')
                ->with('error', 'لا يمكن حذف عملية بيع مكتملة');
        }

        try {
            DB::beginTransaction();

            // Restore branch inventory
            foreach ($sale->items as $item) {
                $branchInventory = BranchInventory::where('branch_id', $sale->branch_id)
                    ->where('product_id', $item->product_id)
                    ->first();
                if ($branchInventory) {
                    $branchInventory->increment('quantity', $item->quantity);
                }
            }

            // Delete account transactions
            AccountTransaction::where('transactionable_type', Sale::class)
                ->where('transactionable_id', $sale->id)
                ->delete();

            // Delete sale items and sale
            $sale->items()->delete();
            $sale->delete();

            DB::commit();

            return redirect()->route('sales.index')
                ->with('success', 'تم حذف عملية البيع بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Print the sale invoice.
     */
    public function print(Sale $sale)
    {
        $sale->load(['customer', 'items.product', 'branch', 'user']);
        return view('sales.print', compact('sale'));
    }

    /**
     * Get product price information for a specific branch
     */
    public function getProductPrice(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'branch_id' => 'required|exists:branches,id',
            'price_level' => 'nullable|integer|min:1|max:3'
        ]);

        $user = $request->user();
        $product = Product::findOrFail($request->product_id);
        $priceLevel = $request->price_level ?? 1;

        // Check branch access
        if (!$user->canAccessBranch($request->branch_id)) {
            return response()->json(['error' => 'Access denied to this branch'], 403);
        }

        try {
            $priceInfo = $this->priceResolutionService->getSellingPrice(
                $product,
                $request->branch_id,
                $priceLevel
            );

            $suggestions = $this->priceResolutionService->getPriceSuggestions(
                $product,
                $request->branch_id
            );

            return response()->json([
                'success' => true,
                'price_info' => $priceInfo,
                'suggestions' => $suggestions,
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Validate a proposed selling price
     */
    public function validatePrice(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'branch_id' => 'required|exists:branches,id',
            'price' => 'required|numeric|min:0'
        ]);

        $user = $request->user();
        $product = Product::findOrFail($request->product_id);

        // Check branch access
        if (!$user->canAccessBranch($request->branch_id)) {
            return response()->json(['error' => 'Access denied to this branch'], 403);
        }

        try {
            $validation = $this->priceResolutionService->validateSellingPrice(
                $request->price,
                $product,
                $request->branch_id,
                $user
            );

            return response()->json([
                'success' => true,
                'validation' => $validation
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Enhanced product search API for sales create
     */
    public function searchProducts(Request $request)
    {
        // Ensure we return JSON for AJAX requests
        if ($request->expectsJson() || $request->ajax()) {
            $request->headers->set('Accept', 'application/json');
        }

        $user = $request->user();

        if (!$user) {
            return response()->json(['success' => false, 'error' => 'User not authenticated'], 401);
        }

        $search = $request->get('search', '');
        $branchId = $request->get('branch_id');

        if (!$branchId) {
            return response()->json(['success' => false, 'error' => 'Branch ID is required'], 400);
        }

        // Validate branch access
        if (!$user->canAccessBranch($branchId)) {
            return response()->json(['success' => false, 'error' => 'Access denied to this branch'], 403);
        }

        try {
            $query = Product::with(['category', 'branchInventories' => function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            }])
                ->whereHas('branchInventories', function ($q) use ($branchId) {
                    $q->where('branch_id', $branchId)->where('quantity', '>', 0);
                });

            if (!empty($search)) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                        ->orWhere('sku', 'LIKE', "%{$search}%")
                        ->orWhereHas('category', function ($categoryQuery) use ($search) {
                            $categoryQuery->where('name', 'LIKE', "%{$search}%");
                        });
                });
            }

            $products = $query->limit(20)->get()->map(function ($product) use ($branchId) {
                $inventory = $product->branchInventories->first();

                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'category' => $product->category?->name,
                    'cost_price' => $inventory?->cost_price ?? $product->price,
                    'sale_price_1' => $inventory?->sale_price_1 ?? $product->selling_price,
                    'sale_price_2' => $inventory?->sale_price_2,
                    'sale_price_3' => $inventory?->sale_price_3,
                    'available_quantity' => $inventory?->quantity ?? 0,
                    'minimum_stock' => $product->minimum_stock,
                    'is_low_stock' => ($inventory?->quantity ?? 0) <= $product->minimum_stock,
                ];
            });

            return response()->json([
                'success' => true,
                'products' => $products
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
