<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-user text-primary"></i> {{ __('تفاصيل المستخدم') }}
                </h2>
                <p class="text-muted small mb-0">عرض معلومات وإحصائيات المستخدم</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.settings.users.edit', $user) }}" class="btn btn-primary">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <a href="{{ route('admin.settings.users') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid">
        <!-- User Info Card -->
        <div class="row mb-4">
            <div class="col-lg-4">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user-circle"></i> معلومات المستخدم
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            @if($user->avatar)
                                <img src="{{ asset('storage/' . $user->avatar) }}" 
                                     alt="{{ $user->name }}" 
                                     class="rounded-circle" 
                                     style="width: 100px; height: 100px; object-fit: cover;">
                            @else
                                <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center text-white"
                                     style="width: 100px; height: 100px; font-size: 2rem;">
                                    {{ strtoupper(substr($user->name, 0, 1)) }}
                                </div>
                            @endif
                        </div>
                        
                        <h4 class="mb-1">{{ $user->name }}</h4>
                        <p class="text-muted mb-2">{{ $user->email }}</p>
                        
                        <div class="mb-3">
                            @if($user->is_active)
                                <span class="badge bg-success">نشط</span>
                            @else
                                <span class="badge bg-danger">غير نشط</span>
                            @endif
                        </div>

                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h5 class="mb-0 text-primary">{{ $user->role->name }}</h5>
                                    <small class="text-muted">الدور</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h5 class="mb-0 text-success">{{ $user->branch->name ?? 'غير محدد' }}</h5>
                                <small class="text-muted">الفرع</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-8">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي المبيعات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_sales'] }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            قيمة المبيعات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ number_format($stats['total_sales_amount'], 2) }} ج.م
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card shadow">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-address-card"></i> معلومات الاتصال
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">البريد الإلكتروني:</label>
                                <p class="mb-0">{{ $user->email }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">رقم الهاتف:</label>
                                <p class="mb-0">{{ $user->phone ?? 'غير محدد' }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">آخر تسجيل دخول:</label>
                                <p class="mb-0">
                                    @if($stats['last_login'])
                                        {{ $stats['last_login']->diffForHumans() }}
                                    @else
                                        لم يسجل دخول بعد
                                    @endif
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ إنشاء الحساب:</label>
                                <p class="mb-0">{{ $stats['account_created']->format('Y-m-d H:i') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Sales Activity -->
        @if($recentSales->count() > 0)
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-history"></i> آخر المبيعات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>الفرع</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentSales as $sale)
                                    <tr>
                                        <td class="fw-bold">{{ $sale->invoice_number }}</td>
                                        <td>{{ $sale->customer->name ?? 'عميل نقدي' }}</td>
                                        <td>{{ $sale->branch->name ?? 'غير محدد' }}</td>
                                        <td class="fw-bold">{{ number_format($sale->total_amount, 2) }} ج.م</td>
                                        <td>{{ $sale->created_at->format('Y-m-d H:i') }}</td>
                                        <td>
                                            <a href="{{ route('admin.sales.show', $sale) }}" 
                                               class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @else
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مبيعات بعد</h5>
                    <p class="text-muted">لم يقم هذا المستخدم بأي عمليات بيع حتى الآن</p>
                </div>
            </div>
        @endif

        <!-- User Permissions -->
        @if($user->role && $user->role->permissions)
            <div class="card shadow mt-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-key"></i> الصلاحيات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($user->role->permissions as $permission)
                            <div class="col-md-4 mb-2">
                                <span class="badge bg-primary">
                                    <i class="fas fa-check"></i> {{ $permission }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif
    </div>
</x-app-layout>
