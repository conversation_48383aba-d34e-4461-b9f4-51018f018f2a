<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('المصروفات')); ?>

            </h2>
            <div>
                <a href="<?php echo e(route('expenses.export')); ?>" class="btn btn-success me-2">
                    <i class="fas fa-file-export"></i> <?php echo e(__('تصدير')); ?>

                </a>
                <a href="<?php echo e(route('expenses.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> <?php echo e(__('إضافة مصروف جديد')); ?>

                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('إجمالي المصروفات')); ?></h5>
                                    <h3 class="mb-0"><?php echo e(number_format($totalExpenses, 2)); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('مصروفات اليوم')); ?></h5>
                                    <h3 class="mb-0"><?php echo e(number_format($todayExpenses, 2)); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('مصروفات الشهر')); ?></h5>
                                    <h3 class="mb-0"><?php echo e(number_format($monthExpenses, 2)); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('عدد المصروفات')); ?></h5>
                                    <h3 class="mb-0"><?php echo e($expensesCount); ?></h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Create Button -->
                    <div class="mb-4">
                        <a href="<?php echo e(route('expenses.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> <?php echo e(__('إضافة مصروف جديد')); ?>

                        </a>
                    </div>

                    <!-- Filters -->
                    <form action="<?php echo e(route('expenses.index')); ?>" method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search"><?php echo e(__('بحث')); ?></label>
                                    <input type="text" name="search" id="search" class="form-control" value="<?php echo e(request('search')); ?>" placeholder="<?php echo e(__('الوصف')); ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="category"><?php echo e(__('التصنيف')); ?></label>
                                    <select name="category" id="category" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                                <?php echo e($category->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="branch"><?php echo e(__('الفرع')); ?></label>
                                    <select name="branch" id="branch" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($branch->id); ?>" <?php echo e(request('branch') == $branch->id ? 'selected' : ''); ?>>
                                                <?php echo e($branch->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_range"><?php echo e(__('الفترة')); ?></label>
                                    <input type="text" name="date_range" id="date_range" class="form-control" value="<?php echo e(request('date_range')); ?>" placeholder="<?php echo e(__('اختر الفترة')); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12 text-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> <?php echo e(__('بحث')); ?>

                                </button>
                                <a href="<?php echo e(route('expenses.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> <?php echo e(__('إعادة تعيين')); ?>

                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Expenses Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('التاريخ')); ?></th>
                                    <th><?php echo e(__('المرجع')); ?></th>
                                    <th><?php echo e(__('التصنيف')); ?></th>
                                    <th><?php echo e(__('الفرع')); ?></th>
                                    <th><?php echo e(__('الوصف')); ?></th>
                                    <th><?php echo e(__('المبلغ')); ?></th>
                                    <th><?php echo e(__('طريقة الدفع')); ?></th>
                                    <th><?php echo e(__('المستخدم')); ?></th>
                                    <th><?php echo e(__('الحالة')); ?></th>
                                    <th><?php echo e(__('الإجراءات')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $expenses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $expense): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($expense->expense_date->format('Y-m-d')); ?></td>
                                        <td><?php echo e($expense->reference); ?></td>
                                        <td>
                                            <span class="badge" style="background-color: <?php echo e($expense->category->color ?: '#6c757d'); ?>">
                                                <?php echo e($expense->category->name); ?>

                                            </span>
                                        </td>
                                        <td><?php echo e($expense->branch->name); ?></td>
                                        <td><?php echo e($expense->description); ?></td>
                                        <td><?php echo e(number_format($expense->amount, 2)); ?></td>
                                        <td><?php echo e($expense->payment_method); ?></td>
                                        <td><?php echo e($expense->user->name); ?></td>
                                        <td>
                                            <?php if($expense->status == 'pending'): ?>
                                                <span class="badge bg-warning"><?php echo e(__('قيد الانتظار')); ?></span>
                                            <?php elseif($expense->status == 'approved'): ?>
                                                <span class="badge bg-success"><?php echo e(__('تمت الموافقة')); ?></span>
                                            <?php elseif($expense->status == 'rejected'): ?>
                                                <span class="badge bg-danger"><?php echo e(__('مرفوض')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="<?php echo e(route('expenses.show', $expense)); ?>" class="btn btn-info btn-sm" title="<?php echo e(__('عرض')); ?>">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('expenses.edit', $expense)); ?>" class="btn btn-primary btn-sm" title="<?php echo e(__('تعديل')); ?>">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="<?php echo e(route('expenses.destroy', $expense)); ?>" method="POST" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('<?php echo e(__('هل أنت متأكد من حذف هذا المصروف؟')); ?>')" title="<?php echo e(__('حذف')); ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="10" class="text-center"><?php echo e(__('لا توجد مصروفات')); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        <?php echo e($expenses->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .form-select {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .btn-group {
            flex-direction: row-reverse;
        }
        .rtl .btn-group .btn {
            margin-right: 0;
            margin-left: 0.25rem;
        }
        .rtl .text-start {
            text-align: right !important;
        }
        .rtl .text-end {
            text-align: left !important;
        }
        .rtl .form-control {
            text-align: right;
        }
        .rtl .form-control::placeholder {
            text-align: right;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .card-body {
            padding: 1.5rem;
        }
        .card-title {
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }
        .badge {
            padding: 0.5em 0.75em;
        }
    </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#date_range').daterangepicker({
                locale: {
                    format: 'YYYY-MM-DD',
                    applyLabel: 'تطبيق',
                    cancelLabel: 'إلغاء',
                    fromLabel: 'من',
                    toLabel: 'إلى',
                    customRangeLabel: 'مخصص',
                    daysOfWeek: ['أحد', 'اثن', 'ثلا', 'أرب', 'خمي', 'جمع', 'سبت'],
                    monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
                }
            });
        });
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\expenses\index.blade.php ENDPATH**/ ?>