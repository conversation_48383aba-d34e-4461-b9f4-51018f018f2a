<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة بيع - <?php echo e($sale->invoice_number); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            body {
                padding: 15px;
                font-size: 14px;
            }

            .no-print {
                display: none !important;
            }

            .page-break {
                page-break-before: always;
            }
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.4;
            color: #000;
        }

        .invoice-header {
            border-bottom: 2px solid #000;
            margin-bottom: 20px;
            padding-bottom: 15px;
        }

        .company-info {
            text-align: center;
            margin-bottom: 20px;
        }

        .company-name {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .invoice-title {
            background: #000;
            color: white;
            padding: 8px 15px;
            display: inline-block;
            font-weight: bold;
            margin: 15px 0;
        }

        .invoice-details {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #000;
        }

        .table {
            border-collapse: collapse;
            width: 100%;
        }

        .table th {
            background: #000 !important;
            color: white !important;
            font-weight: bold;
            text-align: center;
            padding: 10px 8px;
            border: 1px solid #000;
        }

        .table td {
            padding: 8px;
            border: 1px solid #000;
            vertical-align: middle;
        }

        .total-row {
            font-weight: bold;
            background: #000 !important;
            color: white !important;
        }

        .total-row td {
            border: 1px solid #000 !important;
            padding: 10px 8px;
        }

        .footer-note {
            margin-top: 20px;
            padding: 15px;
            border-top: 2px solid #000;
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="no-print mb-3">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i> طباعة
            </button>
            <a href="<?php echo e(user_route('sales.show', $sale)); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> عودة
            </a>
        </div>

        <div class="invoice-header">
            <div class="company-info">
                <?php
                    $settings = \App\Models\Setting::first();
                ?>
                <div class="company-name"><?php echo e($settings->company_name ?? 'نظام نقاط البيع'); ?></div>
                <?php if($settings->company_address): ?>
                    <p class="mb-2"><?php echo e($settings->company_address); ?></p>
                <?php endif; ?>
                <div class="d-flex justify-content-center gap-3 mb-2">
                    <?php if($settings->company_phone): ?>
                        <span><?php echo e($settings->company_phone); ?></span>
                    <?php endif; ?>
                    <?php if($settings->company_email): ?>
                        <span><?php echo e($settings->company_email); ?></span>
                    <?php endif; ?>
                </div>
                <?php if($settings->company_tax_number): ?>
                    <p class="mb-0">الرقم الضريبي: <?php echo e($settings->company_tax_number); ?></p>
                <?php endif; ?>

                <div class="invoice-title">
                    فاتورة بيع رقم: <?php echo e($sale->invoice_number); ?>

                </div>
            </div>

            <div class="row invoice-details">
                <div class="col-6">
                    <h5 class="mb-3">معلومات الفاتورة</h5>
                    <div class="mb-2"><strong>رقم الفاتورة:</strong> <?php echo e($sale->invoice_number); ?></div>
                    <div class="mb-2"><strong>التاريخ:</strong> <?php echo e($sale->created_at->format('Y-m-d')); ?></div>
                    <div class="mb-2"><strong>الوقت:</strong> <?php echo e($sale->created_at->format('H:i')); ?></div>
                    <div class="mb-2"><strong>الفرع:</strong> <?php echo e($sale->branch->name); ?></div>
                    <div class="mb-2"><strong>البائع:</strong> <?php echo e($sale->user->name ?? 'غير محدد'); ?></div>
                </div>
                <div class="col-6">
                    <h5 class="mb-3">معلومات العميل</h5>
                    <?php if($sale->customer): ?>
                        <div class="mb-2"><strong>الاسم:</strong> <?php echo e($sale->customer->name); ?></div>
                        <?php if($sale->customer->email): ?>
                            <div class="mb-2"><strong>البريد الإلكتروني:</strong> <?php echo e($sale->customer->email); ?></div>
                        <?php endif; ?>
                        <?php if($sale->customer->phone): ?>
                            <div class="mb-2"><strong>رقم الهاتف:</strong> <?php echo e($sale->customer->phone); ?></div>
                        <?php endif; ?>
                        <?php if($sale->customer->address): ?>
                            <div class="mb-2"><strong>العنوان:</strong> <?php echo e($sale->customer->address); ?></div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div>عميل نقدي</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>المنتج</th>
                        <th>الكود</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $sale->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($index + 1); ?></td>
                            <td>
                                <strong><?php echo e($item->product->name); ?></strong>
                                <?php if($item->product->category): ?>
                                    <br><small class="text-muted"><?php echo e($item->product->category->name); ?></small>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($item->product->sku ?? '-'); ?></td>
                            <td><?php echo e($item->quantity); ?></td>
                            <td><?php echo e(number_format($item->price, 2)); ?> ج.م</td>
                            <td><?php echo e(number_format($item->subtotal, 2)); ?> ج.م</td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
                <tfoot>
                    <?php
                        $subtotal = $sale->items->sum('subtotal');
                        $finalAmount = $sale->total_amount - $sale->discount_amount;
                        $paidAmount = $sale->getTotalPaymentsAttribute();
                        $remainingAmount = $sale->getActualRemainingAmountAttribute();
                    ?>
                    <tr>
                        <td colspan="5" class="text-start fw-bold">المجموع </td>
                        <td class="fw-bold"><?php echo e(number_format($subtotal, 2)); ?> ج.م</td>
                    </tr>
                    <?php if($sale->discount_amount > 0): ?>
                        <tr>
                            <td colspan="5" class="text-start">الخصم</td>
                            <td>- <?php echo e(number_format($sale->discount_amount, 2)); ?> ج.م</td>
                        </tr>
                    <?php endif; ?>
                    <tr class="total-row">
                        <td colspan="5" class="text-start fw-bold">الإجمالي النهائي</td>
                        <td class="fw-bold"><?php echo e(number_format($finalAmount, 2)); ?> ج.م</td>
                    </tr>
                    <tr>
                        <td colspan="5" class="text-start">المبلغ المدفوع</td>
                        <td class="fw-bold"><?php echo e(number_format($paidAmount, 2)); ?> ج.م</td>
                    </tr>
                    <?php if($remainingAmount > 0): ?>
                        <tr>
                            <td colspan="5" class="text-start">المبلغ المتبقي</td>
                            <td class="fw-bold"><?php echo e(number_format($remainingAmount, 2)); ?> ج.م</td>
                        </tr>
                    <?php elseif($remainingAmount < 0): ?>
                        <tr>
                            <td colspan="5" class="text-start">المبلغ الزائد</td>
                            <td class="fw-bold"><?php echo e(number_format(abs($remainingAmount), 2)); ?> ج.م</td>
                        </tr>
                    <?php endif; ?>
                </tfoot>
            </table>
        </div>

        <?php if($sale->notes): ?>
            <div style="margin-top: 20px; padding: 15px; border: 1px solid #000;">
                <h6 class="mb-2">ملاحظات</h6>
                <p class="mb-0"><?php echo e($sale->notes); ?></p>
            </div>
        <?php endif; ?>

        <div class="footer-note">
            <div class="row align-items-center">
                <div class="col-6">
                    <strong>شكراً لتعاملكم معنا</strong><br>
                    <small>تم إنشاء هذه الفاتورة إلكترونياً</small>
                </div>
                <div class="col-6 text-end">
                    <div class="d-inline-block">
                        <strong>التوقيع</strong><br>
                        <small><?php echo e($sale->user->name ?? 'غير محدد'); ?></small><br>
                        <div style="border-top: 2px solid #000; width: 150px; margin-top: 20px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
<?php /**PATH D:\my-pos-app\resources\views/sales/print.blade.php ENDPATH**/ ?>