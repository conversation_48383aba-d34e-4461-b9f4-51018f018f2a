<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid px-4">
        <!-- Header Section -->
        <div class="row align-items-center mb-4">
            <div class="col-md-4">
                <nav aria-label="breadcrumb" class="d-flex justify-content-start">
                    <ol class="breadcrumb mb-0 bg-light px-3 py-2 rounded">
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(user_route('dashboard')); ?>" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(user_route('categories.index')); ?>" class="text-decoration-none">
                                <i class="fas fa-tags me-1"></i>الفئات
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-plus me-1"></i>إضافة جديدة
                        </li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-8">
                <div class="d-flex align-items-center justify-content-end">
                    <div class="text-end me-3">
                        <h2 class="font-semibold text-xl text-gray-800 leading-tight mb-0">
                            إضافة فئة جديدة
                        </h2>
                        <p class="text-muted mb-0 small">إنشاء فئة جديدة لتصنيف المنتجات</p>
                    </div>
                    <div class="avatar-circle bg-success text-white d-inline-flex">
                        <i class="fas fa-plus"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back Button Row -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-start">
                    <a href="<?php echo e(user_route('categories.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Preview Section -->
            <div class="col-xl-4 col-lg-5">
                <div class="card shadow border-0 h-100">
                    <div class="card-header bg-gradient-success text-white py-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-eye fa-lg me-3"></i>
                            <h5 class="mb-0 fw-bold">معاينة الفئة</h5>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <!-- Live Preview -->
                        <div class="text-center mb-4">
                            <div class="avatar-circle bg-primary text-white mx-auto mb-3"
                                style="width: 80px; height: 80px; font-size: 2rem;">
                                <i class="fas fa-tag"></i>
                            </div>
                            <h4 class="fw-bold text-primary" id="preview-name">اسم الفئة</h4>
                            <p class="text-muted" id="preview-description">وصف الفئة</p>
                        </div>

                        <!-- Preview Details -->
                        <div class="preview-details">
                            <div
                                class="detail-item d-flex justify-content-between align-items-center py-2 border-bottom">
                                <span class="fw-bold text-muted">الاسم:</span>
                                <span id="preview-name-text">غير محدد</span>
                            </div>
                            <div
                                class="detail-item d-flex justify-content-between align-items-center py-2 border-bottom">
                                <span class="fw-bold text-muted">طول الوصف:</span>
                                <span id="preview-desc-length">0 حرف</span>
                            </div>
                            <div class="detail-item d-flex justify-content-between align-items-center py-2">
                                <span class="fw-bold text-muted">الحالة:</span>
                                <span class="badge bg-warning">جديدة</span>
                            </div>
                        </div>

                        <!-- Category Stats Placeholder -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="fw-bold mb-3">إحصائيات الفئة</h6>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="fw-bold text-primary">0</div>
                                    <small class="text-muted">منتج</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold text-success">0 ج.م</div>
                                    <small class="text-muted">قيمة المخزون</small>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-4 pt-3 border-top">
                            <h6 class="fw-bold mb-3">بعد الحفظ يمكنك:</h6>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm" disabled>
                                    <i class="fas fa-plus me-2"></i>إضافة منتجات
                                </button>
                                <button class="btn btn-outline-info btn-sm" disabled>
                                    <i class="fas fa-edit me-2"></i>تعديل الفئة
                                </button>
                                <button class="btn btn-outline-success btn-sm" disabled>
                                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Section -->
            <div class="col-xl-8 col-lg-7">
                <div class="card shadow border-0 h-100">
                    <div class="card-header bg-gradient-primary text-white py-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-edit fa-lg me-3"></i>
                            <h5 class="mb-0 fw-bold">بيانات الفئة</h5>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <form action="<?php echo e(user_route('categories.store')); ?>" method="POST" id="category-form">
                            <?php echo csrf_field(); ?>

                            <!-- Category Name -->
                            <div class="mb-4">
                                <label for="name" class="form-label fw-bold">
                                    <i class="fas fa-tag text-primary me-2"></i>اسم الفئة
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="input-group input-group-lg">
                                    <span class="input-group-text">
                                        <i class="fas fa-tag text-muted"></i>
                                    </span>
                                    <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="name" name="name" value="<?php echo e(old('name')); ?>"
                                        placeholder="أدخل اسم الفئة..." required>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle text-info me-1"></i>
                                    اختر اسماً واضحاً ومميزاً للفئة
                                </div>
                            </div>

                            <!-- Category Description -->
                            <div class="mb-4">
                                <label for="description" class="form-label fw-bold">
                                    <i class="fas fa-align-left text-info me-2"></i>وصف الفئة
                                </label>
                                <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="description" name="description"
                                    rows="5" placeholder="أدخل وصفاً مفصلاً للفئة (اختياري)..."><?php echo e(old('description')); ?></textarea>
                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <div class="form-text">
                                    <i class="fas fa-info-circle text-info me-1"></i>
                                    وصف مفصل يساعد في فهم نوع المنتجات التي تنتمي لهذه الفئة
                                </div>
                            </div>

                            <!-- Form Tips -->
                            <div class="alert alert-info border-0 bg-light">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-lightbulb text-info me-3 mt-1"></i>
                                    <div>
                                        <h6 class="alert-heading mb-2">نصائح لإنشاء فئة فعالة:</h6>
                                        <ul class="mb-0 small">
                                            <li>استخدم أسماء واضحة ومفهومة للفئات</li>
                                            <li>تجنب إنشاء فئات متداخلة أو معقدة</li>
                                            <li>أضف وصفاً يوضح نوع المنتجات المناسبة لهذه الفئة</li>
                                            <li>يمكنك تعديل الفئة لاحقاً إذا احتجت لذلك</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Action Buttons -->
                            <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                                <button type="reset" class="btn btn-outline-warning ">
                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                </button>
                                <button type="submit" class="btn btn-success ">
                                    <i class="fas fa-save me-2"></i>حفظ الفئة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
        <style>
            .avatar-circle {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
            }

            .bg-gradient-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            .form-control:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            }

            .card {
                transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            }

            .input-group-text {
                background-color: #f8f9fa;
                border-color: #dee2e6;
            }

            .btn-lg {
                padding: 0.75rem 1.5rem;
                font-size: 1rem;
            }
        </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Live preview functionality
                const nameInput = document.getElementById('name');
                const descriptionInput = document.getElementById('description');
                const previewName = document.getElementById('preview-name');
                const previewDescription = document.getElementById('preview-description');
                const previewNameText = document.getElementById('preview-name-text');
                const previewDescLength = document.getElementById('preview-desc-length');

                function updatePreview() {
                    const name = nameInput.value.trim() || 'اسم الفئة';
                    const description = descriptionInput.value.trim() || 'وصف الفئة';
                    const descLength = descriptionInput.value.trim().length;

                    // Update main preview
                    previewName.textContent = name;
                    previewDescription.textContent = description.length > 50 ? description.substring(0, 50) + '...' :
                        description;

                    // Update detail preview
                    previewNameText.textContent = name === 'اسم الفئة' ? 'غير محدد' : name;
                    previewDescLength.textContent = descLength + ' حرف';
                }

                nameInput.addEventListener('input', updatePreview);
                descriptionInput.addEventListener('input', updatePreview);

                // Form validation
                const form = document.getElementById('category-form');
                form.addEventListener('submit', function(e) {
                    const name = nameInput.value.trim();

                    if (!name) {
                        e.preventDefault();
                        nameInput.focus();
                        nameInput.classList.add('is-invalid');

                        // Show error message
                        let errorDiv = nameInput.parentNode.querySelector('.invalid-feedback');
                        if (!errorDiv) {
                            errorDiv = document.createElement('div');
                            errorDiv.className = 'invalid-feedback';
                            nameInput.parentNode.appendChild(errorDiv);
                        }
                        errorDiv.textContent = 'اسم الفئة مطلوب';
                        return false;
                    }

                    // Show loading state
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
                    submitBtn.disabled = true;

                    // Re-enable button after 3 seconds (in case of error)
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 3000);
                });

                // Remove validation errors on input
                nameInput.addEventListener('input', function() {
                    this.classList.remove('is-invalid');
                    const errorDiv = this.parentNode.querySelector('.invalid-feedback');
                    if (errorDiv) {
                        errorDiv.remove();
                    }
                });

                // Reset form functionality
                const resetBtn = form.querySelector('button[type="reset"]');
                resetBtn.addEventListener('click', function() {
                    setTimeout(updatePreview, 10); // Update preview after reset
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\categories\create.blade.php ENDPATH**/ ?>