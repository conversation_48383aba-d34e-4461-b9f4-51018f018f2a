<?php if (isset($component)) { $__componentOriginal69dc84650370d1d4dc1b42d016d7226b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69dc84650370d1d4dc1b42d016d7226b = $attributes; } ?>
<?php $component = App\View\Components\GuestLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('guest-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\GuestLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>

    <body class="rtl">
        <div class="vh-100 d-flex align-items-center justify-content-center"
            style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); overflow: hidden;">
            <!-- Background Pattern -->
            <div class="position-absolute w-100 h-100" style="background-image: url('data:image/svg+xml,<svg width="60"
                height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
                <g fill="none" fill-rule="evenodd">
                    <g fill="%23ffffff" fill-opacity="0.05">
                        <circle cx="30" cy="30" r="4" />
                    </g>
            </div>

            <div class="container-fluid">
                <div class="row justify-content-center">
                    <div class="col-md-6 col-lg-5 col-xl-4">
                        <div class="card shadow-lg border-0"
                            style="border-radius: 20px; backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.95); max-height: 90vh; overflow-y: auto;">
                            <div class="card-body p-4">
                                <!-- Logo Section -->
                                <div class="text-center mb-3">
                                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle mb-2"
                                        style="width: 60px; height: 60px; background: linear-gradient(135deg, #2e8b57, #3cb371);">
                                        <i class="fas fa-store text-white" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <h5 class="fw-bold text-dark mb-1" style="font-size: 1rem; line-height: 1.3;">شركة
                                        الاتحاد لتجارة و توزيع الأدوات الصحيه</h5>
                                    <p class="text-muted small mb-0">مرحباً بعودتك، يرجى تسجيل الدخول</p>
                                </div>

                                <!-- Session Status -->
                                <?php if (isset($component)) { $__componentOriginal7c1bf3a9346f208f66ee83b06b607fb5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7c1bf3a9346f208f66ee83b06b607fb5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.auth-session-status','data' => ['class' => 'mb-3','status' => session('status')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('auth-session-status'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mb-3','status' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(session('status'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7c1bf3a9346f208f66ee83b06b607fb5)): ?>
<?php $attributes = $__attributesOriginal7c1bf3a9346f208f66ee83b06b607fb5; ?>
<?php unset($__attributesOriginal7c1bf3a9346f208f66ee83b06b607fb5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7c1bf3a9346f208f66ee83b06b607fb5)): ?>
<?php $component = $__componentOriginal7c1bf3a9346f208f66ee83b06b607fb5; ?>
<?php unset($__componentOriginal7c1bf3a9346f208f66ee83b06b607fb5); ?>
<?php endif; ?>

                                <form method="POST" action="<?php echo e(route('login')); ?>">
                                    <?php echo csrf_field(); ?>

                                    <!-- Email Address -->
                                    <div class="mb-3">
                                        <label for="email" class="form-label fw-semibold text-dark">
                                            <i class="fas fa-envelope me-2 text-primary"></i>البريد الإلكتروني
                                        </label>
                                        <div class="input-group">
                                            <input id="email" type="email" name="email"
                                                value="<?php echo e(old('email')); ?>"
                                                class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                placeholder="أدخل بريدك الإلكتروني" required autofocus
                                                autocomplete="username"
                                                style="border-radius: 10px; border: 2px solid #e9ecef; padding-right: 45px; height: 45px;">
                                            <span class="position-absolute top-50 end-0 translate-middle-y me-3"
                                                style="z-index: 10;">
                                                <i class="fas fa-envelope text-muted"></i>
                                            </span>
                                        </div>
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback d-block">
                                                <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <!-- Password -->
                                    <div class="mb-3">
                                        <label for="password" class="form-label fw-semibold text-dark">
                                            <i class="fas fa-lock me-2 text-primary"></i>كلمة المرور
                                        </label>
                                        <div class="input-group position-relative">
                                            <input id="password" type="password" name="password"
                                                class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                placeholder="أدخل كلمة المرور" required autocomplete="current-password"
                                                style="border-radius: 10px; border: 2px solid #e9ecef; padding-right: 45px; padding-left: 45px; height: 45px;">
                                            <span class="position-absolute top-50 end-0 translate-middle-y me-3"
                                                style="z-index: 10;">
                                                <i class="fas fa-lock text-muted"></i>
                                            </span>
                                            <span
                                                class="position-absolute top-50 start-0 translate-middle-y ms-3 cursor-pointer"
                                                style="z-index: 10;" onclick="togglePassword()">
                                                <i class="fas fa-eye text-muted" id="togglePasswordIcon"></i>
                                            </span>
                                        </div>
                                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback d-block">
                                                <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <!-- Remember Me & Forgot Password -->
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="remember"
                                                id="remember_me">
                                            <label class="form-check-label text-muted small" for="remember_me">
                                                تذكرني
                                            </label>
                                        </div>
                                        <?php if(Route::has('password.request')): ?>
                                            <a href="<?php echo e(route('password.request')); ?>"
                                                class="text-decoration-none text-primary small fw-semibold">
                                                نسيت كلمة المرور؟
                                            </a>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Login Button -->
                                    <div class="d-grid mb-3">
                                        <button type="submit" class="btn text-white fw-semibold"
                                            style="background: linear-gradient(135deg, #2e8b57, #3cb371); border: none; border-radius: 10px; padding: 12px; transition: all 0.3s ease; height: 45px;">
                                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                        </button>
                                    </div>
                                </form>

                                <!-- Footer -->
                                <div class="text-center">
                                    <p class="text-muted small mb-0">
                                        <i class="fas fa-shield-alt me-1"></i>
                                        محمي بأحدث تقنيات الأمان
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            function togglePassword() {
                const passwordInput = document.getElementById('password');
                const toggleIcon = document.getElementById('togglePasswordIcon');

                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    toggleIcon.classList.remove('fa-eye');
                    toggleIcon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    toggleIcon.classList.remove('fa-eye-slash');
                    toggleIcon.classList.add('fa-eye');
                }
            }

            // Add hover effects to login button
            document.addEventListener('DOMContentLoaded', function() {
                const loginBtn = document.querySelector('button[type="submit"]');

                loginBtn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 8px 25px rgba(46, 139, 87, 0.3)';
                });

                loginBtn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });

                // Add focus effects to inputs
                const inputs = document.querySelectorAll('input[type="email"], input[type="password"]');
                inputs.forEach(input => {
                    input.addEventListener('focus', function() {
                        this.style.borderColor = '#2e8b57';
                        this.style.boxShadow = '0 0 0 0.25rem rgba(46, 139, 87, 0.25)';
                    });

                    input.addEventListener('blur', function() {
                        this.style.borderColor = '#e9ecef';
                        this.style.boxShadow = 'none';
                    });
                });
            });
        </script>
    </body>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69dc84650370d1d4dc1b42d016d7226b)): ?>
<?php $attributes = $__attributesOriginal69dc84650370d1d4dc1b42d016d7226b; ?>
<?php unset($__attributesOriginal69dc84650370d1d4dc1b42d016d7226b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69dc84650370d1d4dc1b42d016d7226b)): ?>
<?php $component = $__componentOriginal69dc84650370d1d4dc1b42d016d7226b; ?>
<?php unset($__componentOriginal69dc84650370d1d4dc1b42d016d7226b); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\auth\login.blade.php ENDPATH**/ ?>