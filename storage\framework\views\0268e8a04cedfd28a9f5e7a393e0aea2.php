<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-store text-primary"></i> <?php echo e(__('مخزون المخازن')); ?>

                </h2>
                <p class="text-muted small mb-0">عرض مخزون جميع المخازن</p>
            </div>
            <div>
                <a href="<?php echo e(route('admin.inventory.overview')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للنظرة العامة
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="container-fluid">
        <div class="row">
            <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-success">
                                <i class="fas fa-store"></i> <?php echo e($store->name); ?>

                            </h6>
                            <span class="badge bg-success"><?php echo e($store->code); ?></span>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-building"></i> <?php echo e($store->branch_name); ?>

                                </small>
                            </div>

                            <div class="row no-gutters align-items-center mb-3">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي المنتجات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($store->total_products); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-boxes fa-2x text-gray-300"></i>
                                </div>
                            </div>

                            <div class="row no-gutters align-items-center mb-3">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        إجمالي القيمة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($store->total_value, 2)); ?> ج.م</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>

                            <div class="row no-gutters align-items-center mb-3">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        مخزون منخفض
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($store->low_stock_count); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <a href="<?php echo e(route('admin.inventory.store-details', $store->id)); ?>" class="btn btn-success btn-sm">
                                    <i class="fas fa-eye"></i> عرض التفاصيل
                                </a>
                                <a href="<?php echo e(route('admin.stores.inventory', $store->id)); ?>" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-cog"></i> إدارة المخزون
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <?php if($stores->isEmpty()): ?>
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-store fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مخازن</h5>
                    <p class="text-muted">لم يتم إنشاء أي مخازن بعد</p>
                    <a href="<?php echo e(route('admin.stores.create')); ?>" class="btn btn-success">
                        <i class="fas fa-plus"></i> إضافة مخزن جديد
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\admin\inventory\stores.blade.php ENDPATH**/ ?>