<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h3 mb-0">تحليلات الفروع</h2>
                        <p class="text-muted mb-0">تحليل شامل لأداء جميع الفروع</p>
                    </div>
                    
                </div>
            </div>
        </div>

        <!-- Date Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form id="analyticsFilterForm" class="row g-3">
                            <div class="col-md-3">
                                <label for="period" class="form-label">الفترة الزمنية</label>
                                <select class="form-select" id="period" name="period">
                                    <option value="current_month" <?php echo e($period == 'current_month' ? 'selected' : ''); ?>>
                                        الشهر الحالي</option>
                                    <option value="last_month" <?php echo e($period == 'last_month' ? 'selected' : ''); ?>>الشهر
                                        الماضي</option>
                                    <option value="last_3_months" <?php echo e($period == 'last_3_months' ? 'selected' : ''); ?>>آخر
                                        3 أشهر</option>
                                    <option value="last_6_months" <?php echo e($period == 'last_6_months' ? 'selected' : ''); ?>>آخر
                                        6 أشهر</option>
                                    <option value="current_year" <?php echo e($period == 'current_year' ? 'selected' : ''); ?>>السنة
                                        الحالية</option>
                                    <option value="last_year" <?php echo e($period == 'last_year' ? 'selected' : ''); ?>>السنة
                                        الماضية</option>
                                    <option value="custom" <?php echo e($period == 'custom' ? 'selected' : ''); ?>>فترة مخصصة
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3" id="customDateFrom"
                                style="<?php echo e($period != 'custom' ? 'display: none;' : ''); ?>">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from"
                                    value="<?php echo e($dateFrom); ?>">
                            </div>
                            <div class="col-md-3" id="customDateTo"
                                style="<?php echo e($period != 'custom' ? 'display: none;' : ''); ?>">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to"
                                    value="<?php echo e($dateTo); ?>">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-filter"></i> تطبيق الفلتر
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                                    <i class="fas fa-undo"></i> إعادة تعيين
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overall Statistics -->
        <div class="row mb-4" id="statisticsCards">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي الفروع</h6>
                                <h3 class="mb-0"><?php echo e($branches->count()); ?></h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-building fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المبيعات</h6>
                                <h3 class="mb-0" id="totalSalesCard"><?php echo e(number_format($totalSales, 2)); ?> ج.م</h3>
                                <?php if(isset($previousPeriodComparison['sales_change'])): ?>
                                    <small class="d-block">
                                        <?php if($previousPeriodComparison['sales_change'] >= 0): ?>
                                            <i class="fas fa-arrow-up"></i>
                                            +<?php echo e(number_format($previousPeriodComparison['sales_change'], 1)); ?>%
                                        <?php else: ?>
                                            <i class="fas fa-arrow-down"></i>
                                            <?php echo e(number_format($previousPeriodComparison['sales_change'], 1)); ?>%
                                        <?php endif; ?>
                                        مقارنة بالفترة السابقة
                                    </small>
                                <?php endif; ?>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المشتريات</h6>
                                <h3 class="mb-0" id="totalPurchasesCard"><?php echo e(number_format($totalPurchases, 2)); ?> ج.م
                                </h3>
                                <?php if(isset($previousPeriodComparison['purchases_change'])): ?>
                                    <small class="d-block">
                                        <?php if($previousPeriodComparison['purchases_change'] >= 0): ?>
                                            <i class="fas fa-arrow-up"></i>
                                            +<?php echo e(number_format($previousPeriodComparison['purchases_change'], 1)); ?>%
                                        <?php else: ?>
                                            <i class="fas fa-arrow-down"></i>
                                            <?php echo e(number_format($previousPeriodComparison['purchases_change'], 1)); ?>%
                                        <?php endif; ?>
                                        مقارنة بالفترة السابقة
                                    </small>
                                <?php endif; ?>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-shopping-cart fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">صافي الربح</h6>
                                <h3 class="mb-0" id="totalProfitCard"><?php echo e(number_format($totalProfit, 2)); ?> ج.م</h3>
                                <?php
                                    $profitMargin = $totalSales > 0 ? ($totalProfit / $totalSales) * 100 : 0;
                                ?>
                                <small class="d-block">
                                    هامش الربح: <?php echo e(number_format($profitMargin, 1)); ?>%
                                </small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-coins fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            
        </div>

        <!-- Charts Section -->
        <div class="row mb-4">
            <div class="col-lg-8 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line"></i> اتجاهات المبيعات والمشتريات الشهرية
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyTrendsChart" height="100"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie"></i> توزيع الأرباح حسب الفروع
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="branchProfitChart" height="150"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Performance Comparison -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar"></i> مقارنة أداء الفروع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الفرع</th>
                                        <th>المبيعات</th>
                                        <th>المشتريات</th>
                                        <th>صافي الربح</th>
                                        <th>عدد المبيعات</th>
                                        <th>متوسط البيع</th>
                                        <th>نمو المبيعات</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $branchPerformance; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branchData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $branch = $branches->find($branchData['id']);
                                        ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-3">
                                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                                            style="width: 40px; height: 40px;">
                                                            <i class="fas fa-building text-white"></i>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0"><?php echo e($branchData['name']); ?></h6>
                                                        <small
                                                            class="text-muted"><?php echo e($branch->code ?? 'فرع رقم ' . $branchData['id']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span
                                                    class="fw-bold text-success"><?php echo e(number_format($branchData['sales'], 2)); ?>

                                                    ج.م</span>
                                            </td>
                                            <td>
                                                <span
                                                    class="fw-bold text-primary"><?php echo e(number_format($branchData['purchases'], 2)); ?>

                                                    ج.م</span>
                                            </td>
                                            <td>
                                                <span
                                                    class="fw-bold text-<?php echo e($branchData['profit'] >= 0 ? 'success' : 'danger'); ?>">
                                                    <?php echo e(number_format($branchData['profit'], 2)); ?> ج.م
                                                </span>
                                            </td>
                                            <td><?php echo e($branchData['sales_count']); ?></td>
                                            <td><?php echo e(number_format($branchData['avg_sale_amount'], 2)); ?> ج.م</td>
                                            <td>
                                                <?php
                                                    $growthRate = $branch ? $branch->sales_growth : 0;
                                                ?>
                                                <span class="badge bg-<?php echo e($growthRate >= 0 ? 'success' : 'danger'); ?>">
                                                    <?php echo e(number_format($growthRate, 1)); ?>%
                                                </span>
                                            </td>
                                            <td>
                                                <span
                                                    class="badge bg-<?php echo e($branch && $branch->is_active ? 'success' : 'danger'); ?>">
                                                    <?php echo e($branch && $branch->is_active ? 'نشط' : 'غير نشط'); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php if($branch): ?>
                                                    <a href="<?php echo e(route('admin.branches.show', $branch)); ?>"
                                                        class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-info"
                                                        onclick="showBranchDetails(<?php echo e($branch->id); ?>)">
                                                        <i class="fas fa-chart-line"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing Branches -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy"></i> أفضل الفروع (المبيعات)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php $__currentLoopData = $topBranchesBySales->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <span class="badge bg-primary rounded-pill"><?php echo e($index + 1); ?></span>
                                    </div>
                                    <div>
                                        <h6 class="mb-0"><?php echo e($branch['name']); ?></h6>
                                        <small class="text-muted">فرع رقم <?php echo e($branch['id']); ?></small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold text-success"><?php echo e(number_format($branch['sales'], 2)); ?> ج.م
                                    </div>
                                    <small class="text-muted"><?php echo e($branch['sales_count']); ?> عملية بيع</small>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie"></i> أفضل الفروع (الربحية)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php $__currentLoopData = $topBranchesByProfit->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <span class="badge bg-success rounded-pill"><?php echo e($index + 1); ?></span>
                                    </div>
                                    <div>
                                        <h6 class="mb-0"><?php echo e($branch['name']); ?></h6>
                                        <small class="text-muted">فرع رقم <?php echo e($branch['id']); ?></small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold text-<?php echo e($branch['profit'] >= 0 ? 'success' : 'danger'); ?>">
                                        <?php echo e(number_format($branch['profit'], 2)); ?> ج.م
                                    </div>
                                    <small
                                        class="text-muted"><?php echo e($branch['sales'] > 0 ? number_format(($branch['profit'] / $branch['sales']) * 100, 1) : 0); ?>%
                                        هامش ربح</small>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Trends -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt"></i> اتجاهات الأداء الشهري
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-4">
                                <div class="text-center">
                                    <h6 class="text-muted">مبيعات هذا الشهر</h6>
                                    <h3 class="text-success"><?php echo e(number_format($monthSales, 2)); ?> ج.م</h3>
                                    <small class="text-muted">مقارنة بالشهر الماضي</small>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="text-center">
                                    <h6 class="text-muted">مشتريات هذا الشهر</h6>
                                    <h3 class="text-primary"><?php echo e(number_format($monthPurchases, 2)); ?> ج.م</h3>
                                    <small class="text-muted">مقارنة بالشهر الماضي</small>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="text-center">
                                    <h6 class="text-muted">صافي ربح الشهر</h6>
                                    <h3 class="text-<?php echo e($monthSales - $monthPurchases >= 0 ? 'success' : 'danger'); ?>">
                                        <?php echo e(number_format($monthSales - $monthPurchases, 2)); ?> ج.م
                                    </h3>
                                    <small class="text-muted">مقارنة بالشهر الماضي</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Branch Details Modal -->
    <div class="modal fade" id="branchDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الفرع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="branchDetailsContent">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // Global variables for charts
        let monthlyTrendsChart = null;
        let branchProfitChart = null;

        // Initialize charts on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Period selector change
            document.getElementById('period').addEventListener('change', function() {
                const customFields = ['customDateFrom', 'customDateTo'];
                if (this.value === 'custom') {
                    customFields.forEach(id => document.getElementById(id).style.display = 'block');
                } else {
                    customFields.forEach(id => document.getElementById(id).style.display = 'none');
                }
            });

            // Form submission
            document.getElementById('analyticsFilterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                updateAnalytics();
            });
        }

        function initializeCharts() {
            // Monthly trends chart
            const monthlyCtx = document.getElementById('monthlyTrendsChart').getContext('2d');
            monthlyTrendsChart = new Chart(monthlyCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($monthlyTrends->pluck('month_name'), 15, 512) ?>,
                    datasets: [{
                        label: 'المبيعات',
                        data: <?php echo json_encode($monthlyTrends->pluck('total_sales'), 15, 512) ?>,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }, {
                        label: 'المشتريات',
                        data: <?php echo json_encode($monthlyTrends->pluck('total_purchases'), 15, 512) ?>,
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'اتجاهات المبيعات والمشتريات'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ج.م';
                                }
                            }
                        }
                    }
                }
            });

            // Branch profit chart
            const branchCtx = document.getElementById('branchProfitChart').getContext('2d');
            const branchData = <?php echo json_encode($branchPerformance->take(5), 15, 512) ?>;
            branchProfitChart = new Chart(branchCtx, {
                type: 'doughnut',
                data: {
                    labels: branchData.map(branch => branch.name),
                    datasets: [{
                        data: branchData.map(branch => branch.profit),
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed.toLocaleString() + ' ج.م';
                                }
                            }
                        }
                    }
                }
            });
        }

        function updateAnalytics() {
            const formData = new FormData(document.getElementById('analyticsFilterForm'));
            const params = new URLSearchParams(formData);

            // Show loading state
            showLoadingState();

            // Update statistics
            fetch(`<?php echo e(route('admin.branches.analytics.data')); ?>?${params}`)
                .then(response => response.json())
                .then(data => {
                    updateStatisticsCards(data);
                })
                .catch(error => {
                    console.error('Error updating statistics:', error);
                    alert('حدث خطأ في تحديث الإحصائيات');
                });

            // Update monthly trends chart
            fetch(`<?php echo e(route('admin.branches.analytics.monthly-trends')); ?>?${params}`)
                .then(response => response.json())
                .then(data => {
                    updateMonthlyTrendsChart(data.chart_data);
                })
                .catch(error => {
                    console.error('Error updating monthly trends:', error);
                });

            // Update branch comparison chart
            fetch(`<?php echo e(route('admin.branches.analytics.branch-comparison')); ?>?${params}`)
                .then(response => response.json())
                .then(data => {
                    updateBranchProfitChart(data.chart_data);
                })
                .catch(error => {
                    console.error('Error updating branch comparison:', error);
                })
                .finally(() => {
                    hideLoadingState();
                });
        }

        function updateStatisticsCards(data) {
            document.getElementById('totalSalesCard').textContent = data.total_sales.toLocaleString() + ' ج.م';
            document.getElementById('totalPurchasesCard').textContent = data.total_purchases.toLocaleString() + ' ج.م';
            document.getElementById('totalProfitCard').textContent = data.total_profit.toLocaleString() + ' ج.م';
            if (document.getElementById('totalExpensesCard')) {
                document.getElementById('totalExpensesCard').textContent = data.total_expenses.toLocaleString() + ' ج.م';
            }
        }

        function updateMonthlyTrendsChart(chartData) {
            monthlyTrendsChart.data.labels = chartData.labels;
            monthlyTrendsChart.data.datasets[0].data = chartData.sales;
            monthlyTrendsChart.data.datasets[1].data = chartData.purchases;
            monthlyTrendsChart.update();
        }

        function updateBranchProfitChart(chartData) {
            branchProfitChart.data.labels = chartData.labels;
            branchProfitChart.data.datasets[0].data = chartData.profit;
            branchProfitChart.update();
        }

        function showLoadingState() {
            document.getElementById('statisticsCards').style.opacity = '0.5';
        }

        function hideLoadingState() {
            document.getElementById('statisticsCards').style.opacity = '1';
        }

        function resetFilters() {
            document.getElementById('period').value = 'current_month';
            document.getElementById('customDateFrom').style.display = 'none';
            document.getElementById('customDateTo').style.display = 'none';
            updateAnalytics();
        }

        function showBranchDetails(branchId) {
            fetch(`/admin/branches/${branchId}/analytics`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('branchDetailsContent').innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-primary text-white mb-3">
                                    <div class="card-body text-center">
                                        <h4>${data.total_sales.toLocaleString()} ج.م</h4>
                                        <p class="mb-0">إجمالي المبيعات</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-success text-white mb-3">
                                    <div class="card-body text-center">
                                        <h4>${data.total_purchases.toLocaleString()} ج.م</h4>
                                        <p class="mb-0">إجمالي المشتريات</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-info text-white mb-3">
                                    <div class="card-body text-center">
                                        <h4>${data.today_sales.toLocaleString()} ج.م</h4>
                                        <p class="mb-0">مبيعات اليوم</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-warning text-white mb-3">
                                    <div class="card-body text-center">
                                        <h4>${data.month_sales.toLocaleString()} ج.م</h4>
                                        <p class="mb-0">مبيعات الشهر</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    new bootstrap.Modal(document.getElementById('branchDetailsModal')).show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في تحميل البيانات');
                });
        }

        function exportAnalytics() {
            const formData = new FormData(document.getElementById('analyticsFilterForm'));
            const params = new URLSearchParams(formData);
            window.location.href = `/admin/branches/analytics/export?${params}`;
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\admin\branches\analytics.blade.php ENDPATH**/ ?>