<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">تفاصيل المعاملة المالية</h2>
                    <div class="btn-group">
                        <a href="<?php echo e(route('account-transactions.edit', $accountTransaction)); ?>" class="btn btn-outline-warning">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a href="<?php echo e(route('account-transactions.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Details -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exchange-alt text-primary"></i> بيانات المعاملة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">رقم المرجع:</label>
                                <p class="mb-0"><?php echo e($accountTransaction->reference_number); ?></p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">نوع المعاملة:</label>
                                <p class="mb-0">
                                    <?php if($accountTransaction->type === 'debit'): ?>
                                        <span class="badge bg-danger">مدين</span>
                                    <?php elseif($accountTransaction->type === 'credit'): ?>
                                        <span class="badge bg-success">دائن</span>
                                    <?php else: ?>
                                        <span class="badge bg-info">تحويل</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الحساب:</label>
                                <p class="mb-0"><?php echo e($accountTransaction->account->name ?? 'غير محدد'); ?></p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">المبلغ:</label>
                                <p class="mb-0 fw-bold text-primary"><?php echo e(number_format($accountTransaction->amount, 2)); ?> ريال</p>
                            </div>
                        </div>

                        <?php if($accountTransaction->type === 'transfer'): ?>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">من الحساب:</label>
                                    <p class="mb-0"><?php echo e($accountTransaction->fromAccount->name ?? 'غير محدد'); ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">إلى الحساب:</label>
                                    <p class="mb-0"><?php echo e($accountTransaction->toAccount->name ?? 'غير محدد'); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الرصيد قبل المعاملة:</label>
                                <p class="mb-0"><?php echo e(number_format($accountTransaction->balance_before, 2)); ?> ريال</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الرصيد بعد المعاملة:</label>
                                <p class="mb-0"><?php echo e(number_format($accountTransaction->balance_after, 2)); ?> ريال</p>
                            </div>
                        </div>

                        <?php if($accountTransaction->description): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">الوصف:</label>
                                <p class="mb-0"><?php echo e($accountTransaction->description); ?></p>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ المعاملة:</label>
                                <p class="mb-0"><?php echo e($accountTransaction->created_at->format('Y-m-d H:i:s')); ?></p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">المستخدم:</label>
                                <p class="mb-0"><?php echo e($accountTransaction->user->name ?? 'غير محدد'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transaction Summary -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line text-success"></i> ملخص المعاملة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>نوع المعاملة:</span>
                            <?php if($accountTransaction->type === 'debit'): ?>
                                <span class="badge bg-danger fs-6">مدين</span>
                            <?php elseif($accountTransaction->type === 'credit'): ?>
                                <span class="badge bg-success fs-6">دائن</span>
                            <?php else: ?>
                                <span class="badge bg-info fs-6">تحويل</span>
                            <?php endif; ?>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>المبلغ:</span>
                            <span class="fw-bold text-primary"><?php echo e(number_format($accountTransaction->amount, 2)); ?> ريال</span>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>التأثير على الرصيد:</span>
                            <?php
                                $impact = $accountTransaction->balance_after - $accountTransaction->balance_before;
                            ?>
                            <span class="fw-bold <?php echo e($impact >= 0 ? 'text-success' : 'text-danger'); ?>">
                                <?php echo e($impact >= 0 ? '+' : ''); ?><?php echo e(number_format($impact, 2)); ?> ريال
                            </span>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between align-items-center">
                            <span>الرصيد الحالي:</span>
                            <span class="fw-bold text-info"><?php echo e(number_format($accountTransaction->balance_after, 2)); ?> ريال</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt text-warning"></i> إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('account-transactions.edit', $accountTransaction)); ?>" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-edit"></i> تعديل المعاملة
                            </a>
                            <a href="<?php echo e(route('accounts.show', $accountTransaction->account)); ?>" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-eye"></i> عرض الحساب
                            </a>
                            <form action="<?php echo e(route('account-transactions.destroy', $accountTransaction)); ?>" method="POST" 
                                  onsubmit="return confirm('هل أنت متأكد من حذف هذه المعاملة؟')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                    <i class="fas fa-trash"></i> حذف المعاملة
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\account_transactions\show.blade.php ENDPATH**/ ?>