<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">تفاصيل الحساب: <?php echo e($account->name); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-bordered">
                                    <tr>
                                        <th style="width: 200px;">الاسم</th>
                                        <td><?php echo e($account->name); ?></td>
                                    </tr>
                                    <tr>
                                        <th>الرمز</th>
                                        <td><?php echo e($account->code); ?></td>
                                    </tr>
                                    <tr>
                                        <th>النوع</th>
                                        <td><?php echo e($account->type == 'branch' ? 'فرع' : ($account->type == 'supplier' ? 'مورد' : 'عميل')); ?></td>
                                    </tr>
                                    <tr>
                                        <th>الرصيد الحالي</th>
                                        <td><?php echo e(number_format($account->current_balance, 2)); ?></td>
                                    </tr>
                                    <tr>
                                        <th>الحالة</th>
                                        <td><?php echo e($account->is_active ? 'نشط' : 'غير نشط'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h5>المعاملات الأخيرة</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th>المرجع</th>
                                            <th>النوع</th>
                                            <th>المبلغ</th>
                                            <th>الرصيد قبل</th>
                                            <th>الرصيد بعد</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($transaction->reference_number); ?></td>
                                                <td>
                                                    <?php if($transaction->type == 'deposit'): ?>
                                                        إيداع
                                                    <?php elseif($transaction->type == 'withdrawal'): ?>
                                                        سحب
                                                    <?php else: ?>
                                                        تحويل
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo e(number_format($transaction->amount, 2)); ?></td>
                                                <td><?php echo e(number_format($transaction->balance_before, 2)); ?></td>
                                                <td><?php echo e(number_format($transaction->balance_after, 2)); ?></td>
                                                <td><?php echo e($transaction->created_at->format('Y-m-d H:i:s')); ?></td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-4">
                                <?php echo e($transactions->links()); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\accounts\show.blade.php ENDPATH**/ ?>