<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('تفاصيل البيع')); ?> #<?php echo e($sale->invoice_number); ?>

            </h2>
            <div>
                <a href="<?php echo e(user_route('sales.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> <?php echo e(__('العودة إلى المبيعات')); ?>

                </a>
                <?php if($sale->status === 'pending'): ?>
                    <a href="<?php echo e(user_route('sales.edit', $sale)); ?>" class="btn btn-primary">
                        <i class="fas fa-edit"></i> <?php echo e(__('تعديل')); ?>

                    </a>
                <?php endif; ?>
                <a href="<?php echo e(user_route('sales.print', $sale)); ?>" class="btn btn-info" target="_blank">
                    <i class="fas fa-print"></i> <?php echo e(__('طباعة')); ?>

                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Sale Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0"><?php echo e(__('معلومات البيع')); ?></h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th><?php echo e(__('رقم الفاتورة')); ?></th>
                                            <td><?php echo e($sale->invoice_number); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('العميل')); ?></th>
                                            <td><?php echo e($sale->customer ? $sale->customer->name : 'عميل نقدي'); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('الفرع')); ?></th>
                                            <td><?php echo e($sale->branch->name); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('التاريخ')); ?></th>
                                            <td><?php echo e($sale->created_at->format('Y-m-d H:i')); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('الحالة')); ?></th>
                                            <td>
                                                <?php if($sale->status === 'completed'): ?>
                                                    <span class="badge bg-success"><?php echo e(__('مكتمل')); ?></span>
                                                <?php elseif($sale->status === 'cancelled'): ?>
                                                    <span class="badge bg-danger"><?php echo e(__('ملغي')); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning"><?php echo e(__('قيد الانتظار')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0"><?php echo e(__('ملخص البيع')); ?></h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th><?php echo e(__('عدد المنتجات')); ?></th>
                                            <td><?php echo e($sale->items->count()); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('إجمالي الكمية')); ?></th>
                                            <td><?php echo e($sale->items->sum('quantity')); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('المجموع')); ?></th>
                                            <td><?php echo e(number_format($sale->total_amount, 2)); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('الخصم')); ?></th>
                                            <td><?php echo e(number_format($sale->discount_amount, 2)); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('المبلغ المدفوع')); ?></th>
                                            <td class="text-success">
                                                <?php echo e(number_format($sale->getTotalPaymentsAttribute(), 2)); ?> ج.م</td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('المبلغ المتبقي')); ?></th>
                                            <td
                                                class="<?php echo e($sale->getActualRemainingAmountAttribute() > 0 ? 'text-danger' : 'text-success'); ?>">
                                                <?php echo e(number_format($sale->getActualRemainingAmountAttribute(), 2)); ?> ج.م
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payments Section -->
                    <?php if($sale->customer): ?>
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-money-bill-wave me-2"></i><?php echo e(__('الدفعات')); ?>

                                </h5>
                                <div class="d-flex gap-2">
                                    <?php if($sale->canReceivePayment()): ?>
                                        <a href="<?php echo e(user_route('customer-payments.create', ['sale_id' => $sale->id])); ?>"
                                            class="btn btn-success btn-sm">
                                            <i class="fas fa-plus me-2"></i>إضافة دفعة
                                        </a>
                                    <?php endif; ?>
                                    <?php if($sale->canBeReturned()): ?>
                                        <a href="<?php echo e(user_route('sale-returns.create', ['sale_id' => $sale->id])); ?>"
                                            class="btn btn-warning btn-sm">
                                            <i class="fas fa-undo me-2"></i>إنشاء مرتجع
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="card-body">
                                <?php if($sale->payments->count() > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>المبلغ</th>
                                                    <th>طريقة الدفع</th>
                                                    <th>رقم المرجع</th>
                                                    <th>تاريخ الدفع</th>
                                                    <th>المستخدم</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $sale->payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td>
                                                            <span class="fw-bold text-success">
                                                                <?php echo e(number_format($payment->amount, 2)); ?> ج.م
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-primary">
                                                                <?php echo e($payment->payment_method_label); ?>

                                                            </span>
                                                        </td>
                                                        <td><?php echo e($payment->reference_number ?? '-'); ?></td>
                                                        <td>
                                                            <div><?php echo e($payment->payment_date->format('d/m/Y')); ?></div>
                                                            <small
                                                                class="text-muted"><?php echo e($payment->payment_date->format('h:i A')); ?></small>
                                                        </td>
                                                        <td>
                                                            <small
                                                                class="text-muted"><?php echo e($payment->user->name); ?></small>
                                                        </td>
                                                        <td>
                                                            <a href="<?php echo e(user_route('customer-payments.show', $payment)); ?>"
                                                                class="btn btn-sm btn-outline-info"
                                                                title="عرض التفاصيل">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                            <tfoot class="table-light">
                                                <tr>
                                                    <th class="text-success">
                                                        <?php echo e(number_format($sale->payments->sum('amount'), 2)); ?> ج.م
                                                    </th>
                                                    <th colspan="5" class="text-start">إجمالي الدفعات</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">لا توجد دفعات</h6>
                                        <?php if($sale->canReceivePayment()): ?>
                                            <a href="<?php echo e(user_route('customer-payments.create', ['sale_id' => $sale->id])); ?>"
                                                class="btn btn-success">
                                                <i class="fas fa-plus me-2"></i>إضافة دفعة
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Products Table -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo e(__('المنتجات')); ?></h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th><?php echo e(__('المنتج')); ?></th>
                                            <th><?php echo e(__('الكمية')); ?></th>
                                            <th><?php echo e(__('السعر')); ?></th>
                                            <th><?php echo e(__('المجموع')); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $sale->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($item->product->name); ?></td>
                                                <td><?php echo e($item->quantity); ?></td>
                                                <td><?php echo e(number_format($item->price, 2)); ?></td>
                                                <td><?php echo e(number_format($item->subtotal, 2)); ?></td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="3" class="text-start"><?php echo e(__('المجموع الكلي')); ?></th>
                                            <th><?php echo e(number_format($sale->total_amount, 2)); ?></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Returns Section -->
                    <?php if($sale->hasReturns()): ?>
                        <div class="card mt-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-undo me-2"></i><?php echo e(__('المرتجعات')); ?>

                                </h5>
                                <a href="<?php echo e(user_route('sale-returns.index', ['sale_id' => $sale->id])); ?>"
                                    class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-list me-2"></i>عرض جميع المرتجعات
                                </a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>رقم المرتجع</th>
                                                <th>المبلغ</th>
                                                <th>نوع المرتجع</th>
                                                <th>الحالة</th>
                                                <th>التاريخ</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $sale->returns->where('status', '!=', 'cancelled'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $return): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td>
                                                        <a href="<?php echo e(user_route('sale-returns.show', $return)); ?>"
                                                            class="text-decoration-none fw-bold text-primary">
                                                            <?php echo e($return->return_number); ?>

                                                        </a>
                                                    </td>
                                                    <td>
                                                        <span class="fw-bold text-warning">
                                                            <?php echo e(number_format($return->total_amount, 2)); ?> ج.م
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">
                                                            <?php echo e($return->return_type_label); ?>

                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo e($return->status_color); ?>">
                                                            <?php echo e($return->status_label); ?>

                                                        </span>
                                                    </td>
                                                    <td><?php echo e($return->return_date->format('d/m/Y')); ?></td>
                                                    <td>
                                                        <a href="<?php echo e(user_route('sale-returns.show', $return)); ?>"
                                                            class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                        <tfoot class="table-light">
                                            <tr>
                                                <th>الإجمالي:</th>
                                                <th class="text-warning">
                                                    <?php echo e(number_format($sale->total_returns, 2)); ?> ج.م
                                                </th>
                                                <th colspan="4"></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Actions -->
                    <?php if($sale->status === 'pending'): ?>
                        <div class="row mt-4">
                            <div class="col-12 text-start">
                                <form action="<?php echo e(route('sales.complete', $sale)); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-success"
                                        onclick="return confirm('<?php echo e(__('هل أنت متأكد من إكمال هذه العملية؟')); ?>')">
                                        <i class="fas fa-check"></i> <?php echo e(__('إكمال البيع')); ?>

                                    </button>
                                </form>
                                <form action="<?php echo e(route('sales.cancel', $sale)); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-danger"
                                        onclick="return confirm('<?php echo e(__('هل أنت متأكد من إلغاء هذه العملية؟')); ?>')">
                                        <i class="fas fa-times"></i> <?php echo e(__('إلغاء البيع')); ?>

                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Print Button -->
                    <div class="row mt-4">
                        <div class="col-12 text-start">
                            
                            <a href="<?php echo e(user_route('sales.print', $sale)); ?>" class="btn btn-primary"
                                target="_blank">
                                <i class="fas fa-print"></i> <?php echo e(__('طباعة الفاتورة')); ?>

                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
        <style>
            .rtl {
                direction: rtl;
                text-align: right;
            }

            .rtl .table th,
            .rtl .table td {
                text-align: right;
            }

            .rtl .btn-group {
                flex-direction: row-reverse;
            }

            .rtl .btn-group .btn {
                margin-right: 0;
                margin-left: 0.25rem;
            }

            .rtl .text-start {
                text-align: right !important;
            }

            .rtl .text-end {
                text-align: left !important;
            }

            @media print {
                .no-print {
                    display: none !important;
                }

                .card {
                    border: none !important;
                }

                .card-header {
                    background-color: #fff !important;
                    border-bottom: 2px solid #000 !important;
                }

                .table {
                    border-collapse: collapse !important;
                }

                .table th,
                .table td {
                    border: 1px solid #000 !important;
                }
            }
        </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
        <script>
            function printSale() {
                window.print();
            }
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\sales\show.blade.php ENDPATH**/ ?>