<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Illuminate\Database\Eloquent\Relations\Relation;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register Blade directive for user-aware routes
        Blade::directive('userRoute', function ($expression) {
            return "<?php echo user_route($expression); ?>";
        });

        // Register polymorphic type mappings for inventory transfers
        Relation::morphMap([
            'store' => \App\Models\Store::class,
            'branch' => \App\Models\Branch::class,
        ]);
    }
}
