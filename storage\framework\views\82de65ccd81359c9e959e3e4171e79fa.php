<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-plus text-success me-2"></i>
                    إضافة دفعة جديدة
                </h1>
                <p class="mb-0 text-muted">إضافة دفعة جديدة لعملية بيع</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(user_route('customer-payments.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى الدفعات
                </a>
            </div>
        </div>

        <form method="POST" action="<?php echo e(user_route('customer-payments.store')); ?>" id="paymentForm">
            <?php echo csrf_field(); ?>

            <!-- Sale Selection Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-receipt me-2"></i>اختيار عملية البيع
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="sale_id" class="form-label fw-bold">
                                <i class="fas fa-shopping-cart text-info me-2"></i>عملية البيع
                            </label>
                            <select class="form-select <?php $__errorArgs = ['sale_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="sale_id"
                                name="sale_id" required>
                                <option value="">اختر عملية البيع</option>
                                <?php $__currentLoopData = $unpaidSales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $unpaidSale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($unpaidSale->id); ?>"
                                        data-customer="<?php echo e($unpaidSale->customer?->name ?? 'عميل نقدي'); ?>"
                                        data-total="<?php echo e($unpaidSale->total_amount); ?>"
                                        data-discount="<?php echo e($unpaidSale->discount_amount); ?>"
                                        data-paid="<?php echo e($unpaidSale->paid_amount); ?>"
                                        data-remaining="<?php echo e($unpaidSale->getActualRemainingAmountAttribute()); ?>"
                                        <?php echo e(old('sale_id', $sale?->id) == $unpaidSale->id ? 'selected' : ''); ?>>
                                        <?php echo e($unpaidSale->invoice_number); ?> -
                                        <?php echo e($unpaidSale->customer?->name ?? 'عميل نقدي'); ?>

                                        (متبقي:
                                        <?php echo e(number_format($unpaidSale->getActualRemainingAmountAttribute(), 2)); ?> ج.م)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['sale_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div id="saleDetails" class="p-3 bg-light rounded" style="display: none;">
                                <h6 class="fw-bold text-primary mb-2">تفاصيل العملية</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">العميل:</small>
                                        <div class="fw-bold" id="customerName">-</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">المبلغ الإجمالي:</small>
                                        <div class="fw-bold text-primary" id="totalAmount">-</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">المدفوع:</small>
                                        <div class="fw-bold text-success" id="paidAmount">-</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">المتبقي:</small>
                                        <div class="fw-bold text-danger" id="remainingAmount">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Details Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-money-bill me-2"></i>تفاصيل الدفعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="amount" class="form-label fw-bold">
                                <i class="fas fa-coins text-success me-2"></i>المبلغ
                            </label>
                            <div class="input-group">
                                <input type="number" step="0.01" min="0.01"
                                    class="form-control <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="amount"
                                    name="amount" value="<?php echo e(old('amount')); ?>" required>
                                <span class="input-group-text">ج.م</span>
                            </div>
                            <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="payment_method" class="form-label fw-bold">
                                <i class="fas fa-credit-card text-info me-2"></i>طريقة الدفع
                            </label>
                            <select class="form-select <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="payment_method" name="payment_method" required>
                                <option value="">اختر طريقة الدفع</option>
                                <option value="cash" <?php echo e(old('payment_method', 'cash') == 'cash' ? 'selected' : ''); ?>>
                                    نقدي</option>
                                <option value="card" <?php echo e(old('payment_method') == 'card' ? 'selected' : ''); ?>>بطاقة
                                </option>
                                <option value="bank_transfer"
                                    <?php echo e(old('payment_method') == 'bank_transfer' ? 'selected' : ''); ?>>تحويل بنكي
                                </option>
                                <option value="check" <?php echo e(old('payment_method') == 'check' ? 'selected' : ''); ?>>شيك
                                </option>
                                <option value="other" <?php echo e(old('payment_method') == 'other' ? 'selected' : ''); ?>>أخرى
                                </option>
                            </select>
                            <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="payment_date" class="form-label fw-bold">
                                <i class="fas fa-calendar text-warning me-2"></i>تاريخ الدفع
                            </label>
                            <input type="date" class="form-control <?php $__errorArgs = ['payment_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="payment_date" name="payment_date" value="<?php echo e(old('payment_date', date('Y-m-d'))); ?>"
                                required>
                            <?php $__errorArgs = ['payment_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="reference_number" class="form-label fw-bold">
                                <i class="fas fa-hashtag text-secondary me-2"></i>رقم المرجع
                            </label>
                            <input type="text" class="form-control <?php $__errorArgs = ['reference_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="reference_number" name="reference_number" value="<?php echo e(old('reference_number')); ?>"
                                placeholder="رقم الشيك، رقم التحويل، إلخ...">
                            <?php $__errorArgs = ['reference_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="notes" class="form-label fw-bold">
                                <i class="fas fa-sticky-note text-warning me-2"></i>ملاحظات
                            </label>
                            <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="notes" name="notes" rows="3"
                                placeholder="أي ملاحظات إضافية..."><?php echo e(old('notes')); ?></textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card shadow mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                تأكد من صحة البيانات قبل الحفظ
                            </small>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?php echo e(user_route('customer-payments.index')); ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-save me-2"></i>حفظ الدفعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const saleSelect = document.getElementById('sale_id');
                const saleDetails = document.getElementById('saleDetails');
                const amountInput = document.getElementById('amount');

                // Show sale details when sale is selected
                saleSelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];

                    if (this.value) {
                        const customerName = selectedOption.dataset.customer;
                        const totalAmount = parseFloat(selectedOption.dataset.total);
                        const discountAmount = parseFloat(selectedOption.dataset.discount);
                        const paidAmount = parseFloat(selectedOption.dataset.paid);
                        const remainingAmount = parseFloat(selectedOption.dataset.remaining);

                        document.getElementById('customerName').textContent = customerName;
                        document.getElementById('totalAmount').textContent = totalAmount.toFixed(2) + ' ج.م';
                        document.getElementById('paidAmount').textContent = paidAmount.toFixed(2) + ' ج.م';
                        document.getElementById('remainingAmount').textContent = remainingAmount.toFixed(2) +
                            ' ج.م';

                        // Set max amount to remaining amount
                        amountInput.max = remainingAmount.toFixed(2);
                        amountInput.placeholder = `الحد الأقصى: ${remainingAmount.toFixed(2)} ج.م`;

                        saleDetails.style.display = 'block';
                    } else {
                        saleDetails.style.display = 'none';
                        amountInput.removeAttribute('max');
                        amountInput.placeholder = '';
                    }
                });

                // Validate amount doesn't exceed remaining
                amountInput.addEventListener('input', function() {
                    const maxAmount = parseFloat(this.max);
                    const currentAmount = parseFloat(this.value);

                    if (maxAmount && currentAmount > maxAmount) {
                        this.setCustomValidity(`المبلغ لا يمكن أن يتجاوز ${maxAmount.toFixed(2)} ج.م`);
                    } else {
                        this.setCustomValidity('');
                    }
                });

                // Trigger change event if sale is pre-selected
                if (saleSelect.value) {
                    saleSelect.dispatchEvent(new Event('change'));
                }
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views/customer-payments/create.blade.php ENDPATH**/ ?>