<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-undo text-warning me-2"></i>
                    مرتجعات المبيعات
                </h1>
                <p class="mb-0 text-muted">إدارة ومتابعة مرتجعات المبيعات</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(user_route('sale-returns.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إنشاء مرتجع جديد
                </a>
                <a href="<?php echo e(user_route('sales.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المبيعات
                </a>
            </div>
        </div>

        <!-- Filters Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter me-2"></i>البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(user_route('sale-returns.index')); ?>">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="customer_id" class="form-label">العميل</label>
                            <select class="form-select" id="customer_id" name="customer_id">
                                <option value="">جميع العملاء</option>
                                <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($customer->id); ?>"
                                        <?php echo e(request('customer_id') == $customer->id ? 'selected' : ''); ?>>
                                        <?php echo e($customer->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>قيد
                                    الانتظار</option>
                                <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>موافق
                                    عليه</option>
                                <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>مكتمل
                                </option>
                                <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>ملغي
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="return_type" class="form-label">نوع المرتجع</label>
                            <select class="form-select" id="return_type" name="return_type">
                                <option value="">جميع الأنواع</option>
                                <option value="full" <?php echo e(request('return_type') == 'full' ? 'selected' : ''); ?>>مرتجع
                                    كامل</option>
                                <option value="partial" <?php echo e(request('return_type') == 'partial' ? 'selected' : ''); ?>>
                                    مرتجع جزئي</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                value="<?php echo e(request('date_from')); ?>">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                value="<?php echo e(request('date_to')); ?>">
                        </div>
                        <div class="col-md-1 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="<?php echo e(user_route('sale-returns.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Returns Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>قائمة المرتجعات
                </h6>
            </div>
            <div class="card-body">
                <?php if($returns->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم المرتجع</th>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>نوع المرتجع</th>
                                    
                                    <th>تاريخ المرتجع</th>
                                    <th>المستخدم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $returns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $return): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <a href="<?php echo e(user_route('sale-returns.show', $return)); ?>"
                                                class="text-decoration-none fw-bold text-primary">
                                                <?php echo e($return->return_number); ?>

                                            </a>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(user_route('sales.show', $return->sale)); ?>"
                                                class="text-decoration-none text-info">
                                                <?php echo e($return->sale->invoice_number); ?>

                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-info text-white me-2">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">
                                                        <?php echo e($return->customer?->name ?? 'عميل نقدي'); ?>

                                                    </div>
                                                    <?php if($return->customer?->phone): ?>
                                                        <small
                                                            class="text-muted"><?php echo e($return->customer->phone); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-warning">
                                                <?php echo e(number_format($return->total_amount, 2)); ?> ج.م
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo e($return->return_type_label); ?>

                                            </span>
                                        </td>
                                        
                                        <td>
                                            <div><?php echo e($return->return_date->format('d/m/Y')); ?></div>
                                            <small
                                                class="text-muted"><?php echo e($return->created_at->format('h:i A')); ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo e($return->user->name); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(user_route('sale-returns.show', $return)); ?>"
                                                    class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if($return->canBeEdited()): ?>
                                                    <a href="<?php echo e(user_route('sale-returns.edit', $return)); ?>"
                                                        class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <?php if(auth()->user()->hasRole('admin') && $return->canBeCancelled()): ?>
                                                    <form method="POST"
                                                        action="<?php echo e(user_route('sale-returns.destroy', $return)); ?>"
                                                        class="d-inline"
                                                        onsubmit="return confirm('هل أنت متأكد من إلغاء هذا المرتجع؟')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                            title="إلغاء">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="3" class="text-end">إجمالي المرتجعات:</th>
                                    <th class="text-warning">
                                        <?php echo e(number_format($returns->sum('total_amount'), 2)); ?> ج.م
                                    </th>
                                    <th colspan="5"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        <?php echo e($returns->withQueryString()->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-undo fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مرتجعات</h5>
                        <p class="text-muted">لم يتم العثور على أي مرتجعات تطابق معايير البحث</p>
                        <a href="<?php echo e(user_route('sale-returns.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إنشاء مرتجع جديد
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
        <style>
            .avatar-circle {
                width: 35px;
                height: 35px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 12px;
            }
        </style>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\sale-returns\index.blade.php ENDPATH**/ ?>