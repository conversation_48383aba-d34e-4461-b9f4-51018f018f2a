<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-warehouse text-primary"></i> <?php echo e(__('نظرة عامة على المخزون')); ?>

                </h2>
                <p class="text-muted small mb-0">عرض شامل لجميع المنتجات عبر الفروع والمخازن</p>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="container-fluid">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي المنتجات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['total_products']); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-boxes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    إجمالي القيمة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['total_value'], 2)); ?> ج.م</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    مخزون منخفض
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['low_stock_count']); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    نفد المخزون
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['out_of_stock_count']); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter"></i> البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('admin.inventory.overview')); ?>">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo e(request('search')); ?>" placeholder="اسم المنتج أو الكود">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="category_id" class="form-label">التصنيف</label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="">جميع التصنيفات</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category->id); ?>" 
                                            <?php echo e(request('category_id') == $category->id ? 'selected' : ''); ?>>
                                        <?php echo e($category->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="stock_status" class="form-label">حالة المخزون</label>
                            <select class="form-select" id="stock_status" name="stock_status">
                                <option value="">جميع الحالات</option>
                                <option value="available" <?php echo e(request('stock_status') == 'available' ? 'selected' : ''); ?>>متوفر</option>
                                <option value="low_stock" <?php echo e(request('stock_status') == 'low_stock' ? 'selected' : ''); ?>>منخفض</option>
                                <option value="out_of_stock" <?php echo e(request('stock_status') == 'out_of_stock' ? 'selected' : ''); ?>>نفد المخزون</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Products Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table"></i> تفاصيل المخزون
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>التصنيف</th>
                                <th>الكود</th>
                                <th>السعر</th>
                                <th>الفروع</th>
                                <th>المخازن</th>
                                <th>الإجمالي</th>
                                <th>القيمة</th>
                                <th>الحالة</th>
                                <th>المواقع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($product->name); ?></td>
                                    <td><?php echo e($product->category); ?></td>
                                    <td><?php echo e($product->sku ?? '-'); ?></td>
                                    <td><?php echo e(number_format($product->price, 2)); ?> ج.م</td>
                                    <td><?php echo e(number_format($product->branch_quantity, 2)); ?></td>
                                    <td><?php echo e(number_format($product->store_quantity, 2)); ?></td>
                                    <td class="fw-bold"><?php echo e(number_format($product->total_quantity, 2)); ?></td>
                                    <td><?php echo e(number_format($product->total_value, 2)); ?> ج.م</td>
                                    <td>
                                        <?php if($product->stock_status == 'available'): ?>
                                            <span class="badge bg-success">متوفر</span>
                                        <?php elseif($product->stock_status == 'low_stock'): ?>
                                            <span class="badge bg-warning">منخفض</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">نفد المخزون</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-info" type="button" 
                                                data-bs-toggle="collapse" data-bs-target="#locations<?php echo e($product->id); ?>">
                                            <i class="fas fa-eye"></i> عرض
                                        </button>
                                    </td>
                                </tr>
                                <tr class="collapse" id="locations<?php echo e($product->id); ?>">
                                    <td colspan="10">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>الفروع:</h6>
                                                <?php $__empty_2 = true; $__currentLoopData = $product->locations['branches']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_2 = false; ?>
                                                    <small class="d-block"><?php echo e($branch['name']); ?>: <?php echo e($branch['quantity']); ?> (<?php echo e(number_format($branch['cost_price'], 2)); ?> ج.م)</small>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_2): ?>
                                                    <small class="text-muted">لا توجد كمية في الفروع</small>
                                                <?php endif; ?>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>المخازن:</h6>
                                                <?php $__empty_2 = true; $__currentLoopData = $product->locations['stores']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_2 = false; ?>
                                                    <small class="d-block"><?php echo e($store['name']); ?>: <?php echo e($store['quantity']); ?></small>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_2): ?>
                                                    <small class="text-muted">لا توجد كمية في المخازن</small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد منتجات</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\admin\inventory\overview.blade.php ENDPATH**/ ?>