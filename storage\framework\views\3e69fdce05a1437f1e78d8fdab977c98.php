<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-shopping-cart text-primary me-2"></i>
                    إنشاء عملية بيع جديدة
                </h1>
                <p class="mb-0 text-muted">إنشاء فاتورة بيع جديدة للعملاء</p>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-warning" id="togglePricesBtn" onclick="toggleSalePrices()"
                    title="إخفاء الأسعار">
                    <i class="fas fa-eye-slash"></i>
                </button>
                <a href="<?php echo e(user_route('sales.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المبيعات
                </a>
            </div>
        </div>

        <form method="POST" action="<?php echo e(user_route('sales.store')); ?>" id="saleForm">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="print_after_save" id="printAfterSave" value="0">

            <!-- Basic Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="customer_id" class="form-label fw-bold">
                                <i class="fas fa-user text-info me-2"></i>العميل
                            </label>
                            <div class="input-group">
                                <select class="form-select <?php $__errorArgs = ['customer_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="customer_id"
                                    name="customer_id">
                                    <option value="">عميل نقدي</option>
                                    <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($customer->id); ?>"
                                            data-account-id="<?php echo e($customer->account->id ?? ''); ?>"
                                            data-phone="<?php echo e($customer->phone ?? ''); ?>"
                                            data-balance="<?php echo e($customer->getRemainingBalance()); ?>">
                                            <?php echo e($customer->name); ?>

                                            <?php if($customer->getRemainingBalance() != 0): ?>
                                                (<?php echo e(number_format($customer->getRemainingBalance(), 2)); ?> ج.م)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal"
                                    data-bs-target="#addCustomerModal">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <?php $__errorArgs = ['customer_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="branch_id" class="form-label fw-bold">
                                <i class="fas fa-store text-info me-2"></i>الفرع
                            </label>
                            <select class="form-select <?php $__errorArgs = ['branch_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="branch_id"
                                name="branch_id" required>
                                <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($branch->id); ?>"
                                        <?php echo e(old('branch_id', auth()->user()->branch_id) == $branch->id ? 'selected' : ''); ?>>
                                        <?php echo e($branch->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['branch_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="notes" class="form-label fw-bold">
                                <i class="fas fa-sticky-note text-warning me-2"></i>ملاحظات
                            </label>
                            <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="notes" name="notes" rows="3"
                                placeholder="أدخل أي ملاحظات إضافية..."><?php echo e(old('notes')); ?></textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-boxes me-2"></i>المنتجات
                    </h6>
                    <button type="button" class="btn btn-primary btn-sm" id="addProduct">
                        <i class="fas fa-plus me-2"></i>إضافة منتج
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="productsTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="30%">المنتج</th>
                                    <th width="15%">الكمية</th>
                                    <th width="15%">السعر</th>
                                    <th width="15%">الخصم</th>
                                    <th width="15%">الإجمالي</th>
                                    <th width="10%">إجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- Products will be added here -->
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="4" class="text-end">المجموع الكلي:</th>
                                    <th id="totalAmount">0.00 ج.م</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <div id="emptyProductsMessage" class="text-center py-5 text-muted">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <h5>لا توجد منتجات</h5>
                        <p>انقر على "إضافة منتج" لبدء إضافة المنتجات</p>
                    </div>
                </div>
            </div>

            <!-- Financial Summary -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calculator me-2"></i>الملخص المالي
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="total_amount" class="form-label fw-bold">
                                <i class="fas fa-coins text-info me-2"></i>المبلغ الإجمالي
                            </label>
                            <div class="input-group">
                                <input type="number" step="0.01" class="form-control bg-light" id="total_amount"
                                    name="total_amount" readonly>
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="discount_amount" class="form-label fw-bold">
                                <i class="fas fa-percentage text-danger me-2"></i>الخصم
                            </label>
                            <div class="row">
                                <div class="col-4">
                                    <select class="form-select" id="discount_type" name="discount_type">
                                        <option value="amount"
                                            <?php echo e(old('discount_type', 'amount') == 'amount' ? 'selected' : ''); ?>>مبلغ
                                        </option>
                                        <option value="percentage"
                                            <?php echo e(old('discount_type') == 'percentage' ? 'selected' : ''); ?>>نسبة</option>
                                    </select>
                                </div>
                                <div class="col-8">
                                    <div class="input-group">
                                        <input type="number" step="0.01"
                                            class="form-control <?php $__errorArgs = ['discount_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            id="discount_amount" name="discount_amount"
                                            value="<?php echo e(old('discount_amount', 0)); ?>" min="0">
                                        <span class="input-group-text" id="discount_unit">ج.م</span>
                                    </div>
                                </div>
                            </div>
                            <?php $__errorArgs = ['discount_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="paid_amount" class="form-label fw-bold">
                                <i class="fas fa-money-bill text-success me-2"></i>المبلغ المدفوع
                            </label>
                            <div class="input-group">
                                <input type="number" step="0.01"
                                    class="form-control <?php $__errorArgs = ['paid_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="paid_amount"
                                    name="paid_amount" value="<?php echo e(old('paid_amount', 0)); ?>">
                                <span class="input-group-text">ج.م</span>
                            </div>
                            <?php $__errorArgs = ['paid_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="remaining_money" class="form-label fw-bold">
                                <i class="fas fa-clock text-warning me-2"></i>المبلغ المتبقي
                            </label>
                            <div class="input-group">
                                <input type="number" step="0.01" class="form-control bg-light"
                                    id="remaining_money" name="remaining_money" readonly>
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card shadow mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                تأكد من إدخال جميع البيانات بشكل صحيح
                            </small>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?php echo e(user_route('sales.index')); ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-success btn-lg" id="saveAndPrintBtn">
                                <i class="fas fa-print me-2"></i>حفظ وطباعة
                            </button>
                            <button type="submit" class="btn btn-primary btn-lg" id="saveBtn">
                                <i class="fas fa-save me-2"></i>حفظ العملية
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                let productIndex = 0;
                const productsTableBody = document.getElementById('productsTableBody');
                const productsTable = document.getElementById('productsTable');
                const emptyMessage = document.getElementById('emptyProductsMessage');
                const products = <?php echo json_encode($products, 15, 512) ?>;

                // Toggle empty message and table visibility
                function toggleEmptyMessage() {
                    const hasProducts = productsTableBody.children.length > 0;
                    emptyMessage.style.display = hasProducts ? 'none' : 'block';
                    if (hasProducts) {
                        productsTable.classList.add('show');
                    } else {
                        productsTable.classList.remove('show');
                    }
                }

                // Add Product Row
                document.getElementById('addProduct').addEventListener('click', function() {
                    const rowHtml = `
                        <tr class="product-row" data-index="${productIndex}">
                            <td>
                                <div class="product-search-container position-relative">
                                    <input type="text" class="form-control product-search" placeholder="ابحث عن المنتج..." autocomplete="off">
                                    <input type="hidden" name="products[${productIndex}][product_id]" class="product-id-input" required>
                                    <div class="product-dropdown position-absolute w-100 bg-white border rounded shadow-sm" style="display: none; max-height: 200px; overflow-y: auto; z-index: 1000;">
                                        ${products.map(product => `
                                                                                                                                    <div class="product-option p-2 border-bottom cursor-pointer"
                                                                                                                                         data-id="${product.id}"
                                                                                                                                         data-sale-price="${product.sale_price_1 || 0}"
                                                                                                                                         data-sale-price-2="${product.sale_price_2 || 0}"
                                                                                                                                         data-sale-price-3="${product.sale_price_3 || 0}"
                                                                                                                                         data-name="${product.name}"
                                                                                                                                         data-sku="${product.sku || ''}">
                                                                                                                                        <div class="fw-bold">${product.name}</div>
                                                                                                                                        ${product.sku ? `<small class="text-muted">كود: ${product.sku}</small>` : ''}
                                                                                                                                        <div class="sale-prices-info" style="display: ${window.pricesVisible !== false ? 'block' : 'none'}">
                                                                                                                                            <small class="text-success d-block">سعر 1: ${product.sale_price_1 || 0} ج.م</small>
                                                                                                                                            ${product.sale_price_2 ? `<small class="text-info d-block">سعر 2: ${product.sale_price_2} ج.م</small>` : ''}
                                                                                                                                            ${product.sale_price_3 ? `<small class="text-warning d-block">سعر 3: ${product.sale_price_3} ج.م</small>` : ''}
                                                                                                                                        </div>
                                                                                                                                        <small class="text-muted d-block">متوفر: ${product.available_quantity || 0} قطعة</small>
                                                                                                                                    </div>
                                                                                                                                `).join('')}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <input type="number" name="products[${productIndex}][quantity]" class="form-control quantity-input" min="1" step="1" required>
                            </td>
                            <td>
                                <input type="number" name="products[${productIndex}][sale_price]" class="form-control sale-price-input" min="0" step="0.01" required>
                            </td>
                            <td>
                                <div class="row">
                                    <div class="col-4">
                                        <select class="form-select discount-type-select">
                                            <option value="amount">مبلغ</option>
                                            <option value="percentage">نسبة</option>
                                        </select>
                                    </div>
                                    <div class="col-8">
                                        <input type="number" name="products[${productIndex}][discount]" class="form-control discount-input" min="0" step="0.01" value="0">
                                    </div>
                                </div>
                            </td>
                            <td>
                                <input type="number" class="form-control total-price-input bg-light" readonly>
                            </td>
                            <td>
                                <button type="button" class="btn btn-danger btn-sm remove-product">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;

                    productsTableBody.insertAdjacentHTML('beforeend', rowHtml);
                    const newRow = productsTableBody.lastElementChild;
                    initializeRowEvents(newRow);
                    productIndex++;
                    toggleEmptyMessage();
                });

                // Initialize Events for a Row
                function initializeRowEvents(row) {
                    const productSearch = row.querySelector('.product-search');
                    const productIdInput = row.querySelector('.product-id-input');
                    const productDropdown = row.querySelector('.product-dropdown');
                    const quantityInput = row.querySelector('.quantity-input');
                    const salePriceInput = row.querySelector('.sale-price-input');
                    const discountInput = row.querySelector('.discount-input');
                    const discountTypeSelect = row.querySelector('.discount-type-select');
                    const totalPriceInput = row.querySelector('.total-price-input');
                    const removeButton = row.querySelector('.remove-product');

                    // Handle product search
                    productSearch.addEventListener('input', function() {
                        const searchTerm = this.value.toLowerCase();
                        const options = productDropdown.querySelectorAll('.product-option');
                        let hasVisibleOptions = false;

                        options.forEach(option => {
                            const name = option.dataset.name.toLowerCase();
                            const sku = option.dataset.sku.toLowerCase();

                            if (name.includes(searchTerm) || sku.includes(searchTerm)) {
                                option.style.display = 'block';
                                hasVisibleOptions = true;
                            } else {
                                option.style.display = 'none';
                            }
                        });

                        productDropdown.style.display = searchTerm && hasVisibleOptions ? 'block' : 'none';
                    });

                    // Handle product selection
                    productDropdown.addEventListener('click', function(e) {
                        const option = e.target.closest('.product-option');
                        if (option) {
                            productSearch.value = option.dataset.name;
                            productIdInput.value = option.dataset.id;
                            salePriceInput.value = parseFloat(option.dataset.salePrice) || 0;
                            productDropdown.style.display = 'none';
                            calculateTotal();
                        }
                    });

                    // Show dropdown on focus
                    productSearch.addEventListener('focus', function() {
                        if (this.value) {
                            productDropdown.style.display = 'block';
                        }
                    });

                    // Hide dropdown when clicking outside
                    document.addEventListener('click', function(e) {
                        if (!row.contains(e.target)) {
                            productDropdown.style.display = 'none';
                        }
                    });

                    // Calculate Total Price
                    function calculateTotal() {
                        const quantity = parseFloat(quantityInput.value) || 0;
                        const price = parseFloat(salePriceInput.value) || 0;
                        const discountValue = parseFloat(discountInput.value) || 0;
                        const discountType = discountTypeSelect.value;

                        let subtotal = quantity * price;
                        let discountAmount = 0;

                        if (discountType === 'percentage') {
                            discountAmount = (subtotal * discountValue) / 100;
                        } else {
                            discountAmount = discountValue;
                        }

                        const total = subtotal - discountAmount;
                        totalPriceInput.value = total.toFixed(2);
                        updateSaleTotals();
                    }

                    quantityInput.addEventListener('input', calculateTotal);
                    salePriceInput.addEventListener('input', calculateTotal);
                    discountInput.addEventListener('input', calculateTotal);
                    discountTypeSelect.addEventListener('change', calculateTotal);

                    // Remove Row
                    removeButton.addEventListener('click', function() {
                        row.remove();
                        updateSaleTotals();
                        toggleEmptyMessage();
                    });
                }

                // Update Sale Totals
                function updateSaleTotals() {
                    let total = 0;
                    document.querySelectorAll('.total-price-input').forEach(input => {
                        total += parseFloat(input.value) || 0;
                    });

                    // Update table footer total
                    document.getElementById('totalAmount').textContent = total.toFixed(2) + ' ج.م';

                    // Calculate discount
                    const discountType = document.getElementById('discount_type').value;
                    const discountValue = parseFloat(document.getElementById('discount_amount').value) || 0;
                    let discountAmount = 0;

                    if (discountType === 'percentage') {
                        discountAmount = (total * discountValue) / 100;
                    } else {
                        discountAmount = discountValue;
                    }

                    // Update financial summary
                    const paid = parseFloat(document.getElementById('paid_amount').value) || 0;
                    const remaining = total - discountAmount - paid;

                    document.getElementById('total_amount').value = total.toFixed(2);
                    document.getElementById('remaining_money').value = remaining.toFixed(2);
                }

                // Handle discount type change
                document.getElementById('discount_type').addEventListener('change', function() {
                    const discountUnit = document.getElementById('discount_unit');
                    const discountAmount = document.getElementById('discount_amount');

                    if (this.value === 'percentage') {
                        discountUnit.textContent = '%';
                        discountAmount.max = '100';
                        discountAmount.placeholder = 'نسبة الخصم';
                    } else {
                        discountUnit.textContent = 'ج.م';
                        discountAmount.removeAttribute('max');
                        discountAmount.placeholder = 'مبلغ الخصم';
                    }
                    updateSaleTotals();
                });

                // Update totals when discount or paid amount changes
                document.getElementById('discount_amount').addEventListener('input', updateSaleTotals);
                document.getElementById('paid_amount').addEventListener('input', updateSaleTotals);

                // Handle print button
                document.getElementById('saveAndPrintBtn').addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('printAfterSave').value = '1';
                    document.getElementById('saleForm').submit();
                });

                // Handle regular save button
                document.getElementById('saveBtn').addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('printAfterSave').value = '0';
                    document.getElementById('saleForm').submit();
                });

                // Initialize discount type
                document.getElementById('discount_type').dispatchEvent(new Event('change'));

                // Initialize empty message
                toggleEmptyMessage();
            });

            // Initialize global price visibility state
            window.pricesVisible = true;

            // Toggle sale prices visibility globally
            function toggleSalePrices() {
                const pricesInfo = document.querySelectorAll('.sale-prices-info');
                const toggleBtn = document.getElementById('togglePricesBtn');

                window.pricesVisible = !window.pricesVisible;

                // Update button state regardless of whether there are price elements
                if (window.pricesVisible) {
                    toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
                    toggleBtn.className = 'btn btn-warning';
                    toggleBtn.title = 'إخفاء الأسعار';
                } else {
                    toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
                    toggleBtn.className = 'btn btn-success';
                    toggleBtn.title = 'إظهار الأسعار';
                }

                // Update existing price info elements
                pricesInfo.forEach(info => {
                    info.style.display = window.pricesVisible ? 'block' : 'none';
                });
            }
        </script>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('styles'); ?>
        <style>
            #productsTable {
                display: none;
            }

            #productsTable.show {
                display: table !important;
            }

            .product-row td {
                vertical-align: middle;
                padding: 0.5rem;
            }

            .product-search-container {
                position: relative;
            }

            .product-dropdown {
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
                z-index: 1000;
                max-height: 200px;
                overflow-y: auto;
            }

            .product-option {
                padding: 0.5rem 0.75rem;
                border-bottom: 1px solid #f8f9fa;
                cursor: pointer;
                transition: background-color 0.15s ease-in-out;
            }

            .product-option:hover {
                background-color: #f8f9fa;
            }

            .product-option:last-child {
                border-bottom: none;
            }

            .cursor-pointer {
                cursor: pointer;
            }
        </style>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views/sales/create.blade.php ENDPATH**/ ?>