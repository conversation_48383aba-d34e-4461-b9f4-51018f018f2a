<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4 class="mb-1">استلام المنتجات - <?php echo e($branch->name); ?></h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.branches.index')); ?>">الفروع</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.branches.show', $branch)); ?>"><?php echo e($branch->name); ?></a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.branches.sales-inventory', $branch)); ?>">مخزون المبيعات</a></li>
                        <li class="breadcrumb-item active">استلام المنتجات</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?php echo e(route('admin.branches.sales-inventory', $branch)); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> عودة
                </a>
            </div>
        </div>

        <?php if($pendingTransfers->count() > 0): ?>
            <!-- Pending Transfers -->
            <div class="row">
                <?php $__currentLoopData = $pendingTransfers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transfer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">طلب نقل #<?php echo e($transfer->transfer_number); ?></h6>
                                    <small class="text-muted">من: <?php echo e($transfer->source->name); ?></small>
                                </div>
                                <div>
                                    <?php if($transfer->status === 'approved'): ?>
                                        <span class="badge bg-warning">معتمد</span>
                                    <?php elseif($transfer->status === 'in_transit'): ?>
                                        <span class="badge bg-info">في الطريق</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>تاريخ الطلب:</strong> <?php echo e($transfer->created_at->format('Y-m-d H:i')); ?><br>
                                    <strong>المطلوب بواسطة:</strong> <?php echo e($transfer->requestedBy->name ?? 'غير محدد'); ?><br>
                                    <?php if($transfer->notes): ?>
                                        <strong>ملاحظات:</strong> <?php echo e($transfer->notes); ?>

                                    <?php endif; ?>
                                </div>

                                <!-- Transfer Items -->
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكمية المطلوبة</th>
                                                <th>الكمية المرسلة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $transfer->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td>
                                                        <div>
                                                            <strong><?php echo e($item->product->name); ?></strong><br>
                                                            <small class="text-muted"><?php echo e($item->product->barcode); ?></small>
                                                        </div>
                                                    </td>
                                                    <td><?php echo e(number_format($item->requested_quantity)); ?></td>
                                                    <td>
                                                        <?php if($item->shipped_quantity): ?>
                                                            <span class="text-success fw-bold"><?php echo e(number_format($item->shipped_quantity)); ?></span>
                                                        <?php else: ?>
                                                            <span class="text-muted">لم يتم الشحن</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Action Buttons -->
                                <div class="d-flex gap-2 mt-3">
                                    <?php if($transfer->status === 'in_transit'): ?>
                                        <form method="POST" action="<?php echo e(route('admin.inventory-transfers.receive', $transfer)); ?>" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-success btn-sm" 
                                                    onclick="return confirm('هل أنت متأكد من استلام هذه المنتجات؟')">
                                                <i class="fas fa-check"></i> تأكيد الاستلام
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    
                                    <a href="<?php echo e(route('admin.inventory-transfers.show', $transfer)); ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <!-- No Pending Transfers -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-truck fa-4x text-muted mb-4"></i>
                    <h5 class="text-muted">لا توجد منتجات في انتظار الاستلام</h5>
                    <p class="text-muted mb-4">
                        لا توجد طلبات نقل معتمدة أو في الطريق لهذا الفرع حالياً.
                    </p>
                    <div class="d-flex gap-2 justify-content-center">
                        <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>?destination_type=branch&destination_id=<?php echo e($branch->id); ?>" 
                           class="btn btn-primary">
                            <i class="fas fa-plus"></i> طلب نقل منتجات
                        </a>
                        <a href="<?php echo e(route('admin.inventory-transfers.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-list"></i> عرض جميع طلبات النقل
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-plus-circle fa-2x text-primary mb-3"></i>
                        <h6>طلب منتجات جديدة</h6>
                        <p class="text-muted small">إنشاء طلب نقل منتجات من المخازن</p>
                        <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>?destination_type=branch&destination_id=<?php echo e($branch->id); ?>" 
                           class="btn btn-primary btn-sm">
                            إنشاء طلب
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-history fa-2x text-info mb-3"></i>
                        <h6>سجل الاستلام</h6>
                        <p class="text-muted small">عرض تاريخ المنتجات المستلمة</p>
                        <a href="<?php echo e(route('admin.inventory-transfers.index')); ?>?destination_type=branch&destination_id=<?php echo e($branch->id); ?>&status=completed" 
                           class="btn btn-info btn-sm">
                            عرض السجل
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x text-success mb-3"></i>
                        <h6>تقارير المخزون</h6>
                        <p class="text-muted small">تحليل حركة المنتجات والمبيعات</p>
                        <a href="<?php echo e(route('admin.branches.analytics', $branch)); ?>" class="btn btn-success btn-sm">
                            عرض التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> تعليمات الاستلام</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">خطوات الاستلام:</h6>
                        <ol class="small">
                            <li>تحقق من وصول المنتجات المطلوبة</li>
                            <li>قم بفحص الكميات والجودة</li>
                            <li>اضغط على "تأكيد الاستلام" عند التأكد</li>
                            <li>سيتم تحديث مخزون الفرع تلقائياً</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">ملاحظات مهمة:</h6>
                        <ul class="small">
                            <li>تأكد من مطابقة الكميات المستلمة مع المرسلة</li>
                            <li>في حالة وجود نقص، تواصل مع المخزن المرسل</li>
                            <li>لا يمكن التراجع عن تأكيد الاستلام</li>
                            <li>سيتم إشعار المخزن المرسل بتأكيد الاستلام</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\branches\receive-products.blade.php ENDPATH**/ ?>