<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Dashboard')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <?php if(auth()->user()->isManager()): ?>
                        <!-- Manager Dashboard -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <!-- Sales Overview -->
                            <div class="bg-blue-100 p-4 rounded-lg">
                                <h3 class="text-lg font-semibold text-blue-800">Today's Sales</h3>
                                <p class="text-2xl font-bold text-blue-600">$<?php echo e(number_format($todaySales, 2)); ?></p>
                            </div>

                            <!-- Products Overview -->
                            <div class="bg-green-100 p-4 rounded-lg">
                                <h3 class="text-lg font-semibold text-green-800">Total Products</h3>
                                <p class="text-2xl font-bold text-green-600"><?php echo e($totalProducts); ?></p>
                            </div>

                            <!-- Customers Overview -->
                            <div class="bg-purple-100 p-4 rounded-lg">
                                <h3 class="text-lg font-semibold text-purple-800">Total Customers</h3>
                                <p class="text-2xl font-bold text-purple-600"><?php echo e($totalCustomers); ?></p>
                            </div>

                            <!-- Branches Overview -->
                            <div class="bg-yellow-100 p-4 rounded-lg">
                                <h3 class="text-lg font-semibold text-yellow-800">Total Branches</h3>
                                <p class="text-2xl font-bold text-yellow-600"><?php echo e($totalBranches); ?></p>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="mt-8">
                            <h3 class="text-lg font-semibold mb-4">Recent Activity</h3>
                            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                                <ul class="divide-y divide-gray-200">
                                    <?php $__currentLoopData = $recentActivities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="px-4 py-4">
                                            <div class="flex items-center justify-between">
                                                <div class="text-sm text-gray-900"><?php echo e($activity->description); ?></div>
                                                <div class="text-sm text-gray-500">
                                                    <?php echo e($activity->created_at->diffForHumans()); ?></div>
                                            </div>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Employee Dashboard -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Today's Sales -->
                            <div class="bg-blue-100 p-4 rounded-lg">
                                <h3 class="text-lg font-semibold text-blue-800">Today's Sales</h3>
                                <p class="text-2xl font-bold text-blue-600">$<?php echo e(number_format($todaySales, 2)); ?></p>
                            </div>

                            <!-- Branch Information -->
                            <div class="bg-green-100 p-4 rounded-lg">
                                <h3 class="text-lg font-semibold text-green-800">Your Branch</h3>
                                <p class="text-xl font-bold text-green-600"><?php echo e($branchName); ?></p>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-8">
                            <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <a href="<?php echo e(user_route('sales.create')); ?>"
                                    class="bg-white shadow rounded-lg p-4 hover:bg-gray-50 transition duration-150">
                                    <div class="flex items-center">
                                        <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        <span class="ml-2 text-gray-900">New Sale</span>
                                    </div>
                                </a>
                                <a href="<?php echo e(user_route('sales.index')); ?>"
                                    class="bg-white shadow rounded-lg p-4 hover:bg-gray-50 transition duration-150">
                                    <div class="flex items-center">
                                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                        <span class="ml-2 text-gray-900">View Sales</span>
                                    </div>
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\dashboard.blade.php ENDPATH**/ ?>