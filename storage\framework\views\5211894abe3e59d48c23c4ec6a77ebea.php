<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('تقرير المبيعات')); ?>

            </h2>
            <div>
                <button type="button" class="btn btn-success" onclick="printReport()">
                    <i class="fas fa-print"></i> <?php echo e(__('طباعة')); ?>

                </button>
                <button type="button" class="btn btn-primary" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> <?php echo e(__('تصدير إلى Excel')); ?>

                </button>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Filters -->
                    <form action="<?php echo e(route('reports.sales')); ?>" method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="branch_id"><?php echo e(__('الفرع')); ?></label>
                                    <select name="branch_id" id="branch_id" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($branch->id); ?>" <?php echo e(request('branch_id') == $branch->id ? 'selected' : ''); ?>>
                                                <?php echo e($branch->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="customer_id"><?php echo e(__('العميل')); ?></label>
                                    <select name="customer_id" id="customer_id" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($customer->id); ?>" <?php echo e(request('customer_id') == $customer->id ? 'selected' : ''); ?>>
                                                <?php echo e($customer->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="user_id"><?php echo e(__('المستخدم')); ?></label>
                                    <select name="user_id" id="user_id" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($user->id); ?>" <?php echo e(request('user_id') == $user->id ? 'selected' : ''); ?>>
                                                <?php echo e($user->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="payment_method"><?php echo e(__('طريقة الدفع')); ?></label>
                                    <select name="payment_method" id="payment_method" class="form-select">
                                        <option value=""><?php echo e(__('الكل')); ?></option>
                                        <option value="cash" <?php echo e(request('payment_method') == 'cash' ? 'selected' : ''); ?>><?php echo e(__('نقدي')); ?></option>
                                        <option value="card" <?php echo e(request('payment_method') == 'card' ? 'selected' : ''); ?>><?php echo e(__('بطاقة')); ?></option>
                                        <option value="bank" <?php echo e(request('payment_method') == 'bank' ? 'selected' : ''); ?>><?php echo e(__('تحويل بنكي')); ?></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_from"><?php echo e(__('من تاريخ')); ?></label>
                                    <input type="date" name="date_from" id="date_from" class="form-control" value="<?php echo e(request('date_from')); ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_to"><?php echo e(__('إلى تاريخ')); ?></label>
                                    <input type="date" name="date_to" id="date_to" class="form-control" value="<?php echo e(request('date_to')); ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="group_by"><?php echo e(__('تجميع حسب')); ?></label>
                                    <select name="group_by" id="group_by" class="form-select">
                                        <option value="daily" <?php echo e(request('group_by') == 'daily' ? 'selected' : ''); ?>><?php echo e(__('يومي')); ?></option>
                                        <option value="weekly" <?php echo e(request('group_by') == 'weekly' ? 'selected' : ''); ?>><?php echo e(__('أسبوعي')); ?></option>
                                        <option value="monthly" <?php echo e(request('group_by') == 'monthly' ? 'selected' : ''); ?>><?php echo e(__('شهري')); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="sort_by"><?php echo e(__('ترتيب حسب')); ?></label>
                                    <select name="sort_by" id="sort_by" class="form-select">
                                        <option value="date_asc" <?php echo e(request('sort_by') == 'date_asc' ? 'selected' : ''); ?>><?php echo e(__('التاريخ (تصاعدي)')); ?></option>
                                        <option value="date_desc" <?php echo e(request('sort_by') == 'date_desc' ? 'selected' : ''); ?>><?php echo e(__('التاريخ (تنازلي)')); ?></option>
                                        <option value="amount_desc" <?php echo e(request('sort_by') == 'amount_desc' ? 'selected' : ''); ?>><?php echo e(__('المبلغ (تنازلي)')); ?></option>
                                        <option value="amount_asc" <?php echo e(request('sort_by') == 'amount_asc' ? 'selected' : ''); ?>><?php echo e(__('المبلغ (تصاعدي)')); ?></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12 text-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> <?php echo e(__('عرض التقرير')); ?>

                                </button>
                                <a href="<?php echo e(route('reports.sales')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> <?php echo e(__('إعادة تعيين')); ?>

                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('إجمالي المبيعات')); ?></h5>
                                    <h3 class="mb-0"><?php echo e(number_format($summary['total_sales'], 2)); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('صافي المبيعات')); ?></h5>
                                    <h3 class="mb-0"><?php echo e(number_format($summary['net_sales'], 2)); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('عدد الفواتير')); ?></h5>
                                    <h3 class="mb-0"><?php echo e($summary['total_invoices']); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('متوسط قيمة الفاتورة')); ?></h5>
                                    <h3 class="mb-0"><?php echo e(number_format($summary['average_invoice'], 2)); ?></h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('إجمالي الخصومات')); ?></h5>
                                    <h3 class="mb-0"><?php echo e(number_format($summary['total_discount'], 2)); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(__('عدد المنتجات المباعة')); ?></h5>
                                    <h3 class="mb-0"><?php echo e($summary['total_items_sold']); ?></h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Top Products -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0"><?php echo e(__('أعلى المنتجات مبيعاً')); ?></h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th><?php echo e(__('المنتج')); ?></th>
                                                    <th><?php echo e(__('الكمية')); ?></th>
                                                    <th><?php echo e(__('المبلغ')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $topProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($product['name']); ?></td>
                                                        <td><?php echo e($product['quantity']); ?></td>
                                                        <td><?php echo e(number_format($product['total'], 2)); ?></td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sales Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('الفترة')); ?></th>
                                    <th><?php echo e(__('عدد الفواتير')); ?></th>
                                    <th><?php echo e(__('إجمالي المبيعات')); ?></th>
                                    <th><?php echo e(__('الخصومات')); ?></th>
                                    <th><?php echo e(__('صافي المبيعات')); ?></th>
                                    <th><?php echo e(__('متوسط قيمة الفاتورة')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $sales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($sale->period); ?></td>
                                        <td><?php echo e($sale->invoices_count); ?></td>
                                        <td><?php echo e(number_format($sale->total_sales, 2)); ?></td>
                                        <td><?php echo e(number_format($sale->total_discount, 2)); ?></td>
                                        <td><?php echo e(number_format($sale->net_sales, 2)); ?></td>
                                        <td><?php echo e(number_format($sale->average_invoice, 2)); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="6" class="text-center"><?php echo e(__('لا توجد بيانات')); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        <?php echo e($sales->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .form-select {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .text-start {
            text-align: right !important;
        }
        .rtl .text-end {
            text-align: left !important;
        }
        .rtl .form-control {
            text-align: right;
        }
        .rtl .form-control::placeholder {
            text-align: right;
        }
        .rtl .card-title {
            text-align: right;
        }
        .card {
            margin-bottom: 1rem;
        }
        .card-body {
            padding: 1rem;
        }
        .card-title {
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
        .card h3 {
            font-size: 1.5rem;
            margin-bottom: 0;
        }
        .card small {
            font-size: 0.75rem;
            opacity: 0.8;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .container-fluid {
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            .card {
                break-inside: avoid;
            }
        }
    </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
    <script>
        function printReport() {
            window.print();
        }

        function exportToExcel() {
            // Get the current URL with all query parameters
            let url = new URL(window.location.href);
            url.searchParams.set('export', 'excel');

            // Redirect to the export URL
            window.location.href = url.toString();
        }
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\reports\sales.blade.php ENDPATH**/ ?>