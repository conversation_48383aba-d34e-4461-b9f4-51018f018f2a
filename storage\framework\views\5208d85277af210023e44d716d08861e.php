<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h3 mb-0">تفاصيل العميل - <?php echo e($customer->name); ?></h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="<?php echo e(route('seller.dashboard')); ?>">لوحة التحكم</a></li>
                                <li class="breadcrumb-item"><a href="<?php echo e(route('seller.customers.index')); ?>">العملاء</a></li>
                                <li class="breadcrumb-item active"><?php echo e($customer->name); ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-store"></i> <?php echo e(auth()->user()->branch->name ?? 'غير محدد'); ?>

                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Info and Actions -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2 text-center">
                                <div class="avatar-lg bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                                    <span class="fs-2 fw-bold"><?php echo e(strtoupper(substr($customer->name, 0, 1))); ?></span>
                                </div>
                            </div>
                            <div class="col-md-10">
                                <h4 class="mb-3"><?php echo e($customer->name); ?></h4>
                                <div class="row">
                                    <div class="col-md-6">
                                        <?php if($customer->phone): ?>
                                            <div class="mb-2">
                                                <i class="fas fa-phone text-muted me-2"></i>
                                                <span><?php echo e($customer->phone); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($customer->email): ?>
                                            <div class="mb-2">
                                                <i class="fas fa-envelope text-muted me-2"></i>
                                                <span><?php echo e($customer->email); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-6">
                                        <?php if($customer->address): ?>
                                            <div class="mb-2">
                                                <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                                <span><?php echo e($customer->address); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <div class="mb-2">
                                            <i class="fas fa-calendar text-muted me-2"></i>
                                            <span>عميل منذ <?php echo e($customer->created_at->format('Y-m-d')); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <h6 class="text-muted mb-3">الإجراءات السريعة</h6>
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('seller.sales.create', ['customer_id' => $customer->id])); ?>" 
                               class="btn btn-primary">
                                <i class="fas fa-plus"></i> بيع جديد
                            </a>
                            <?php if(($customerSummary['total_remaining'] ?? 0) > 0): ?>
                                <a href="<?php echo e(route('seller.customer-payments.create', ['customer_id' => $customer->id])); ?>" 
                                   class="btn btn-success">
                                    <i class="fas fa-money-bill"></i> تسجيل دفعة
                                </a>
                            <?php endif; ?>
                            <a href="<?php echo e(route('seller.customers.edit', $customer)); ?>" 
                               class="btn btn-outline-warning">
                                <i class="fas fa-edit"></i> تعديل البيانات
                            </a>
                            <a href="<?php echo e(route('seller.customers.index')); ?>" 
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للعملاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-primary bg-gradient rounded-3 p-3 mb-3 d-inline-block">
                            <i class="fas fa-shopping-cart text-white fa-lg"></i>
                        </div>
                        <h5 class="mb-1"><?php echo e($customerSummary['sales_count'] ?? 0); ?></h5>
                        <p class="text-muted mb-0">إجمالي الفواتير</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-success bg-gradient rounded-3 p-3 mb-3 d-inline-block">
                            <i class="fas fa-chart-line text-white fa-lg"></i>
                        </div>
                        <h5 class="mb-1"><?php echo e(number_format($customerSummary['total_sales'] ?? 0, 2)); ?> ج.م</h5>
                        <p class="text-muted mb-0">إجمالي المشتريات</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-info bg-gradient rounded-3 p-3 mb-3 d-inline-block">
                            <i class="fas fa-money-bill-wave text-white fa-lg"></i>
                        </div>
                        <h5 class="mb-1"><?php echo e(number_format($customerSummary['total_paid'] ?? 0, 2)); ?> ج.م</h5>
                        <p class="text-muted mb-0">المبلغ المدفوع</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-warning bg-gradient rounded-3 p-3 mb-3 d-inline-block">
                            <i class="fas fa-exclamation-triangle text-white fa-lg"></i>
                        </div>
                        <h5 class="mb-1"><?php echo e(number_format($customerSummary['total_remaining'] ?? 0, 2)); ?> ج.م</h5>
                        <p class="text-muted mb-0">المبلغ المستحق</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-search text-primary"></i> البحث في منتجات العميل
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="<?php echo e(route('seller.customers.show', $customer)); ?>">
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <input type="text" class="form-control" name="product_search" 
                                           value="<?php echo e($productSearch); ?>" 
                                           placeholder="البحث عن منتج اشتراه العميل...">
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                        <?php if($productSearch): ?>
                                            <a href="<?php echo e(route('seller.customers.show', $customer)); ?>" 
                                               class="btn btn-outline-secondary">
                                                <i class="fas fa-times"></i> إعادة تعيين
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <?php if($productSearch && $purchasedProducts->count() > 0): ?>
                            <div class="mt-4">
                                <h6 class="mb-3">نتائج البحث: <?php echo e($purchasedProducts->count()); ?> منتج</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>المنتج</th>
                                                <th>إجمالي الكمية</th>
                                                <th>إجمالي المبلغ</th>
                                                <th>متوسط السعر</th>
                                                <th>عدد مرات الشراء</th>
                                                <th>آخر شراء</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $purchasedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo e($item->product->name); ?></strong>
                                                        <?php if($item->product->sku): ?>
                                                            <br><small class="text-muted"><?php echo e($item->product->sku); ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo e(number_format($item->total_quantity)); ?></td>
                                                    <td><?php echo e(number_format($item->total_amount, 2)); ?> ج.م</td>
                                                    <td><?php echo e(number_format($item->average_price, 2)); ?> ج.م</td>
                                                    <td><?php echo e($item->purchase_count); ?></td>
                                                    <td><?php echo e($item->last_purchase_date->format('Y-m-d')); ?></td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php elseif($productSearch): ?>
                            <div class="mt-4">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> لم يتم العثور على منتجات تطابق البحث "<?php echo e($productSearch); ?>"
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales History -->
        <?php if($customer->sales->count() > 0): ?>
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-history text-primary"></i> سجل المشتريات من فرعك
                                </h5>
                                <span class="badge bg-primary fs-6"><?php echo e($customer->sales->count()); ?> فاتورة</span>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="border-0">رقم الفاتورة</th>
                                            <th class="border-0">التاريخ</th>
                                            <th class="border-0">عدد المنتجات</th>
                                            <th class="border-0">المجموع</th>
                                            <th class="border-0">الخصم</th>
                                            <th class="border-0">المدفوع</th>
                                            <th class="border-0">المتبقي</th>
                                            <th class="border-0 text-center">الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $customer->sales->sortByDesc('created_at'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <span class="fw-semibold"><?php echo e($sale->invoice_number); ?></span>
                                                </td>
                                                <td>
                                                    <span><?php echo e($sale->created_at->format('Y-m-d')); ?></span>
                                                    <br><small class="text-muted"><?php echo e($sale->created_at->format('H:i')); ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo e($sale->items->count()); ?> منتج</span>
                                                </td>
                                                <td>
                                                    <span class="fw-semibold"><?php echo e(number_format($sale->total_amount, 2)); ?> ج.م</span>
                                                </td>
                                                <td>
                                                    <?php if($sale->discount_amount > 0): ?>
                                                        <span class="text-success"><?php echo e(number_format($sale->discount_amount, 2)); ?> ج.م</span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="text-success fw-semibold"><?php echo e(number_format($sale->paid_amount, 2)); ?> ج.م</span>
                                                </td>
                                                <td>
                                                    <?php
                                                        $remaining = ($sale->total_amount - $sale->discount_amount) - $sale->paid_amount;
                                                    ?>
                                                    <?php if($remaining > 0): ?>
                                                        <span class="badge bg-warning"><?php echo e(number_format($remaining, 2)); ?> ج.م</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">مسدد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-center">
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="<?php echo e(route('seller.sales.show', $sale)); ?>" 
                                                           class="btn btn-outline-primary" title="عرض الفاتورة">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="<?php echo e(route('seller.sales.print', $sale)); ?>" 
                                                           class="btn btn-outline-secondary" title="طباعة" target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                        <?php if($remaining > 0): ?>
                                                            <a href="<?php echo e(route('seller.customer-payments.create', ['customer_id' => $customer->id, 'sale_id' => $sale->id])); ?>" 
                                                               class="btn btn-outline-success" title="تسجيل دفعة">
                                                                <i class="fas fa-money-bill"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مشتريات</h5>
                            <p class="text-muted">لم يقم هذا العميل بأي مشتريات من فرعك بعد</p>
                            <a href="<?php echo e(route('seller.sales.create', ['customer_id' => $customer->id])); ?>" 
                               class="btn btn-primary">
                                <i class="fas fa-plus"></i> إنشاء فاتورة جديدة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views/seller/customers/show.blade.php ENDPATH**/ ?>