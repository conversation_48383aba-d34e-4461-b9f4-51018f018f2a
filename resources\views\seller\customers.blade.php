<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">إدارة العملاء</h2>
                    <div class="text-muted">
                        <i class="fas fa-store"></i> {{ auth()->user()->branch->name ?? 'غير محدد' }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Summary Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي العملاء</h6>
                                <h3 class="mb-0">{{ $customerSummary['total_customers'] ?? 0 }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المبيعات</h6>
                                <h3 class="mb-0">{{ format_currency($customerSummary['total_sales'] ?? 0) }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">المبلغ المدفوع</h6>
                                <h3 class="mb-0">{{ format_currency($customerSummary['total_paid'] ?? 0) }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-money-bill-wave fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">المبلغ المستحق</h6>
                                <h3 class="mb-0">{{ number_format($customerSummary['total_owed'] ?? 0, 2) }} ر.س</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" action="{{ route('seller.customers.index') }}">
                            <div class="row align-items-end">
                                <div class="col-md-4">
                                    <label for="search" class="form-label">البحث</label>
                                    <div class="input-group">
                                        <input type="text" name="search" id="search" class="form-control"
                                            placeholder="اسم العميل أو رقم الهاتف..." value="{{ request('search') }}">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="balance_status" class="form-label">حالة الرصيد</label>
                                    <select name="balance_status" id="balance_status" class="form-select">
                                        <option value="">جميع العملاء</option>
                                        <option value="owing"
                                            {{ request('balance_status') == 'owing' ? 'selected' : '' }}>مدينون</option>
                                        <option value="paid"
                                            {{ request('balance_status') == 'paid' ? 'selected' : '' }}>مسددون</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="sort_by" class="form-label">ترتيب حسب</label>
                                    <select name="sort_by" id="sort_by" class="form-select">
                                        <option value="name" {{ request('sort_by') == 'name' ? 'selected' : '' }}>
                                            الاسم</option>
                                        <option value="total_sales"
                                            {{ request('sort_by') == 'total_sales' ? 'selected' : '' }}>إجمالي المبيعات
                                        </option>
                                        <option value="amount_owed"
                                            {{ request('sort_by') == 'amount_owed' ? 'selected' : '' }}>المبلغ المستحق
                                        </option>
                                        <option value="created_at"
                                            {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>تاريخ الإضافة
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-filter"></i> تصفية
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> قائمة العملاء
                        </h5>
                        <div class="btn-group">
                            <a href="{{ route('seller.customers.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> إضافة عميل جديد
                            </a>
                            <button type="button" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم العميل</th>
                                        <th>رقم الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>إجمالي المشتريات</th>
                                        <th>المبلغ المدفوع</th>
                                        <th>المبلغ المستحق</th>
                                        <th>آخر عملية شراء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($customers as $customer)
                                        @php
                                            $totalSales = $customer['total_sales'] ?? 0;
                                            $totalPaid = $customer['total_paid'] ?? 0;
                                            $amountOwed = $customer['amount_owed'] ?? 0;
                                            $lastSale = $customer['last_sale_date'] ?? null;
                                        @endphp
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div
                                                        class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        {{ strtoupper(substr($customer['name'], 0, 1)) }}
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">{{ $customer['name'] }}</h6>
                                                        @if ($customer['address'])
                                                            <small
                                                                class="text-muted">{{ Str::limit($customer['address'], 30) }}</small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if ($customer['phone'])
                                                    <a href="tel:{{ $customer['phone'] }}"
                                                        class="text-decoration-none">
                                                        <i class="fas fa-phone text-success"></i>
                                                        {{ $customer['phone'] }}
                                                    </a>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if ($customer['email'])
                                                    <a href="mailto:{{ $customer['email'] }}"
                                                        class="text-decoration-none">
                                                        {{ $customer['email'] }}
                                                    </a>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td class="fw-bold">{{ format_currency($totalSales) }}</td>
                                            <td class="text-success">{{ format_currency($totalPaid) }}</td>
                                            <td>
                                                @if ($amountOwed > 0)
                                                    <span
                                                        class="badge bg-warning fs-6">{{ format_currency($amountOwed) }}</span>
                                                @else
                                                    <span class="badge bg-success fs-6">مسدد</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if ($lastSale)
                                                    <span
                                                        class="text-muted">{{ \Carbon\Carbon::parse($lastSale)->diffForHumans() }}</span>
                                                @else
                                                    <span class="text-muted">لا توجد مشتريات</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ user_route('customers.show', $customer['id']) }}"
                                                        class="btn btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ user_route('customers.edit', $customer['id']) }}"
                                                        class="btn btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @if ($amountOwed > 0)
                                                        <button type="button" class="btn btn-outline-success"
                                                            onclick="recordPayment({{ $customer['id'] }}, {{ $amountOwed }})"
                                                            title="تسجيل دفعة">
                                                            <i class="fas fa-money-bill"></i>
                                                        </button>
                                                    @endif
                                                    <a href="{{ user_route('sales.create', ['customer_id' => $customer['id']]) }}"
                                                        class="btn btn-outline-info" title="بيع جديد">
                                                        <i class="fas fa-plus"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="text-center text-muted py-5">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <h5>لا توجد عملاء</h5>
                                                <p>لا توجد عملاء تطابق معايير البحث المحددة</p>
                                                <a href="{{ user_route('customers.create') }}"
                                                    class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> إضافة عميل جديد
                                                </a>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>

                    @if (method_exists($customers, 'hasPages') && $customers->hasPages())
                        <div class="card-footer">
                            {{ $customers->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Outstanding Balances Alert -->
        @if (($customerSummary['total_owed'] ?? 0) > 0)
            <div class="row mt-4">
                <div class="col-12">
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">
                            <i class="fas fa-exclamation-triangle"></i> تنبيه مستحقات العملاء
                        </h5>
                        <p class="mb-0">
                            إجمالي المبلغ المستحق من العملاء:
                            <strong>{{ format_currency($customerSummary['total_owed'] ?? 0) }}</strong>
                        </p>
                        <hr>
                        <a href="{{ route('seller.customers', ['balance_status' => 'owing']) }}"
                            class="btn btn-warning btn-sm">
                            عرض العملاء المدينين
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Payment Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تسجيل دفعة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="paymentForm">
                    <div class="modal-body">
                        <input type="hidden" id="customerId" name="customer_id">

                        <div class="mb-3">
                            <label for="customerName" class="form-label">اسم العميل</label>
                            <input type="text" id="customerName" class="form-control" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="outstandingAmount" class="form-label">المبلغ المستحق</label>
                            <input type="text" id="outstandingAmount" class="form-control" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="paymentAmount" class="form-label">مبلغ الدفعة</label>
                            <input type="number" id="paymentAmount" name="amount" class="form-control"
                                step="0.01" min="0" required>
                        </div>

                        <div class="mb-3">
                            <label for="paymentMethod" class="form-label">طريقة الدفع</label>
                            <select id="paymentMethod" name="payment_method" class="form-select" required>
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة ائتمان</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="paymentNotes" class="form-label">ملاحظات (اختياري)</label>
                            <textarea id="paymentNotes" name="notes" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">تسجيل الدفعة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function recordPayment(customerId, outstandingAmount) {
            // Find customer name from the table
            const customerRow = document.querySelector(`tr:has(a[href*="customers/${customerId}"])`);
            const customerName = customerRow ? customerRow.querySelector('h6').textContent : 'غير محدد';

            // Fill modal
            document.getElementById('customerId').value = customerId;
            document.getElementById('customerName').value = customerName;
            document.getElementById('outstandingAmount').value = outstandingAmount.toFixed(2) + ' {{ currency_symbol() }}';
            document.getElementById('paymentAmount').value = outstandingAmount.toFixed(2);
            document.getElementById('paymentAmount').max = outstandingAmount;

            // Show modal
            new bootstrap.Modal(document.getElementById('paymentModal')).show();
        }

        // Handle payment form submission
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            data._token = '{{ csrf_token() }}';

            fetch('{{ route('account-transactions.store') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        toastr.success('تم تسجيل الدفعة بنجاح');
                        bootstrap.Modal.getInstance(document.getElementById('paymentModal')).hide();
                        location.reload();
                    } else {
                        toastr.error(data.message || 'حدث خطأ أثناء تسجيل الدفعة');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    toastr.error('حدث خطأ أثناء تسجيل الدفعة');
                });
        });

        // Auto-submit form on filter change
        document.getElementById('balance_status').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('sort_by').addEventListener('change', function() {
            this.form.submit();
        });
    </script>
</x-app-layout>
