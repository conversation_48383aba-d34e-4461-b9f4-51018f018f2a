<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-eye text-info me-2"></i>
                    تفاصيل الدفعة
                </h1>
                <p class="mb-0 text-muted">عرض تفاصيل دفعة العميل</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ user_route('customer-payments.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى الدفعات
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Payment Details -->
            <div class="col-md-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-info-circle me-2"></i>معلومات الدفعة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">رقم الفاتورة:</strong>
                            </div>
                            <div class="col-sm-8">
                                <a href="{{ user_route('sales.show', $payment->sale) }}" 
                                   class="text-decoration-none fw-bold text-primary">
                                    {{ $payment->sale->invoice_number }}
                                </a>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">العميل:</strong>
                            </div>
                            <div class="col-sm-8">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-info text-white me-2">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ $payment->sale->customer?->name ?? 'عميل نقدي' }}</div>
                                        @if($payment->sale->customer?->phone)
                                            <small class="text-muted">{{ $payment->sale->customer->phone }}</small>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">المبلغ:</strong>
                            </div>
                            <div class="col-sm-8">
                                <span class="badge bg-success fs-6 px-3 py-2">
                                    {{ number_format($payment->amount, 2) }} ج.م
                                </span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">طريقة الدفع:</strong>
                            </div>
                            <div class="col-sm-8">
                                <span class="badge bg-primary">
                                    {{ $payment->payment_method_label }}
                                </span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">رقم المرجع:</strong>
                            </div>
                            <div class="col-sm-8">
                                {{ $payment->reference_number ?? 'غير محدد' }}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">تاريخ الدفع:</strong>
                            </div>
                            <div class="col-sm-8">
                                <div class="text-dark">{{ $payment->payment_date->format('d/m/Y') }}</div>
                                <small class="text-muted">{{ $payment->payment_date->format('h:i A') }}</small>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong class="text-muted">المستخدم:</strong>
                            </div>
                            <div class="col-sm-8">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-secondary text-white me-2">
                                        {{ substr($payment->user->name, 0, 1) }}
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ $payment->user->name }}</div>
                                        <small class="text-muted">{{ $payment->created_at->format('d/m/Y h:i A') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if($payment->notes)
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-muted">الملاحظات:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <div class="p-2 bg-light rounded">
                                        {{ $payment->notes }}
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sale Summary -->
            <div class="col-md-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-receipt me-2"></i>ملخص العملية
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-6">
                                <small class="text-muted">المبلغ الإجمالي:</small>
                            </div>
                            <div class="col-6 text-end">
                                <span class="fw-bold">{{ number_format($payment->sale->total_amount, 2) }} ج.م</span>
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-6">
                                <small class="text-muted">الخصم:</small>
                            </div>
                            <div class="col-6 text-end">
                                <span class="text-warning">{{ number_format($payment->sale->discount_amount, 2) }} ج.م</span>
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-6">
                                <small class="text-muted">المدفوع:</small>
                            </div>
                            <div class="col-6 text-end">
                                <span class="text-success">{{ number_format($payment->sale->paid_amount, 2) }} ج.م</span>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong class="text-muted">المتبقي:</strong>
                            </div>
                            <div class="col-6 text-end">
                                <span class="fw-bold {{ $payment->sale->getActualRemainingAmountAttribute() > 0 ? 'text-danger' : 'text-success' }}">
                                    {{ number_format($payment->sale->getActualRemainingAmountAttribute(), 2) }} ج.م
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-cogs me-2"></i>الإجراءات
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ user_route('sales.show', $payment->sale) }}" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>عرض الفاتورة
                            </a>
                            @if($payment->sale->canReceivePayment())
                                <a href="{{ user_route('customer-payments.create', ['sale_id' => $payment->sale->id]) }}" 
                                   class="btn btn-outline-success">
                                    <i class="fas fa-plus me-2"></i>إضافة دفعة أخرى
                                </a>
                            @endif
                            @if(auth()->user()->hasRole('admin'))
                                <form method="POST" action="{{ user_route('customer-payments.destroy', $payment) }}"
                                      onsubmit="return confirm('هل أنت متأكد من حذف هذه الدفعة؟')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger">
                                        <i class="fas fa-trash me-2"></i>حذف الدفعة
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
        <style>
            .avatar-circle {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 14px;
            }
        </style>
    @endpush
</x-app-layout>
