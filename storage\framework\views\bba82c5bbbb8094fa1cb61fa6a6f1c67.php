<?php $__env->startSection('title', 'تاريخ المدفوعات'); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">تاريخ المدفوعات</h1>
                <p class="text-muted mb-0">سجل تفصيلي لجميع المدفوعات المسجلة</p>
            </div>
            <div>
                <a href="<?php echo e(user_route('payment-reports.index')); ?>" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
                <a href="<?php echo e(user_route('payment-reports.export', array_merge(['type' => 'payments'], request()->query()))); ?>"
                    class="btn btn-success">
                    <i class="fas fa-file-csv me-2"></i>تصدير
                </a>
            </div>
        </div>

        <!-- Filters and Summary -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" name="date_from" class="form-control" value="<?php echo e($dateFrom); ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" name="date_to" class="form-control" value="<?php echo e($dateTo); ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">المورد</label>
                                <select name="supplier_id" class="form-select">
                                    <option value="">جميع الموردين</option>
                                    <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($supplier->id); ?>"
                                            <?php echo e($supplierId == $supplier->id ? 'selected' : ''); ?>>
                                            <?php echo e($supplier->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary d-block w-100">
                                    <i class="fas fa-search me-2"></i>بحث
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-left-success shadow h-100">
                    <div class="card-body">
                        <div class="text-center">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                إجمالي المدفوعات
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">
                                <?php echo e(format_currency($totalAmount)); ?>

                            </div>
                            <div class="text-muted mt-2">
                                <?php echo e($payments->total()); ?> عملية دفع
                            </div>
                            <div class="text-muted small">
                                <?php echo e($dateFrom); ?> إلى <?php echo e($dateTo); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment History Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">سجل المدفوعات</h6>
            </div>
            <div class="card-body">
                <?php if($payments->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-bold">رقم المرجع</th>
                                    <th class="border-0 fw-bold">رقم العملية</th>
                                    <th class="border-0 fw-bold">المورد</th>
                                    <th class="border-0 fw-bold">المبلغ</th>
                                    <th class="border-0 fw-bold">طريقة الدفع</th>
                                    <th class="border-0 fw-bold">تاريخ الدفع</th>
                                    <th class="border-0 fw-bold">المستخدم</th>
                                    <th class="border-0 fw-bold text-center">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="border-bottom">
                                        <td>
                                            <span class="badge bg-secondary"><?php echo e($payment->reference_number); ?></span>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(user_route('purchases.show', $payment->transactionable)); ?>"
                                                class="fw-bold text-primary text-decoration-none">
                                                #<?php echo e($payment->transactionable->id); ?>

                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-primary text-white me-2">
                                                    <?php echo e(substr($payment->transactionable->supplier->name, 0, 1)); ?>

                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo e($payment->transactionable->supplier->name); ?>

                                                    </div>
                                                    <?php if($payment->transactionable->supplier->phone): ?>
                                                        <small
                                                            class="text-muted"><?php echo e($payment->transactionable->supplier->phone); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span
                                                class="fw-bold text-success fs-6"><?php echo e(format_currency($payment->amount)); ?></span>
                                        </td>
                                        <td>
                                            <?php
                                                $methodColors = [
                                                    'cash' => 'success',
                                                    'bank_transfer' => 'info',
                                                    'check' => 'warning',
                                                    'credit_card' => 'primary',
                                                    'other' => 'secondary',
                                                ];
                                                $methodLabels = [
                                                    'cash' => 'نقدي',
                                                    'bank_transfer' => 'تحويل بنكي',
                                                    'check' => 'شيك',
                                                    'credit_card' => 'بطاقة ائتمان',
                                                    'other' => 'أخرى',
                                                ];
                                            ?>
                                            <span
                                                class="badge bg-<?php echo e($methodColors[$payment->payment_method] ?? 'secondary'); ?>">
                                                <?php echo e($methodLabels[$payment->payment_method] ?? $payment->payment_method); ?>

                                            </span>
                                            <?php if($payment->payment_reference): ?>
                                                <div class="small text-muted mt-1"><?php echo e($payment->payment_reference); ?></div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="fw-bold"><?php echo e($payment->created_at->format('Y-m-d')); ?></div>
                                            <small class="text-muted"><?php echo e($payment->created_at->format('H:i')); ?></small>
                                            <div class="small text-muted"><?php echo e($payment->created_at->diffForHumans()); ?></div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-info text-white me-2">
                                                    <?php echo e(substr($payment->user->name ?? 'غير محدد', 0, 1)); ?>

                                                </div>
                                                <div>
                                                    <div class="fw-bold small"><?php echo e($payment->user->name ?? 'غير محدد'); ?>

                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(user_route('purchases.show', $payment->transactionable)); ?>"
                                                    class="btn btn-outline-info btn-sm" title="عرض العملية">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(user_route('supplier-payments.show', $payment->transactionable)); ?>"
                                                    class="btn btn-outline-secondary btn-sm" title="تاريخ المدفوعات">
                                                    <i class="fas fa-history"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="3" class="text-end fw-bold text-success">إجمالي المدفوعات:</th>
                                    <th class="fw-bold text-success"><?php echo e(format_currency($totalAmount)); ?></th>
                                    <th colspan="4"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($payments->appends(request()->query())->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مدفوعات</h5>
                        <p class="text-muted">لم يتم العثور على مدفوعات في الفترة المحددة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }

        .btn-group .btn {
            margin-left: 2px;
        }

        .btn-group .btn:first-child {
            margin-left: 0;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my-pos-app\resources\views\payment-reports\payment-history.blade.php ENDPATH**/ ?>