<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-edit text-primary me-2"></i>
                    تعديل المشتريات #<?php echo e($purchase->invoice_number); ?>

                </h1>
                <p class="mb-0 text-muted">تحديث تفاصيل عملية الشراء والمنتجات المرتبطة</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(user_route('purchases.show', $purchase)); ?>" class="btn btn-outline-info">
                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                </a>
                <a href="<?php echo e(user_route('purchases.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المشتريات
                </a>
            </div>
        </div>

        <form action="<?php echo e(user_route('purchases.update', $purchase)); ?>" method="POST" id="purchaseForm">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>

            <!-- Basic Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="supplier_id" class="form-label fw-bold">
                                <i class="fas fa-truck text-info me-2"></i>المورد
                            </label>
                            <select name="supplier_id" id="supplier_id"
                                class="form-select <?php $__errorArgs = ['supplier_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                <option value="">-- اختر المورد --</option>
                                <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($supplier->id); ?>"
                                        <?php echo e(old('supplier_id', $purchase->supplier_id) == $supplier->id ? 'selected' : ''); ?>>
                                        <?php echo e($supplier->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['supplier_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="branch_id" class="form-label fw-bold">
                                <i class="fas fa-store text-success me-2"></i>الفرع
                            </label>
                            <select name="branch_id" id="branch_id"
                                class="form-select <?php $__errorArgs = ['branch_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                <option value="">-- اختر الفرع --</option>
                                <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($branch->id); ?>"
                                        <?php echo e(old('branch_id', $purchase->branch_id) == $branch->id ? 'selected' : ''); ?>>
                                        <?php echo e($branch->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['branch_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="purchase_date" class="form-label fw-bold">
                                <i class="fas fa-calendar text-warning me-2"></i>تاريخ الشراء
                            </label>
                            <input type="date" name="purchase_date" id="purchase_date"
                                class="form-control <?php $__errorArgs = ['purchase_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                value="<?php echo e(old('purchase_date', $purchase->purchase_date->format('Y-m-d'))); ?>" required>
                            <?php $__errorArgs = ['purchase_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="status" class="form-label fw-bold">
                                <i class="fas fa-flag text-danger me-2"></i>الحالة
                            </label>
                            <select name="status" id="status"
                                class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                <option value="pending"
                                    <?php echo e(old('status', $purchase->status) == 'pending' ? 'selected' : ''); ?>>قيد الانتظار
                                </option>
                                <option value="completed"
                                    <?php echo e(old('status', $purchase->status) == 'completed' ? 'selected' : ''); ?>>مكتملة
                                </option>
                                <option value="cancelled"
                                    <?php echo e(old('status', $purchase->status) == 'cancelled' ? 'selected' : ''); ?>>ملغية
                                </option>
                            </select>
                            <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="notes" class="form-label fw-bold">
                                <i class="fas fa-sticky-note text-secondary me-2"></i>ملاحظات
                            </label>
                            <textarea name="notes" id="notes" class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" rows="3"
                                placeholder="أدخل أي ملاحظات إضافية..."><?php echo e(old('notes', $purchase->notes)); ?></textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-boxes me-2"></i>المنتجات
                    </h6>
                    <button type="button" class="btn btn-primary btn-sm" id="addRow">
                        <i class="fas fa-plus me-2"></i>إضافة منتج
                    </button>
                </div>
                <div class="card-body">
                    <div id="productsContainer">
                        <?php $__currentLoopData = $purchase->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="product-row card border-left-primary shadow-sm mb-3">
                                <div class="card-header py-2 bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="m-0 text-primary">
                                            <i class="fas fa-box me-2"></i>منتج #<?php echo e($index + 1); ?>

                                        </h6>
                                        <button type="button" class="btn btn-danger btn-sm remove-row">
                                            <i class="fas fa-trash me-1"></i>حذف
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label class="form-label fw-bold">
                                                <i class="fas fa-tag text-info me-2"></i>المنتج
                                            </label>
                                            <select name="products[<?php echo e($index); ?>][id]"
                                                class="form-select product-select" required>
                                                <option value="">-- اختر المنتج --</option>
                                                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($p->id); ?>"
                                                        data-price="<?php echo e($p->cost_price); ?>"
                                                        <?php echo e($product->id == $p->id ? 'selected' : ''); ?>>
                                                        <?php echo e($p->name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <div class="col-md-2 mb-3">
                                            <label class="form-label fw-bold">
                                                <i class="fas fa-cubes text-success me-2"></i>الكمية
                                            </label>
                                            <input type="number" name="products[<?php echo e($index); ?>][quantity]"
                                                class="form-control quantity" value="<?php echo e($product->pivot->quantity); ?>"
                                                min="1" required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label fw-bold">
                                                <i class="fas fa-dollar-sign text-warning me-2"></i>السعر
                                            </label>
                                            <div class="input-group">
                                                <input type="number" name="products[<?php echo e($index); ?>][price]"
                                                    class="form-control price" value="<?php echo e($product->pivot->price); ?>"
                                                    step="0.01" min="0" required>
                                                <span class="input-group-text"><?php echo e(currency_symbol()); ?></span>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label fw-bold">
                                                <i class="fas fa-calculator text-primary me-2"></i>المجموع
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control total bg-light"
                                                    value="<?php echo e($product->pivot->quantity * $product->pivot->price); ?>"
                                                    readonly>
                                                <span class="input-group-text"><?php echo e(currency_symbol()); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <?php if($purchase->products->count() == 0): ?>
                        <div id="emptyProductsMessage" class="text-center py-5 text-muted">
                            <i class="fas fa-box-open fa-3x mb-3"></i>
                            <h5>لا توجد منتجات</h5>
                            <p>انقر على "إضافة منتج" لبدء إضافة المنتجات</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Financial Summary -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calculator me-2"></i>الملخص المالي
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-left-primary h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                المجموع الفرعي
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="subtotal">
                                                <?php echo e(format_currency($purchase->total)); ?>

                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-calculator fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-left-success h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                الضريبة (0%)
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="tax">
                                                <?php echo e(format_currency(0)); ?>

                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-left-info h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                المجموع الكلي
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="grandTotal">
                                                <?php echo e(format_currency($purchase->total)); ?>

                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card shadow mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save me-2"></i>تحديث المشتريات
                            </button>
                            <a href="<?php echo e(user_route('purchases.index')); ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                تأكد من صحة جميع البيانات قبل الحفظ
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const productsContainer = document.getElementById('productsContainer');
                const addRowBtn = document.getElementById('addRow');
                const emptyMessage = document.getElementById('emptyProductsMessage');
                let rowCount = <?php echo e($purchase->products->count()); ?>;

                // Add new product
                addRowBtn.addEventListener('click', function() {
                    if (emptyMessage) {
                        emptyMessage.style.display = 'none';
                    }

                    const newProductCard = createProductCard(rowCount);
                    productsContainer.appendChild(newProductCard);
                    rowCount++;
                    attachProductEventListeners(newProductCard);
                    updateProductNumbers();
                });

                // Create new product card
                function createProductCard(index) {
                    const cardDiv = document.createElement('div');
                    cardDiv.className = 'product-row card border-left-primary shadow-sm mb-3';
                    cardDiv.innerHTML = `
                        <div class="card-header py-2 bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="m-0 text-primary">
                                    <i class="fas fa-box me-2"></i>منتج #${index + 1}
                                </h6>
                                <button type="button" class="btn btn-danger btn-sm remove-row">
                                    <i class="fas fa-trash me-1"></i>حذف
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-tag text-info me-2"></i>المنتج
                                    </label>
                                    <select name="products[${index}][id]" class="form-select product-select" required>
                                        <option value="">-- اختر المنتج --</option>
                                        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($p->id); ?>" data-price="<?php echo e($p->cost_price); ?>"><?php echo e($p->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-cubes text-success me-2"></i>الكمية
                                    </label>
                                    <input type="number" name="products[${index}][quantity]" class="form-control quantity" min="1" required>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-dollar-sign text-warning me-2"></i>السعر
                                    </label>
                                    <div class="input-group">
                                        <input type="number" name="products[${index}][price]" class="form-control price" step="0.01" min="0" required>
                                        <span class="input-group-text"><?php echo e(currency_symbol()); ?></span>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-calculator text-primary me-2"></i>المجموع
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control total bg-light" readonly>
                                        <span class="input-group-text"><?php echo e(currency_symbol()); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    return cardDiv;
                }

                // Attach event listeners to a product card
                function attachProductEventListeners(card) {
                    const productSelect = card.querySelector('.product-select');
                    const quantityInput = card.querySelector('.quantity');
                    const priceInput = card.querySelector('.price');
                    const removeBtn = card.querySelector('.remove-row');

                    // Product selection change
                    productSelect.addEventListener('change', function() {
                        const selectedOption = this.options[this.selectedIndex];
                        if (selectedOption.value) {
                            priceInput.value = selectedOption.dataset.price;
                            calculateProductTotal(card);
                        }
                    });

                    // Quantity or price change
                    [quantityInput, priceInput].forEach(input => {
                        input.addEventListener('input', function() {
                            calculateProductTotal(card);
                        });
                    });

                    // Remove product
                    removeBtn.addEventListener('click', function() {
                        card.remove();
                        updateProductNumbers();
                        calculateGrandTotal();

                        // Show empty message if no products
                        if (document.querySelectorAll('.product-row').length === 0 && emptyMessage) {
                            emptyMessage.style.display = 'block';
                        }
                    });
                }

                // Calculate product total
                function calculateProductTotal(card) {
                    const quantity = parseFloat(card.querySelector('.quantity').value) || 0;
                    const price = parseFloat(card.querySelector('.price').value) || 0;
                    const total = quantity * price;
                    card.querySelector('.total').value = total.toFixed(2);
                    calculateGrandTotal();
                }

                // Update product numbers
                function updateProductNumbers() {
                    document.querySelectorAll('.product-row').forEach((card, index) => {
                        const header = card.querySelector('.card-header h6');
                        header.innerHTML = `<i class="fas fa-box me-2"></i>منتج #${index + 1}`;

                        // Update input names
                        const inputs = card.querySelectorAll('input, select');
                        inputs.forEach(input => {
                            if (input.name) {
                                input.name = input.name.replace(/\[\d+\]/, `[${index}]`);
                            }
                        });
                    });
                }

                // Calculate grand total with currency formatting
                function calculateGrandTotal() {
                    const totals = Array.from(document.querySelectorAll('.total')).map(input => parseFloat(input
                        .value) || 0);
                    const grandTotal = totals.reduce((sum, total) => sum + total, 0);

                    // Update all total displays
                    document.getElementById('subtotal').textContent = formatCurrency(grandTotal);
                    document.getElementById('grandTotal').textContent = formatCurrency(grandTotal);
                }

                // Format currency
                function formatCurrency(amount) {
                    return new Intl.NumberFormat('ar-EG', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    }).format(amount) + ' <?php echo e(currency_symbol()); ?>';
                }

                // Attach event listeners to all existing products
                document.querySelectorAll('.product-row').forEach(card => {
                    attachProductEventListeners(card);
                });

                // Form validation and submission
                document.getElementById('purchaseForm').addEventListener('submit', function(e) {
                    const submitBtn = document.getElementById('submitBtn');
                    const products = document.querySelectorAll('.product-row');
                    let hasValidProducts = false;

                    products.forEach(card => {
                        const product = card.querySelector('.product-select').value;
                        const quantity = card.querySelector('.quantity').value;
                        const price = card.querySelector('.price').value;

                        if (product && quantity && price && quantity > 0 && price >= 0) {
                            hasValidProducts = true;
                        }
                    });

                    if (!hasValidProducts) {
                        e.preventDefault();
                        alert('الرجاء إضافة منتج واحد صحيح على الأقل إلى عملية الشراء.');
                        return;
                    }

                    // Show loading state
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
                });
            });
        </script>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('styles'); ?>
        <style>
            /* Modern Card Styles */
            .border-left-primary {
                border-left: 0.25rem solid #4e73df !important;
            }

            .border-left-success {
                border-left: 0.25rem solid #1cc88a !important;
            }

            .border-left-info {
                border-left: 0.25rem solid #36b9cc !important;
            }

            .border-left-warning {
                border-left: 0.25rem solid #f6c23e !important;
            }

            .border-left-danger {
                border-left: 0.25rem solid #e74a3b !important;
            }

            /* Product Card Animations */
            .product-row {
                transition: all 0.3s ease;
            }

            .product-row:hover {
                transform: translateY(-2px);
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
            }

            /* Form Enhancements */
            .form-control:focus,
            .form-select:focus {
                border-color: #4e73df;
                box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
            }

            /* Button Enhancements */
            .btn {
                border-radius: 0.35rem;
                font-weight: 600;
            }

            .btn-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
            }

            .btn-primary:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                transform: translateY(-1px);
            }

            /* Loading Animation */
            .fa-spin {
                animation: fa-spin 1s infinite linear;
            }

            @keyframes fa-spin {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }

            /* RTL Support */
            .form-label {
                text-align: right;
                font-weight: 600;
            }

            .card-header h6 {
                text-align: right;
            }

            .input-group-text {
                background-color: #f8f9fc;
                border-color: #d1d3e2;
                color: #5a5c69;
                font-weight: 600;
            }

            /* Empty State */
            #emptyProductsMessage {
                background: linear-gradient(135deg, #f8f9fc 0%, #eaecf4 100%);
                border-radius: 0.5rem;
                border: 2px dashed #d1d3e2;
            }

            /* Responsive Design */
            @media (max-width: 768px) {
                .d-sm-flex {
                    flex-direction: column;
                    gap: 1rem;
                }

                .product-row .col-md-4,
                .product-row .col-md-3,
                .product-row .col-md-2 {
                    margin-bottom: 1rem;
                }
            }
        </style>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\purchases\edit.blade.php ENDPATH**/ ?>