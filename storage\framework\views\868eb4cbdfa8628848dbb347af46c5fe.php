<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">دفع مبلغ للمورد</h1>
                <p class="text-muted mb-0">إكمال دفعة لعملية شراء رقم #<?php echo e($purchase->id); ?></p>
            </div>
            <div>
                <a href="<?php echo e(user_route('supplier-payments.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Purchase Details -->
            <div class="col-lg-4 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">تفاصيل عملية الشراء</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">رقم العملية</label>
                            <div class="fw-bold">#<?php echo e($purchase->id); ?></div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">المورد</label>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle bg-primary text-white me-2">
                                    <?php echo e(substr($purchase->supplier->name, 0, 1)); ?>

                                </div>
                                <div>
                                    <div class="fw-bold"><?php echo e($purchase->supplier->name); ?></div>
                                    <?php if($purchase->supplier->phone): ?>
                                        <small class="text-muted"><?php echo e($purchase->supplier->phone); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">تاريخ الشراء</label>
                            <div class="fw-bold"><?php echo e($purchase->purchase_date); ?></div>
                        </div>

                        <hr>

                        <div class="mb-3">
                            <label class="form-label text-muted">إجمالي المبلغ</label>
                            <div class="fw-bold text-primary"><?php echo e(format_currency($purchase->total_amount)); ?></div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">المبلغ المدفوع</label>
                            <div class="fw-bold text-success"><?php echo e(format_currency($purchase->paid_amount)); ?></div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">المبلغ المتبقي</label>
                            <div class="fw-bold text-danger fs-5">
                                <?php echo e(format_currency($purchase->actual_remaining_amount)); ?>

                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">حالة الدفع</label>
                            <div>
                                <span class="badge bg-<?php echo e($purchase->payment_status_color); ?> fs-6">
                                    <?php echo e($purchase->payment_status); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">تفاصيل الدفعة</h6>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo e(user_route('supplier-payments.store', $purchase)); ?>" method="POST">
                            <?php echo csrf_field(); ?>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="amount" class="form-label">مبلغ الدفعة <span
                                            class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" name="amount" id="amount"
                                            class="form-control <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            value="<?php echo e(old('amount')); ?>" step="0.01" min="0.01"
                                            max="<?php echo e($purchase->actual_remaining_amount); ?>" placeholder="0.00" required>
                                        <span class="input-group-text"><?php echo e(currency_symbol()); ?></span>
                                    </div>
                                    <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <small class="form-text text-muted">
                                        الحد الأقصى: <?php echo e(format_currency($purchase->actual_remaining_amount)); ?>

                                    </small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="payment_method" class="form-label">طريقة الدفع <span
                                            class="text-danger">*</span></label>
                                    <select name="payment_method" id="payment_method"
                                        class="form-select <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                        <option value="">اختر طريقة الدفع</option>
                                        <option value="cash" <?php echo e(old('payment_method') == 'cash' ? 'selected' : ''); ?>>
                                            نقدي
                                        </option>
                                        <option value="bank_transfer"
                                            <?php echo e(old('payment_method') == 'bank_transfer' ? 'selected' : ''); ?>>تحويل بنكي
                                        </option>
                                        <option value="check"
                                            <?php echo e(old('payment_method') == 'check' ? 'selected' : ''); ?>>شيك
                                        </option>
                                        <option value="credit_card"
                                            <?php echo e(old('payment_method') == 'credit_card' ? 'selected' : ''); ?>>بطاقة ائتمان
                                        </option>
                                        <option value="other"
                                            <?php echo e(old('payment_method') == 'other' ? 'selected' : ''); ?>>
                                            أخرى</option>
                                    </select>
                                    <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="reference_number" class="form-label">رقم المرجع</label>
                                    <input type="text" name="reference_number" id="reference_number"
                                        class="form-control <?php $__errorArgs = ['reference_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        value="<?php echo e(old('reference_number')); ?>"
                                        placeholder="رقم الشيك، رقم التحويل، إلخ">
                                    <?php $__errorArgs = ['reference_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <small class="form-text text-muted">
                                        اختياري - رقم الشيك أو التحويل أو أي مرجع آخر
                                    </small>
                                </div>
                            </div>
                    </div>

                    <div class="mb-4">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea name="notes" id="notes" class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" rows="3"
                            placeholder="أي ملاحظات إضافية حول هذه الدفعة"><?php echo e(old('notes')); ?></textarea>
                        <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Quick Amount Buttons -->
                    <div class="mb-4">
                        <label class="form-label">مبالغ سريعة</label>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm quick-amount"
                                data-amount="<?php echo e($purchase->actual_remaining_amount); ?>">
                                المبلغ كاملاً (<?php echo e(format_currency($purchase->actual_remaining_amount)); ?>)
                            </button>
                            <?php if($purchase->actual_remaining_amount >= 1000): ?>
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-amount"
                                    data-amount="1000">
                                    1,000 <?php echo e(currency_symbol()); ?>

                                </button>
                            <?php endif; ?>
                            <?php if($purchase->actual_remaining_amount >= 500): ?>
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-amount"
                                    data-amount="500">
                                    500 <?php echo e(currency_symbol()); ?>

                                </button>
                            <?php endif; ?>
                            <?php if($purchase->actual_remaining_amount >= 100): ?>
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-amount"
                                    data-amount="100">
                                    100 <?php echo e(currency_symbol()); ?>

                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?php echo e(user_route('supplier-payments.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-money-bill-wave me-2"></i>تسجيل الدفعة
                        </button>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    </div>

    <style>
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Quick amount buttons
            document.querySelectorAll('.quick-amount').forEach(button => {
                button.addEventListener('click', function() {
                    const amount = this.getAttribute('data-amount');
                    document.getElementById('amount').value = amount;
                });
            });

            // Auto-focus on amount field
            document.getElementById('amount').focus();
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\supplier-payments\create.blade.php ENDPATH**/ ?>