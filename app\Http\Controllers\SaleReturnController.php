<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\SaleReturn;
use App\Models\SaleReturnItem;
use App\Models\Customer;
use App\Models\Branch;
use App\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SaleReturnController extends Controller
{
    public function index(Request $request)
    {
        $query = SaleReturn::with(['sale', 'customer', 'user', 'branch', 'store'])
            ->orderBy('created_at', 'desc');

        // Filter by customer if provided
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // Filter by status if provided
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by return type if provided
        if ($request->filled('return_type')) {
            $query->where('return_type', $request->return_type);
        }

        // Filter by date range if provided
        if ($request->filled('date_from')) {
            $query->whereDate('return_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('return_date', '<=', $request->date_to);
        }

        $returns = $query->paginate(15);
        $customers = Customer::orderBy('name')->get();

        return view('sale-returns.index', compact('returns', 'customers'));
    }

    public function create(Request $request)
    {
        $sale = null;
        if ($request->filled('sale_id')) {
            $sale = Sale::with(['customer', 'items.product', 'branch', 'store'])->findOrFail($request->sale_id);

            if (!$sale->canBeReturned()) {
                return redirect()->back()->with('error', 'هذه العملية لا يمكن إرجاعها');
            }
        }

        $customers = Customer::orderBy('name')->get();
        $branches = Branch::where('is_active', true)->orderBy('name')->get();
        $stores = Store::where('is_active', true)->orderBy('name')->get();

        // Get sales that can be returned
        $returnableSales = Sale::with('customer')
            ->where('status', 'completed')
            ->whereHas('items')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('sale-returns.create', compact('sale', 'customers', 'branches', 'stores', 'returnableSales'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'sale_id' => 'required|exists:sales,id',
            'return_date' => 'required|date',
            'reason' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:1000',
            'refund_amount' => 'nullable|numeric|min:0',
            'items' => 'required|array|min:1',
            'items.*.sale_item_id' => 'required|exists:sale_items,id',
            'items.*.quantity_returned' => 'required|numeric|min:0.01',
            'items.*.condition' => 'required|in:good,damaged,expired,defective',
            'items.*.item_notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $sale = Sale::with(['items', 'customer'])->findOrFail($request->sale_id);

            if (!$sale->canBeReturned()) {
                return redirect()->back()->with('error', 'هذه العملية لا يمكن إرجاعها');
            }

            // Calculate total return amount
            $totalAmount = 0;
            $validatedItems = [];

            foreach ($request->items as $itemData) {
                $saleItem = $sale->items()->findOrFail($itemData['sale_item_id']);

                // Check if quantity is valid
                $alreadyReturned = SaleReturnItem::whereHas('saleReturn', function ($q) use ($sale) {
                    $q->where('sale_id', $sale->id)->where('status', '!=', 'cancelled');
                })->where('sale_item_id', $saleItem->id)->sum('quantity_returned');

                $availableQuantity = $saleItem->quantity - $alreadyReturned;

                if ($itemData['quantity_returned'] > $availableQuantity) {
                    return redirect()->back()
                        ->withInput()
                        ->with('error', "الكمية المطلوب إرجاعها للمنتج {$saleItem->product->name} تتجاوز الكمية المتاحة");
                }

                $itemTotal = $itemData['quantity_returned'] * $saleItem->price;
                $totalAmount += $itemTotal;

                $validatedItems[] = [
                    'sale_item_id' => $saleItem->id,
                    'product_id' => $saleItem->product_id,
                    'quantity_returned' => $itemData['quantity_returned'],
                    'original_quantity' => $saleItem->quantity,
                    'sale_price' => $saleItem->price,
                    'total_amount' => $itemTotal,
                    'condition' => $itemData['condition'],
                    'item_notes' => $itemData['item_notes'] ?? null,
                ];
            }

            // Determine return type
            $returnType = count($validatedItems) === $sale->items->count() ? 'full' : 'partial';

            // Create sale return
            $saleReturn = SaleReturn::create([
                'sale_id' => $sale->id,
                'customer_id' => $sale->customer_id,
                'branch_id' => $sale->branch_id,
                'store_id' => $sale->store_id,
                'user_id' => auth()->id() ?? 1,
                'total_amount' => $totalAmount,
                'refund_amount' => $request->refund_amount ?? 0,
                'status' => 'pending',
                'return_type' => $returnType,
                'reason' => $request->reason,
                'notes' => $request->notes,
                'return_date' => $request->return_date,
            ]);

            // Create return items
            foreach ($validatedItems as $itemData) {
                $saleReturn->items()->create($itemData);
            }

            DB::commit();

            return redirect()->to(user_route('sale-returns.show', $saleReturn))
                ->with('success', 'تم إنشاء مرتجع المبيعات بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating sale return: ' . $e->getMessage());

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء المرتجع: ' . $e->getMessage());
        }
    }

    public function show(SaleReturn $saleReturn)
    {
        $saleReturn->load(['sale.customer', 'customer', 'user', 'approvedBy', 'items.product', 'items.saleItem', 'branch', 'store']);

        return view('sale-returns.show', compact('saleReturn'));
    }

    public function edit(SaleReturn $saleReturn)
    {
        if (!$saleReturn->canBeEdited()) {
            return redirect()->back()->with('error', 'لا يمكن تعديل هذا المرتجع');
        }

        $saleReturn->load(['sale.items.product', 'items']);
        $customers = Customer::orderBy('name')->get();
        $branches = Branch::where('is_active', true)->orderBy('name')->get();
        $stores = Store::where('is_active', true)->orderBy('name')->get();

        return view('sale-returns.edit', compact('saleReturn', 'customers', 'branches', 'stores'));
    }

    public function update(Request $request, SaleReturn $saleReturn)
    {
        if (!$saleReturn->canBeEdited()) {
            return redirect()->back()->with('error', 'لا يمكن تعديل هذا المرتجع');
        }

        $request->validate([
            'return_date' => 'required|date',
            'reason' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:1000',
            'refund_amount' => 'nullable|numeric|min:0',
        ]);

        try {
            $saleReturn->update([
                'return_date' => $request->return_date,
                'reason' => $request->reason,
                'notes' => $request->notes,
                'refund_amount' => $request->refund_amount ?? 0,
            ]);

            return redirect()->to(user_route('sale-returns.show', $saleReturn))
                ->with('success', 'تم تحديث المرتجع بنجاح');
        } catch (\Exception $e) {
            Log::error('Error updating sale return: ' . $e->getMessage());

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث المرتجع: ' . $e->getMessage());
        }
    }

    public function destroy(SaleReturn $saleReturn)
    {
        if (!$saleReturn->canBeCancelled()) {
            return redirect()->back()->with('error', 'لا يمكن حذف هذا المرتجع');
        }

        try {
            $saleReturn->cancel('تم حذف المرتجع من قبل المستخدم');

            return redirect()->to(user_route('sale-returns.index'))
                ->with('success', 'تم إلغاء المرتجع بنجاح');
        } catch (\Exception $e) {
            Log::error('Error cancelling sale return: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إلغاء المرتجع: ' . $e->getMessage());
        }
    }

    public function approve(SaleReturn $saleReturn)
    {
        if (!$saleReturn->canBeApproved()) {
            return redirect()->back()->with('error', 'لا يمكن الموافقة على هذا المرتجع');
        }

        try {
            $saleReturn->approve();

            return redirect()->back()->with('success', 'تم الموافقة على المرتجع بنجاح');
        } catch (\Exception $e) {
            Log::error('Error approving sale return: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء الموافقة على المرتجع: ' . $e->getMessage());
        }
    }

    public function complete(SaleReturn $saleReturn)
    {
        if (!$saleReturn->canBeCompleted()) {
            return redirect()->back()->with('error', 'لا يمكن إكمال هذا المرتجع');
        }

        try {
            $saleReturn->complete();

            return redirect()->back()->with('success', 'تم إكمال المرتجع بنجاح');
        } catch (\Exception $e) {
            Log::error('Error completing sale return: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إكمال المرتجع: ' . $e->getMessage());
        }
    }

    public function getSaleDetails(Request $request)
    {
        $sale = Sale::with(['customer', 'items.product'])->find($request->sale_id);

        if (!$sale) {
            return response()->json(['error' => 'العملية غير موجودة'], 404);
        }

        if (!$sale->canBeReturned()) {
            return response()->json(['error' => 'هذه العملية لا يمكن إرجاعها'], 400);
        }

        // Get already returned quantities for each item
        $returnedQuantities = [];
        foreach ($sale->items as $item) {
            $returned = SaleReturnItem::whereHas('saleReturn', function ($q) use ($sale) {
                $q->where('sale_id', $sale->id)->where('status', '!=', 'cancelled');
            })->where('sale_item_id', $item->id)->sum('quantity_returned');

            $returnedQuantities[$item->id] = $returned;
        }

        return response()->json([
            'sale' => [
                'id' => $sale->id,
                'invoice_number' => $sale->invoice_number,
                'customer_name' => $sale->customer?->name ?? 'عميل نقدي',
                'total_amount' => $sale->total_amount,
                'discount_amount' => $sale->discount_amount,
                'paid_amount' => $sale->getTotalPaymentsAttribute(),
                'remaining_amount' => $sale->getActualRemainingAmountAttribute(),
                'payment_status' => $sale->payment_status,
                'items' => $sale->items->map(function ($item) use ($returnedQuantities) {
                    $returned = $returnedQuantities[$item->id] ?? 0;
                    return [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'product_name' => $item->product->name,
                        'quantity' => $item->quantity,
                        'returned_quantity' => $returned,
                        'available_quantity' => $item->quantity - $returned,
                        'sale_price' => $item->price,
                        'total_amount' => $item->subtotal,
                    ];
                })->filter(function ($item) {
                    return $item['available_quantity'] > 0;
                })->values()
            ]
        ]);
    }
}
