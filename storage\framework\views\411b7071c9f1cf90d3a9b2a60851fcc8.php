<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-clock text-warning"></i> <?php echo e(__('العمليات المعلقة')); ?>

                </h2>
                <p class="text-muted small mb-0">عمليات النقل التي تحتاج موافقة</p>
            </div>
            <div>
                <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إنشاء عملية نقل
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="container-fluid">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    إجمالي المعلق
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['total_pending']); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    فرع إلى فرع
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['branch_to_branch']); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-building fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    فرع إلى مخزن
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['branch_to_store']); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-arrow-right fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    مخزن إلى فرع
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['store_to_branch']); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-arrow-left fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter"></i> البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('admin.transfers.pending')); ?>">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo e(request('search')); ?>" placeholder="اسم المنتج أو الكود">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="source_type" class="form-label">نوع المصدر</label>
                            <select class="form-select" id="source_type" name="source_type">
                                <option value="">جميع الأنواع</option>
                                <option value="branch" <?php echo e(request('source_type') == 'branch' ? 'selected' : ''); ?>>فرع</option>
                                <option value="store" <?php echo e(request('source_type') == 'store' ? 'selected' : ''); ?>>مخزن</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="destination_type" class="form-label">نوع الوجهة</label>
                            <select class="form-select" id="destination_type" name="destination_type">
                                <option value="">جميع الأنواع</option>
                                <option value="branch" <?php echo e(request('destination_type') == 'branch' ? 'selected' : ''); ?>>فرع</option>
                                <option value="store" <?php echo e(request('destination_type') == 'store' ? 'selected' : ''); ?>>مخزن</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Pending Transfers Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-table"></i> العمليات المعلقة
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>رقم العملية</th>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>من</th>
                                <th>إلى</th>
                                <th>طلب بواسطة</th>
                                <th>تاريخ الطلب</th>
                                <th>الملاحظات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $transfers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transfer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td class="fw-bold">#<?php echo e($transfer->id); ?></td>
                                    <td><?php echo e($transfer->product->name); ?></td>
                                    <td class="text-center">
                                        <span class="badge bg-info"><?php echo e(number_format($transfer->quantity, 2)); ?></span>
                                    </td>
                                    <td>
                                        <?php if($transfer->source_type == 'branch'): ?>
                                            <span class="badge bg-primary">فرع</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">مخزن</span>
                                        <?php endif; ?>
                                        <?php echo e($transfer->sourceLocation->name); ?>

                                    </td>
                                    <td>
                                        <?php if($transfer->destination_type == 'branch'): ?>
                                            <span class="badge bg-primary">فرع</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">مخزن</span>
                                        <?php endif; ?>
                                        <?php echo e($transfer->destinationLocation->name); ?>

                                    </td>
                                    <td><?php echo e($transfer->user->name); ?></td>
                                    <td><?php echo e($transfer->created_at->format('Y-m-d H:i')); ?></td>
                                    <td><?php echo e($transfer->notes ?? '-'); ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-success" onclick="approveTransfer(<?php echo e($transfer->id); ?>)">
                                                <i class="fas fa-check"></i> موافقة
                                            </button>
                                            <button class="btn btn-danger" onclick="rejectTransfer(<?php echo e($transfer->id); ?>)">
                                                <i class="fas fa-times"></i> رفض
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                        <p class="text-muted">لا توجد عمليات نقل معلقة</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if($transfers->hasPages()): ?>
                    <div class="mt-4">
                        <?php echo e($transfers->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Approval Modal -->
    <div class="modal fade" id="approvalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الموافقة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="approvalForm" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <p>هل أنت متأكد من الموافقة على هذه العملية؟</p>
                        <div class="mb-3">
                            <label for="approval_notes" class="form-label">ملاحظات (اختيارية)</label>
                            <textarea class="form-control" id="approval_notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">موافقة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Rejection Modal -->
    <div class="modal fade" id="rejectionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الرفض</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="rejectionForm" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <p>هل أنت متأكد من رفض هذه العملية؟</p>
                        <div class="mb-3">
                            <label for="rejection_notes" class="form-label">سبب الرفض</label>
                            <textarea class="form-control" id="rejection_notes" name="notes" rows="3" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">رفض</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
        function approveTransfer(transferId) {
            const form = document.getElementById('approvalForm');
            form.action = `<?php echo e(route('admin.transfers.approve', '')); ?>/${transferId}`;
            new bootstrap.Modal(document.getElementById('approvalModal')).show();
        }

        function rejectTransfer(transferId) {
            const form = document.getElementById('rejectionForm');
            form.action = `<?php echo e(route('admin.transfers.reject', '')); ?>/${transferId}`;
            new bootstrap.Modal(document.getElementById('rejectionModal')).show();
        }
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\admin\transfers\pending.blade.php ENDPATH**/ ?>