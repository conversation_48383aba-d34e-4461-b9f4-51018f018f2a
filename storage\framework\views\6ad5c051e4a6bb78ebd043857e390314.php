<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-user text-primary"></i> <?php echo e(__('تفاصيل المستخدم')); ?>

                </h2>
                <p class="text-muted small mb-0">عرض معلومات وإحصائيات المستخدم</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(route('admin.settings.users.edit', $user)); ?>" class="btn btn-primary">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <a href="<?php echo e(route('admin.settings.users')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="container-fluid">
        <!-- User Info Card -->
        <div class="row mb-4">
            <div class="col-lg-4">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user-circle"></i> معلومات المستخدم
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <?php if($user->avatar): ?>
                                <img src="<?php echo e(asset('storage/' . $user->avatar)); ?>" 
                                     alt="<?php echo e($user->name); ?>" 
                                     class="rounded-circle" 
                                     style="width: 100px; height: 100px; object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center text-white"
                                     style="width: 100px; height: 100px; font-size: 2rem;">
                                    <?php echo e(strtoupper(substr($user->name, 0, 1))); ?>

                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <h4 class="mb-1"><?php echo e($user->name); ?></h4>
                        <p class="text-muted mb-2"><?php echo e($user->email); ?></p>
                        
                        <div class="mb-3">
                            <?php if($user->is_active): ?>
                                <span class="badge bg-success">نشط</span>
                            <?php else: ?>
                                <span class="badge bg-danger">غير نشط</span>
                            <?php endif; ?>
                        </div>

                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h5 class="mb-0 text-primary"><?php echo e($user->role->name); ?></h5>
                                    <small class="text-muted">الدور</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h5 class="mb-0 text-success"><?php echo e($user->branch->name ?? 'غير محدد'); ?></h5>
                                <small class="text-muted">الفرع</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-8">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي المبيعات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['total_sales']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            قيمة المبيعات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo e(number_format($stats['total_sales_amount'], 2)); ?> ج.م
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card shadow">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-address-card"></i> معلومات الاتصال
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">البريد الإلكتروني:</label>
                                <p class="mb-0"><?php echo e($user->email); ?></p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">رقم الهاتف:</label>
                                <p class="mb-0"><?php echo e($user->phone ?? 'غير محدد'); ?></p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">آخر تسجيل دخول:</label>
                                <p class="mb-0">
                                    <?php if($stats['last_login']): ?>
                                        <?php echo e($stats['last_login']->diffForHumans()); ?>

                                    <?php else: ?>
                                        لم يسجل دخول بعد
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ إنشاء الحساب:</label>
                                <p class="mb-0"><?php echo e($stats['account_created']->format('Y-m-d H:i')); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Sales Activity -->
        <?php if($recentSales->count() > 0): ?>
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-history"></i> آخر المبيعات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>الفرع</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentSales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="fw-bold"><?php echo e($sale->invoice_number); ?></td>
                                        <td><?php echo e($sale->customer->name ?? 'عميل نقدي'); ?></td>
                                        <td><?php echo e($sale->branch->name ?? 'غير محدد'); ?></td>
                                        <td class="fw-bold"><?php echo e(number_format($sale->total_amount, 2)); ?> ج.م</td>
                                        <td><?php echo e($sale->created_at->format('Y-m-d H:i')); ?></td>
                                        <td>
                                            <a href="<?php echo e(route('admin.sales.show', $sale)); ?>" 
                                               class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مبيعات بعد</h5>
                    <p class="text-muted">لم يقم هذا المستخدم بأي عمليات بيع حتى الآن</p>
                </div>
            </div>
        <?php endif; ?>

        <!-- User Permissions -->
        <?php if($user->role && $user->role->permissions): ?>
            <div class="card shadow mt-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-key"></i> الصلاحيات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php $__currentLoopData = $user->role->permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-4 mb-2">
                                <span class="badge bg-primary">
                                    <i class="fas fa-check"></i> <?php echo e($permission); ?>

                                </span>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\users\show.blade.php ENDPATH**/ ?>