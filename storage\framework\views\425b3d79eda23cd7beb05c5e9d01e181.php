<?php $__env->startSection('title', 'ملخص مدفوعات المورد - ' . $supplier->name); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">ملخص مدفوعات المورد</h1>
                <p class="text-muted mb-0"><?php echo e($supplier->name); ?></p>
            </div>
            <div>
                <a href="<?php echo e(user_route('payment-reports.index')); ?>" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
                <a href="<?php echo e(user_route('suppliers.show', $supplier)); ?>" class="btn btn-outline-info">
                    <i class="fas fa-user me-2"></i>عرض المورد
                </a>
            </div>
        </div>

        <!-- Date Filter -->
        <div class="card shadow mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3 align-items-end">
                    <div class="col-md-4">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="<?php echo e($dateFrom); ?>">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="<?php echo e($dateTo); ?>">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>تطبيق الفلتر
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Supplier Info and Statistics -->
        <div class="row mb-4">
            <!-- Supplier Information -->
            <div class="col-lg-4 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">معلومات المورد</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="avatar-circle bg-primary text-white mx-auto mb-2"
                                style="width: 60px; height: 60px; font-size: 24px;">
                                <?php echo e(substr($supplier->name, 0, 1)); ?>

                            </div>
                            <h5 class="fw-bold"><?php echo e($supplier->name); ?></h5>
                        </div>

                        <?php if($supplier->phone): ?>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-phone text-muted me-2"></i>
                                <span><?php echo e($supplier->phone); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if($supplier->email): ?>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-envelope text-muted me-2"></i>
                                <span><?php echo e($supplier->email); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if($supplier->address): ?>
                            <div class="d-flex align-items-start mb-2">
                                <i class="fas fa-map-marker-alt text-muted me-2 mt-1"></i>
                                <span><?php echo e($supplier->address); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Financial Statistics -->
            <div class="col-lg-8">
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            إجمالي المشتريات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo e(format_currency($totalPurchases)); ?>

                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            إجمالي المدفوع
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo e(format_currency($totalPaid)); ?>

                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card border-left-danger shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                            الرصيد المعلق
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo e(format_currency($totalOutstanding)); ?>

                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Outstanding Purchases -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">المشتريات المعلقة</h6>
                        <?php if($outstandingPurchases->count() > 0): ?>
                            <span class="badge bg-danger"><?php echo e($outstandingPurchases->count()); ?></span>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <?php if($outstandingPurchases->count() > 0): ?>
                            <?php $__currentLoopData = $outstandingPurchases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $purchase): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="d-flex align-items-center justify-content-between mb-3 p-2 border rounded">
                                    <div>
                                        <div class="fw-bold">عملية #<?php echo e($purchase->id); ?></div>
                                        <small class="text-muted"><?php echo e($purchase->purchase_date->format('Y-m-d')); ?></small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-danger">
                                            <?php echo e(format_currency($purchase->actual_remaining_amount)); ?></div>
                                        <small class="text-muted">من <?php echo e(format_currency($purchase->total_amount)); ?></small>
                                    </div>
                                    <div>
                                        <a href="<?php echo e(user_route('supplier-payments.create', $purchase)); ?>"
                                            class="btn btn-warning btn-sm">
                                            <i class="fas fa-money-bill-wave"></i>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="text-center py-3">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <p class="text-muted">لا توجد مشتريات معلقة</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">آخر المدفوعات</h6>
                        <?php if($recentPayments->count() > 0): ?>
                            <span class="badge bg-success"><?php echo e($recentPayments->count()); ?></span>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <?php if($recentPayments->count() > 0): ?>
                            <?php $__currentLoopData = $recentPayments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="d-flex align-items-center justify-content-between mb-3 p-2 border rounded">
                                    <div>
                                        <div class="fw-bold">عملية #<?php echo e($payment->transactionable->id); ?></div>
                                        <small class="text-muted"><?php echo e($payment->created_at->format('Y-m-d H:i')); ?></small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-success"><?php echo e(format_currency($payment->amount)); ?></div>
                                        <small class="text-muted"><?php echo e($payment->user->name ?? 'غير محدد'); ?></small>
                                    </div>
                                    <div>
                                        <a href="<?php echo e(user_route('supplier-payments.show', $payment->transactionable)); ?>"
                                            class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="text-center py-3">
                                <i class="fas fa-receipt fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد مدفوعات حديثة</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }

        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }

        .border-left-danger {
            border-left: 0.25rem solid #e74a3b !important;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my-pos-app\resources\views\payment-reports\supplier-summary.blade.php ENDPATH**/ ?>