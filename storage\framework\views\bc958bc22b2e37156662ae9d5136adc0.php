<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-money-bill-wave text-success me-2"></i>
                    دفعات العملاء
                </h1>
                <p class="mb-0 text-muted">إدارة ومتابعة دفعات العملاء</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(user_route('customer-payments.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة دفعة جديدة
                </a>
                <a href="<?php echo e(user_route('sales.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المبيعات
                </a>
            </div>
        </div>

        <!-- Filters Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter me-2"></i>البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(user_route('customer-payments.index')); ?>">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="customer_id" class="form-label">العميل</label>
                            <select class="form-select" id="customer_id" name="customer_id">
                                <option value="">جميع العملاء</option>
                                <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($customer->id); ?>"
                                        <?php echo e(request('customer_id') == $customer->id ? 'selected' : ''); ?>>
                                        <?php echo e($customer->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="payment_method" class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="payment_method" name="payment_method">
                                <option value="">جميع الطرق</option>
                                <option value="cash" <?php echo e(request('payment_method') == 'cash' ? 'selected' : ''); ?>>نقدي
                                </option>
                                <option value="card" <?php echo e(request('payment_method') == 'card' ? 'selected' : ''); ?>>بطاقة
                                </option>
                                <option value="bank_transfer"
                                    <?php echo e(request('payment_method') == 'bank_transfer' ? 'selected' : ''); ?>>تحويل بنكي
                                </option>
                                <option value="check" <?php echo e(request('payment_method') == 'check' ? 'selected' : ''); ?>>شيك
                                </option>
                                <option value="other" <?php echo e(request('payment_method') == 'other' ? 'selected' : ''); ?>>
                                    أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                value="<?php echo e(request('date_from')); ?>">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                value="<?php echo e(request('date_to')); ?>">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="<?php echo e(user_route('customer-payments.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Payments Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>قائمة الدفعات
                </h6>
            </div>
            <div class="card-body">
                <?php if($payments->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>رقم المرجع</th>
                                    <th>تاريخ الدفع</th>
                                    <th>المستخدم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <a href="<?php echo e(user_route('sales.show', $payment->sale)); ?>"
                                                class="text-decoration-none fw-bold text-primary">
                                                <?php echo e($payment->sale->invoice_number); ?>

                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-info text-white me-2">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">
                                                        <?php echo e($payment->sale->customer?->name ?? 'عميل نقدي'); ?>

                                                    </div>
                                                    <?php if($payment->sale->customer?->phone): ?>
                                                        <small
                                                            class="text-muted"><?php echo e($payment->sale->customer->phone); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">
                                                <?php echo e(number_format($payment->amount, 2)); ?> ج.م
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?php echo e($payment->payment_method_label); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php echo e($payment->reference_number ?? '-'); ?>

                                        </td>
                                        <td>
                                            <div><?php echo e($payment->payment_date->format('d/m/Y')); ?></div>
                                            <small
                                                class="text-muted"><?php echo e($payment->payment_date->format('h:i A')); ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo e($payment->user->name); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(user_route('customer-payments.show', $payment)); ?>"
                                                    class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if(auth()->user()->hasRole('admin')): ?>
                                                    <form method="POST"
                                                        action="<?php echo e(user_route('customer-payments.destroy', $payment)); ?>"
                                                        class="d-inline"
                                                        onsubmit="return confirm('هل أنت متأكد من حذف هذه الدفعة؟')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                            title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="2" class="text-end">إجمالي الدفعات:</th>
                                    <th class="text-success">
                                        <?php echo e(number_format($payments->sum('amount'), 2)); ?> ج.م
                                    </th>
                                    <th colspan="5"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        <?php echo e($payments->withQueryString()->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد دفعات</h5>
                        <p class="text-muted">لم يتم العثور على أي دفعات تطابق معايير البحث</p>
                        <a href="<?php echo e(user_route('customer-payments.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة دفعة جديدة
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views/customer-payments/index.blade.php ENDPATH**/ ?>