<?php $__env->startSection('title', 'تاريخ عمليات النقل'); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">تاريخ عمليات النقل</h1>
                <p class="text-muted mb-0">سجل تفصيلي لجميع عمليات نقل المخزون</p>
            </div>
            <div>
                <a href="<?php echo e(user_route('inventory-transfer-reports.index')); ?>" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
                <a href="<?php echo e(user_route('inventory-transfer-reports.export', array_merge(['type' => 'transfers'], request()->query()))); ?>"
                    class="btn btn-success">
                    <i class="fas fa-file-csv me-2"></i>تصدير
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-2">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="<?php echo e($dateFrom); ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="<?php echo e($dateTo); ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="pending" <?php echo e($status == 'pending' ? 'selected' : ''); ?>>قيد الانتظار</option>
                            <option value="approved" <?php echo e($status == 'approved' ? 'selected' : ''); ?>>موافق عليه</option>
                            <option value="shipped" <?php echo e($status == 'shipped' ? 'selected' : ''); ?>>تم الشحن</option>
                            <option value="received" <?php echo e($status == 'received' ? 'selected' : ''); ?>>تم الاستلام</option>
                            <option value="cancelled" <?php echo e($status == 'cancelled' ? 'selected' : ''); ?>>ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">من موقع</label>
                        <select name="from_location_id" class="form-select">
                            <option value="">جميع المواقع</option>
                            <optgroup label="المخازن">
                                <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($store->id); ?>"
                                        <?php echo e($fromLocationId == $store->id ? 'selected' : ''); ?>>
                                        <?php echo e($store->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </optgroup>
                            <optgroup label="الفروع">
                                <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($branch->id); ?>"
                                        <?php echo e($fromLocationId == $branch->id ? 'selected' : ''); ?>>
                                        <?php echo e($branch->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </optgroup>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">إلى موقع</label>
                        <select name="to_location_id" class="form-select">
                            <option value="">جميع المواقع</option>
                            <optgroup label="المخازن">
                                <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($store->id); ?>"
                                        <?php echo e($toLocationId == $store->id ? 'selected' : ''); ?>>
                                        <?php echo e($store->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </optgroup>
                            <optgroup label="الفروع">
                                <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($branch->id); ?>"
                                        <?php echo e($toLocationId == $branch->id ? 'selected' : ''); ?>>
                                        <?php echo e($branch->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </optgroup>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block w-100">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Transfer History Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">سجل عمليات النقل</h6>
            </div>
            <div class="card-body">
                <?php if($transfers->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-bold">رقم النقل</th>
                                    <th class="border-0 fw-bold">من موقع</th>
                                    <th class="border-0 fw-bold">إلى موقع</th>
                                    <th class="border-0 fw-bold">عدد المنتجات</th>
                                    <th class="border-0 fw-bold">الحالة</th>
                                    <th class="border-0 fw-bold">تاريخ الإنشاء</th>
                                    <th class="border-0 fw-bold">المستخدم</th>
                                    <th class="border-0 fw-bold text-center">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $transfers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transfer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="border-bottom">
                                        <td>
                                            <a href="<?php echo e(user_route('inventory-transfers.show', $transfer)); ?>"
                                                class="fw-bold text-primary text-decoration-none">
                                                #<?php echo e($transfer->id); ?>

                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-info text-white me-2">
                                                    <?php echo e(substr($transfer->fromStore ? $transfer->fromStore->name : $transfer->fromBranch->name, 0, 1)); ?>

                                                </div>
                                                <div>
                                                    <div class="fw-bold">
                                                        <?php echo e($transfer->fromStore ? $transfer->fromStore->name : $transfer->fromBranch->name); ?>

                                                    </div>
                                                    <small
                                                        class="text-muted"><?php echo e($transfer->fromStore ? 'مخزن' : 'فرع'); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-success text-white me-2">
                                                    <?php echo e(substr($transfer->toStore ? $transfer->toStore->name : $transfer->toBranch->name, 0, 1)); ?>

                                                </div>
                                                <div>
                                                    <div class="fw-bold">
                                                        <?php echo e($transfer->toStore ? $transfer->toStore->name : $transfer->toBranch->name); ?>

                                                    </div>
                                                    <small
                                                        class="text-muted"><?php echo e($transfer->toStore ? 'مخزن' : 'فرع'); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo e($transfer->items->count()); ?> منتج</span>
                                            <div class="small text-muted mt-1">
                                                إجمالي: <?php echo e($transfer->items->sum('quantity')); ?> قطعة
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                                $statusColors = [
                                                    'pending' => 'warning',
                                                    'approved' => 'info',
                                                    'shipped' => 'secondary',
                                                    'received' => 'success',
                                                    'cancelled' => 'danger',
                                                ];
                                                $statusLabels = [
                                                    'pending' => 'قيد الانتظار',
                                                    'approved' => 'موافق عليه',
                                                    'shipped' => 'تم الشحن',
                                                    'received' => 'تم الاستلام',
                                                    'cancelled' => 'ملغي',
                                                ];
                                            ?>
                                            <span class="badge bg-<?php echo e($statusColors[$transfer->status] ?? 'secondary'); ?>">
                                                <?php echo e($statusLabels[$transfer->status] ?? $transfer->status); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <div class="fw-bold"><?php echo e($transfer->created_at->format('Y-m-d')); ?></div>
                                            <small class="text-muted"><?php echo e($transfer->created_at->format('H:i')); ?></small>
                                            <div class="small text-muted"><?php echo e($transfer->created_at->diffForHumans()); ?>

                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-primary text-white me-2">
                                                    <?php echo e(substr($transfer->user->name ?? 'غير محدد', 0, 1)); ?>

                                                </div>
                                                <div>
                                                    <div class="fw-bold small"><?php echo e($transfer->user->name ?? 'غير محدد'); ?>

                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(user_route('inventory-transfers.show', $transfer)); ?>"
                                                    class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if($transfer->status === 'pending'): ?>
                                                    <a href="<?php echo e(user_route('inventory-transfers.edit', $transfer)); ?>"
                                                        class="btn btn-outline-warning btn-sm" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($transfers->appends(request()->query())->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد عمليات نقل</h5>
                        <p class="text-muted">لم يتم العثور على عمليات نقل في الفترة المحددة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .btn-group .btn {
            margin-left: 2px;
        }

        .btn-group .btn:first-child {
            margin-left: 0;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my-pos-app\resources\views\inventory-transfer-reports\transfer-history.blade.php ENDPATH**/ ?>