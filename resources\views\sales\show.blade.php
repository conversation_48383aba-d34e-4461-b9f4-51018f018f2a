<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('تفاصيل البيع') }} #{{ $sale->invoice_number }}
            </h2>
            <div>
                <a href="{{ user_route('sales.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> {{ __('العودة إلى المبيعات') }}
                </a>
                @if ($sale->status === 'pending')
                    <a href="{{ user_route('sales.edit', $sale) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> {{ __('تعديل') }}
                    </a>
                @endif
                <a href="{{ user_route('sales.print', $sale) }}" class="btn btn-info" target="_blank">
                    <i class="fas fa-print"></i> {{ __('طباعة') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Sale Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ __('معلومات البيع') }}</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>{{ __('رقم الفاتورة') }}</th>
                                            <td>{{ $sale->invoice_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('العميل') }}</th>
                                            <td>{{ $sale->customer ? $sale->customer->name : 'عميل نقدي' }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('الفرع') }}</th>
                                            <td>{{ $sale->branch->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('التاريخ') }}</th>
                                            <td>{{ $sale->created_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('الحالة') }}</th>
                                            <td>
                                                @if ($sale->status === 'completed')
                                                    <span class="badge bg-success">{{ __('مكتمل') }}</span>
                                                @elseif($sale->status === 'cancelled')
                                                    <span class="badge bg-danger">{{ __('ملغي') }}</span>
                                                @else
                                                    <span class="badge bg-warning">{{ __('قيد الانتظار') }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ __('ملخص البيع') }}</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>{{ __('عدد المنتجات') }}</th>
                                            <td>{{ $sale->items->count() }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('إجمالي الكمية') }}</th>
                                            <td>{{ $sale->items->sum('quantity') }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('المجموع') }}</th>
                                            <td>{{ number_format($sale->total_amount, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('الخصم') }}</th>
                                            <td>{{ number_format($sale->discount_amount, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('المبلغ المدفوع') }}</th>
                                            <td class="text-success">
                                                {{ number_format($sale->getTotalPaymentsAttribute(), 2) }} ج.م</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('المبلغ المتبقي') }}</th>
                                            <td
                                                class="{{ $sale->getActualRemainingAmountAttribute() > 0 ? 'text-danger' : 'text-success' }}">
                                                {{ number_format($sale->getActualRemainingAmountAttribute(), 2) }} ج.م
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payments Section -->
                    @if ($sale->customer)
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-money-bill-wave me-2"></i>{{ __('الدفعات') }}
                                </h5>
                                <div class="d-flex gap-2">
                                    @if ($sale->canReceivePayment())
                                        <a href="{{ user_route('customer-payments.create', ['sale_id' => $sale->id]) }}"
                                            class="btn btn-success btn-sm">
                                            <i class="fas fa-plus me-2"></i>إضافة دفعة
                                        </a>
                                    @endif
                                    @if ($sale->canBeReturned())
                                        <a href="{{ user_route('sale-returns.create', ['sale_id' => $sale->id]) }}"
                                            class="btn btn-warning btn-sm">
                                            <i class="fas fa-undo me-2"></i>إنشاء مرتجع
                                        </a>
                                    @endif
                                </div>
                            </div>
                            <div class="card-body">
                                @if ($sale->payments->count() > 0)
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>المبلغ</th>
                                                    <th>طريقة الدفع</th>
                                                    <th>رقم المرجع</th>
                                                    <th>تاريخ الدفع</th>
                                                    <th>المستخدم</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($sale->payments as $payment)
                                                    <tr>
                                                        <td>
                                                            <span class="fw-bold text-success">
                                                                {{ number_format($payment->amount, 2) }} ج.م
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-primary">
                                                                {{ $payment->payment_method_label }}
                                                            </span>
                                                        </td>
                                                        <td>{{ $payment->reference_number ?? '-' }}</td>
                                                        <td>
                                                            <div>{{ $payment->payment_date->format('d/m/Y') }}</div>
                                                            <small
                                                                class="text-muted">{{ $payment->payment_date->format('h:i A') }}</small>
                                                        </td>
                                                        <td>
                                                            <small
                                                                class="text-muted">{{ $payment->user->name }}</small>
                                                        </td>
                                                        <td>
                                                            <a href="{{ user_route('customer-payments.show', $payment) }}"
                                                                class="btn btn-sm btn-outline-info"
                                                                title="عرض التفاصيل">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                            <tfoot class="table-light">
                                                <tr>
                                                    <th class="text-success">
                                                        {{ number_format($sale->payments->sum('amount'), 2) }} ج.م
                                                    </th>
                                                    <th colspan="5" class="text-start">إجمالي الدفعات</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                @else
                                    <div class="text-center py-4">
                                        <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">لا توجد دفعات</h6>
                                        @if ($sale->canReceivePayment())
                                            <a href="{{ user_route('customer-payments.create', ['sale_id' => $sale->id]) }}"
                                                class="btn btn-success">
                                                <i class="fas fa-plus me-2"></i>إضافة دفعة
                                            </a>
                                        @endif
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Products Table -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ __('المنتجات') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th>{{ __('المنتج') }}</th>
                                            <th>{{ __('الكمية') }}</th>
                                            <th>{{ __('السعر') }}</th>
                                            <th>{{ __('المجموع') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($sale->items as $item)
                                            <tr>
                                                <td>{{ $item->product->name }}</td>
                                                <td>{{ $item->quantity }}</td>
                                                <td>{{ number_format($item->price, 2) }}</td>
                                                <td>{{ number_format($item->subtotal, 2) }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="3" class="text-start">{{ __('المجموع الكلي') }}</th>
                                            <th>{{ number_format($sale->total_amount, 2) }}</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Returns Section -->
                    @if ($sale->hasReturns())
                        <div class="card mt-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-undo me-2"></i>{{ __('المرتجعات') }}
                                </h5>
                                <a href="{{ user_route('sale-returns.index', ['sale_id' => $sale->id]) }}"
                                    class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-list me-2"></i>عرض جميع المرتجعات
                                </a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>رقم المرتجع</th>
                                                <th>المبلغ</th>
                                                <th>نوع المرتجع</th>
                                                <th>الحالة</th>
                                                <th>التاريخ</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($sale->returns->where('status', '!=', 'cancelled') as $return)
                                                <tr>
                                                    <td>
                                                        <a href="{{ user_route('sale-returns.show', $return) }}"
                                                            class="text-decoration-none fw-bold text-primary">
                                                            {{ $return->return_number }}
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <span class="fw-bold text-warning">
                                                            {{ number_format($return->total_amount, 2) }} ج.م
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">
                                                            {{ $return->return_type_label }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-{{ $return->status_color }}">
                                                            {{ $return->status_label }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $return->return_date->format('d/m/Y') }}</td>
                                                    <td>
                                                        <a href="{{ user_route('sale-returns.show', $return) }}"
                                                            class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                        <tfoot class="table-light">
                                            <tr>
                                                <th>الإجمالي:</th>
                                                <th class="text-warning">
                                                    {{ number_format($sale->total_returns, 2) }} ج.م
                                                </th>
                                                <th colspan="4"></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Actions -->
                    @if ($sale->status === 'pending')
                        <div class="row mt-4">
                            <div class="col-12 text-start">
                                <form action="{{ route('sales.complete', $sale) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-success"
                                        onclick="return confirm('{{ __('هل أنت متأكد من إكمال هذه العملية؟') }}')">
                                        <i class="fas fa-check"></i> {{ __('إكمال البيع') }}
                                    </button>
                                </form>
                                <form action="{{ route('sales.cancel', $sale) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-danger"
                                        onclick="return confirm('{{ __('هل أنت متأكد من إلغاء هذه العملية؟') }}')">
                                        <i class="fas fa-times"></i> {{ __('إلغاء البيع') }}
                                    </button>
                                </form>
                            </div>
                        </div>
                    @endif

                    <!-- Print Button -->
                    <div class="row mt-4">
                        <div class="col-12 text-start">
                            {{-- <button onclick="window.print()" class="btn btn-primary">
                                <i class="fas fa-print"></i> {{ __('طباعة الفاتورة') }}
                            </button> --}}
                            <a href="{{ user_route('sales.print', $sale) }}" class="btn btn-primary"
                                target="_blank">
                                <i class="fas fa-print"></i> {{ __('طباعة الفاتورة') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
        <style>
            .rtl {
                direction: rtl;
                text-align: right;
            }

            .rtl .table th,
            .rtl .table td {
                text-align: right;
            }

            .rtl .btn-group {
                flex-direction: row-reverse;
            }

            .rtl .btn-group .btn {
                margin-right: 0;
                margin-left: 0.25rem;
            }

            .rtl .text-start {
                text-align: right !important;
            }

            .rtl .text-end {
                text-align: left !important;
            }

            @media print {
                .no-print {
                    display: none !important;
                }

                .card {
                    border: none !important;
                }

                .card-header {
                    background-color: #fff !important;
                    border-bottom: 2px solid #000 !important;
                }

                .table {
                    border-collapse: collapse !important;
                }

                .table th,
                .table td {
                    border: 1px solid #000 !important;
                }
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            function printSale() {
                window.print();
            }
        </script>
    @endpush
</x-app-layout>
