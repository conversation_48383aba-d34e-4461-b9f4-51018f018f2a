<?php

use App\Http\Controllers\SellerDashboardController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\DirectTransferController;
use App\Http\Controllers\TransferController;
use App\Http\Controllers\CustomerPaymentController;
use App\Http\Controllers\SaleReturnController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Seller Routes
|--------------------------------------------------------------------------
|
| Here are the routes that are accessible by seller users.
| These routes are automatically prefixed with 'seller' and protected
| by the 'role:seller' middleware with branch access control.
|
*/

Route::middleware(['auth', 'verified', 'role:seller', 'branch.access', 'store.vs.branch:branch'])->prefix('seller')->name('seller.')->group(function () {

    // Seller Dashboard
    Route::get('/dashboard', [SellerDashboardController::class, 'index'])->name('dashboard');

    // Quick Sale Feature
    Route::get('/quick-sale', [SellerDashboardController::class, 'quickSale'])->name('quick-sale');
    Route::post('/quick-sale/process', [SellerDashboardController::class, 'processQuickSale'])->name('quick-sale.process');
    Route::post('/quick-sale/add-item', [SellerDashboardController::class, 'addQuickSaleItem'])->name('quick-sale.add-item');
    Route::delete('/quick-sale/remove-item/{item}', [SellerDashboardController::class, 'removeQuickSaleItem'])->name('quick-sale.remove-item');

    // Transfer Requests - Removed (keeping only direct transfers)

    // Direct Transfers
    Route::post('/transfer/direct', [SellerDashboardController::class, 'executeDirectTransfer'])->name('transfer.direct');
    Route::post('/transfer/auto', [SellerDashboardController::class, 'executeAutoTransfer'])->name('transfer.auto');
    Route::get('/transfer/branches', [SellerDashboardController::class, 'getAvailableBranches'])->name('transfer.branches');

    Route::post('/transfers/{transfer}/confirm', [SellerDashboardController::class, 'confirmTransferReceipt'])->name('transfers.confirm');

    // Price-related endpoints (must come before resource routes)
    Route::get('sales/product-price', [SaleController::class, 'getProductPrice'])->name('sales.product-price');
    Route::post('sales/validate-price', [SaleController::class, 'validatePrice'])->name('sales.validate-price');
    Route::get('sales/search-products', [SaleController::class, 'searchProducts'])->name('sales.search-products');

    // Sales Management (Seller can only manage their own sales)
    Route::resource('sales', SaleController::class)->except(['destroy']);
    Route::post('sales/{sale}/complete', [SaleController::class, 'complete'])->name('sales.complete');
    Route::post('sales/{sale}/cancel', [SaleController::class, 'cancel'])->name('sales.cancel');
    Route::get('sales/{sale}/print', [SaleController::class, 'print'])->name('sales.print');
    Route::get('sales/{sale}/receipt', [SaleController::class, 'receipt'])->name('sales.receipt');

    // Customer Payments Management
    Route::resource('customer-payments', CustomerPaymentController::class)->except(['destroy']);
    Route::get('customer-payments/sale-details', [CustomerPaymentController::class, 'getSaleDetails'])->name('customer-payments.sale-details');

    // Sale Returns Management (Limited permissions for sellers)
    Route::get('sale-returns/sale-details', [SaleReturnController::class, 'getSaleDetails'])->name('sale-returns.sale-details');
    Route::resource('sale-returns', SaleReturnController::class)->except(['destroy']);

    // Customer Management (Limited to branch customers)
    Route::resource('customers', CustomerController::class)->except(['destroy']);
    Route::get('customers/search', [CustomerController::class, 'search'])->name('customers.search');

    // Product Viewing (Read-only access to branch products)
    Route::get('/products', [ProductController::class, 'index'])->name('products.index');
    Route::get('/products/{product}', [ProductController::class, 'show'])->name('products.show');
    Route::get('/products/search', [ProductController::class, 'search'])->name('products.search');
    Route::get('/products/barcode/{barcode}', [ProductController::class, 'findByBarcode'])->name('products.barcode');

    // Category Viewing (Read-only access to categories)
    Route::get('/categories', [CategoryController::class, 'index'])->name('categories.index');
    Route::get('/categories/{category}', [CategoryController::class, 'show'])->name('categories.show');

    // Branch Viewing (Read-only access to branch information)
    Route::get('/branches/{branch}', [\App\Http\Controllers\BranchController::class, 'show'])->name('branches.show');

    // Inventory Management (Branch-specific)
    Route::get('/inventory', [SellerDashboardController::class, 'inventory'])->name('inventory');
    Route::get('/inventory/low-stock', [SellerDashboardController::class, 'lowStock'])->name('inventory.low-stock');
    Route::post('/inventory/update-stock', [SellerDashboardController::class, 'updateStock'])->name('inventory.update-stock');

    // Direct Transfer Routes (Sellers can transfer from/to their branch)
    Route::prefix('transfers')->name('transfers.')->group(function () {
        Route::get('/direct/create', [DirectTransferController::class, 'create'])->name('direct.create');
        Route::post('/direct', [DirectTransferController::class, 'store'])->name('direct.store');
        Route::get('/history', [TransferController::class, 'history'])->name('history');
        Route::post('/available-quantity', [DirectTransferController::class, 'getAvailableQuantity'])->name('available-quantity');
        Route::get('/{transfer}', [DirectTransferController::class, 'show'])->name('show');
    });

    // Limited Reports (Seller can only see their own performance)
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/my-sales', [ReportController::class, 'mySales'])->name('my-sales');
        Route::get('/daily-summary', [ReportController::class, 'dailySummary'])->name('daily-summary');
        Route::get('/monthly-summary', [ReportController::class, 'monthlySummary'])->name('monthly-summary');
    });

    // Cash Management
    Route::prefix('cash')->name('cash.')->group(function () {
        Route::get('/register', [SellerDashboardController::class, 'cashRegister'])->name('register');
        Route::post('/open', [SellerDashboardController::class, 'openCashRegister'])->name('open');
        Route::post('/close', [SellerDashboardController::class, 'closeCashRegister'])->name('close');
        Route::get('/count', [SellerDashboardController::class, 'cashCount'])->name('count');
        Route::post('/count/submit', [SellerDashboardController::class, 'submitCashCount'])->name('count.submit');
    });

    // Returns & Exchanges (Limited functionality)
    Route::prefix('returns')->name('returns.')->group(function () {
        Route::get('/', [SaleController::class, 'returns'])->name('index');
        Route::get('/create/{sale}', [SaleController::class, 'createReturn'])->name('create');
        Route::post('/process', [SaleController::class, 'processReturn'])->name('process');
    });

    // Notifications & Messages
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [SellerDashboardController::class, 'notifications'])->name('index');
        Route::post('/{notification}/mark-read', [SellerDashboardController::class, 'markNotificationRead'])->name('mark-read');
        Route::post('/mark-all-read', [SellerDashboardController::class, 'markAllNotificationsRead'])->name('mark-all-read');
    });
});
