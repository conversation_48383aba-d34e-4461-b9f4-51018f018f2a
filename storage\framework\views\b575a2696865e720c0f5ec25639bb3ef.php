<?php $__env->startSection('title', 'تقرير الأرصدة المعلقة'); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">تقرير الأرصدة المعلقة</h1>
                <p class="text-muted mb-0">عرض تفصيلي للمشتريات ذات الأرصدة المعلقة</p>
            </div>
            <div>
                <a href="<?php echo e(user_route('payment-reports.index')); ?>" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
                <a href="<?php echo e(user_route('payment-reports.export', ['type' => 'outstanding'])); ?>" class="btn btn-success">
                    <i class="fas fa-file-excel me-2"></i>تصدير
                </a>
            </div>
        </div>

        <!-- Filters and Summary -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">المورد</label>
                                <select name="supplier_id" class="form-select">
                                    <option value="">جميع الموردين</option>
                                    <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($supplier->id); ?>"
                                            <?php echo e($supplierId == $supplier->id ? 'selected' : ''); ?>>
                                            <?php echo e($supplier->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">ترتيب حسب</label>
                                <select name="sort_by" class="form-select">
                                    <option value="remaining_amount" <?php echo e($sortBy == 'remaining_amount' ? 'selected' : ''); ?>>
                                        المبلغ المتبقي</option>
                                    <option value="purchase_date" <?php echo e($sortBy == 'purchase_date' ? 'selected' : ''); ?>>تاريخ
                                        الشراء</option>
                                    <option value="total_amount" <?php echo e($sortBy == 'total_amount' ? 'selected' : ''); ?>>إجمالي
                                        المبلغ</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الترتيب</label>
                                <select name="sort_order" class="form-select">
                                    <option value="desc" <?php echo e($sortOrder == 'desc' ? 'selected' : ''); ?>>تنازلي</option>
                                    <option value="asc" <?php echo e($sortOrder == 'asc' ? 'selected' : ''); ?>>تصاعدي</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>تطبيق الفلاتر
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-left-danger shadow h-100">
                    <div class="card-body">
                        <div class="text-center">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                إجمالي الأرصدة المعلقة
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">
                                <?php echo e(format_currency($totalOutstanding)); ?>

                            </div>
                            <div class="text-muted mt-2">
                                <?php echo e($purchases->total()); ?> عملية شراء
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outstanding Balances Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">الأرصدة المعلقة</h6>
            </div>
            <div class="card-body">
                <?php if($purchases->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-bold">رقم العملية</th>
                                    <th class="border-0 fw-bold">المورد</th>
                                    <th class="border-0 fw-bold">الفرع</th>
                                    <th class="border-0 fw-bold">تاريخ الشراء</th>
                                    <th class="border-0 fw-bold">إجمالي المبلغ</th>
                                    <th class="border-0 fw-bold">المبلغ المدفوع</th>
                                    <th class="border-0 fw-bold">المبلغ المتبقي</th>
                                    <th class="border-0 fw-bold text-center">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $purchases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $purchase): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="border-bottom">
                                        <td>
                                            <span class="fw-bold text-primary">#<?php echo e($purchase->id); ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-primary text-white me-2">
                                                    <?php echo e(substr($purchase->supplier->name, 0, 1)); ?>

                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo e($purchase->supplier->name); ?></div>
                                                    <?php if($purchase->supplier->phone): ?>
                                                        <small class="text-muted"><?php echo e($purchase->supplier->phone); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($purchase->branch->name ?? 'غير محدد'); ?></span>
                                        </td>
                                        <td>
                                            <div class="fw-bold"><?php echo e($purchase->purchase_date->format('Y-m-d')); ?></div>
                                            <small
                                                class="text-muted"><?php echo e($purchase->purchase_date->diffForHumans()); ?></small>
                                        </td>
                                        <td>
                                            <span
                                                class="fw-bold text-info"><?php echo e(format_currency($purchase->total_amount)); ?></span>
                                        </td>
                                        <td>
                                            <span
                                                class="fw-bold text-success"><?php echo e(format_currency($purchase->paid_amount)); ?></span>
                                        </td>
                                        <td>
                                            <span
                                                class="fw-bold text-danger"><?php echo e(format_currency($purchase->actual_remaining_amount)); ?></span>
                                            <div class="progress mt-1" style="height: 4px;">
                                                <div class="progress-bar bg-success"
                                                    style="width: <?php echo e(($purchase->paid_amount / $purchase->total_amount) * 100); ?>%">
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(user_route('purchases.show', $purchase)); ?>"
                                                    class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(user_route('supplier-payments.create', $purchase)); ?>"
                                                    class="btn btn-outline-warning btn-sm" title="دفع مبلغ">
                                                    <i class="fas fa-money-bill-wave"></i>
                                                </a>
                                                <a href="<?php echo e(user_route('supplier-payments.show', $purchase)); ?>"
                                                    class="btn btn-outline-secondary btn-sm" title="تاريخ المدفوعات">
                                                    <i class="fas fa-history"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="6" class="text-end fw-bold text-danger">إجمالي المبالغ المتبقية:</th>
                                    <th class="fw-bold text-danger"><?php echo e(format_currency($totalOutstanding)); ?></th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($purchases->appends(request()->query())->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">لا توجد أرصدة معلقة</h5>
                        <p class="text-muted">جميع المشتريات مدفوعة بالكامل</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .border-left-danger {
            border-left: 0.25rem solid #e74a3b !important;
        }

        .btn-group .btn {
            margin-left: 2px;
        }

        .btn-group .btn:first-child {
            margin-left: 0;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my-pos-app\resources\views\payment-reports\outstanding-balances.blade.php ENDPATH**/ ?>