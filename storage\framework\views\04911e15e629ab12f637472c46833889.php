<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-undo text-warning me-2"></i>
                    مرتجعات الشراء
                </h1>
                <p class="mb-0 text-muted">إدارة ومتابعة مرتجعات المشتريات</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(user_route('purchase-returns.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إنشاء مرتجع جديد
                </a>
                <a href="<?php echo e(user_route('purchases.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المشتريات
                </a>
            </div>
        </div>

        <!-- Purchase Returns Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>قائمة المرتجعات
                </h6>
            </div>
            <div class="card-body">
                <!-- Filters -->
                <form method="GET" action="<?php echo e(user_route('purchase-returns.index')); ?>" class="mb-4">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>في
                                    الانتظار</option>
                                <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>
                                    معتمد</option>
                                <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>
                                    مكتمل</option>
                                <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>
                                    ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="supplier_id" class="form-label">المورد</label>
                            <select name="supplier_id" id="supplier_id" class="form-select">
                                <option value="">جميع الموردين</option>
                                <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($supplier->id); ?>"
                                        <?php echo e(request('supplier_id') == $supplier->id ? 'selected' : ''); ?>>
                                        <?php echo e($supplier->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" name="date_from" id="date_from" class="form-control"
                                value="<?php echo e(request('date_from')); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" name="date_to" id="date_to" class="form-control"
                                value="<?php echo e(request('date_to')); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" name="search" id="search" class="form-control"
                                placeholder="رقم المرتجع أو رقم الشراء" value="<?php echo e(request('search')); ?>">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="<?php echo e(route('admin.purchase-returns.index')); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> إلغاء الفلتر
                            </a>
                        </div>
                    </div>
                </form>

                <!-- Returns Table -->
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم المرتجع</th>
                                <th>رقم الشراء</th>
                                <th>المورد</th>
                                <th>تاريخ المرتجع</th>
                                <th>المبلغ الإجمالي</th>
                                <th>مبلغ الاسترداد</th>
                                <th>الحالة</th>
                                <th>نوع المرتجع</th>
                                <th>المستخدم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $returns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $return): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <a href="<?php echo e(route('admin.purchase-returns.show', $return)); ?>"
                                            class="text-decoration-none">
                                            <?php echo e($return->return_number); ?>

                                        </a>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('admin.purchases.show', $return->purchase)); ?>"
                                            class="text-decoration-none">
                                            #<?php echo e($return->purchase_id); ?>

                                        </a>
                                    </td>
                                    <td><?php echo e($return->supplier->name); ?></td>
                                    <td><?php echo e($return->return_date->format('Y-m-d')); ?></td>
                                    <td><?php echo e(number_format($return->total_amount, 2)); ?> ج.م</td>
                                    <td><?php echo e(number_format($return->refund_amount, 2)); ?> ج.م</td>
                                    <td>
                                        <?php switch($return->status):
                                            case ('pending'): ?>
                                                <span class="badge bg-warning">في الانتظار</span>
                                            <?php break; ?>

                                            <?php case ('approved'): ?>
                                                <span class="badge bg-info">معتمد</span>
                                            <?php break; ?>

                                            <?php case ('completed'): ?>
                                                <span class="badge bg-success">مكتمل</span>
                                            <?php break; ?>

                                            <?php case ('cancelled'): ?>
                                                <span class="badge bg-danger">ملغي</span>
                                            <?php break; ?>
                                        <?php endswitch; ?>
                                    </td>
                                    <td>
                                        <?php if($return->return_type == 'full'): ?>
                                            <span class="badge bg-primary">مرتجع كامل</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">مرتجع جزئي</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($return->user->name); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(user_route('purchase-returns.show', $return)); ?>"
                                                class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if($return->canBeEdited()): ?>
                                                <a href="<?php echo e(user_route('purchase-returns.edit', $return)); ?>"
                                                    class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($return->canBeApproved()): ?>
                                                <form method="POST"
                                                    action="<?php echo e(user_route('purchase-returns.approve', $return)); ?>"
                                                    class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-info"
                                                        onclick="return confirm('هل أنت متأكد من اعتماد هذا المرتجع؟')">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            <?php if($return->canBeCompleted()): ?>
                                                <form method="POST"
                                                    action="<?php echo e(user_route('purchase-returns.complete', $return)); ?>"
                                                    class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-success"
                                                        onclick="return confirm('هل أنت متأكد من إتمام هذا المرتجع؟ سيتم تعديل المخزون والحسابات.')">
                                                        <i class="fas fa-check-double"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="10" class="text-center">لا توجد مرتجعات</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        <?php echo e($returns->appends(request()->query())->links()); ?>

                    </div>
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views/admin/purchase-returns/index.blade.php ENDPATH**/ ?>