<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">تفاصيل المخزن: <?php echo e($store->name); ?></h2>
                    <div class="btn-group">
                        <a href="<?php echo e(user_route('stores.edit', $store)); ?>" class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a href="<?php echo e(user_route('stores.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Store Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle text-primary"></i> معلومات المتجر
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold text-muted">اسم المتجر:</td>
                                        <td><?php echo e($store->name); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">كود المتجر:</td>
                                        <td><span class="badge bg-secondary"><?php echo e($store->code); ?></span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">الفرع:</td>
                                        <td><span class="badge bg-info"><?php echo e($store->branch->name); ?></span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">المدير:</td>
                                        <td>
                                            <?php if($store->manager): ?>
                                                <i class="fas fa-user-tie text-primary"></i> <?php echo e($store->manager->name); ?>

                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">الحالة:</td>
                                        <td>
                                            <?php if($store->is_active): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold text-muted">العنوان:</td>
                                        <td><?php echo e($store->address ?: 'غير محدد'); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">الهاتف:</td>
                                        <td>
                                            <?php if($store->phone): ?>
                                                <a href="tel:<?php echo e($store->phone); ?>" class="text-decoration-none">
                                                    <i class="fas fa-phone text-success"></i> <?php echo e($store->phone); ?>

                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">البريد الإلكتروني:</td>
                                        <td>
                                            <?php if($store->email): ?>
                                                <a href="mailto:<?php echo e($store->email); ?>" class="text-decoration-none">
                                                    <i class="fas fa-envelope text-info"></i> <?php echo e($store->email); ?>

                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">تاريخ الإنشاء:</td>
                                        <td><?php echo e($store->created_at); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">آخر تحديث:</td>
                                        <td><?php echo e($store->updated_at); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <?php if($store->description): ?>
                            <div class="mt-3">
                                <h6 class="fw-bold text-muted">الوصف:</h6>
                                <p class="text-muted"><?php echo e($store->description); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Store Users -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-users text-primary"></i> موظفو المتجر
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if($store->users->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الاسم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الدور</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $store->users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($user->name); ?></td>
                                                <td><?php echo e($user->email); ?></td>
                                                <td><span class="badge bg-info"><?php echo e($user->role); ?></span></td>
                                                <td>
                                                    <?php if($user->is_active): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-3">
                                <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا يوجد موظفون مسجلون في هذا المتجر</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Store Inventory Overview -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-warehouse text-primary"></i> نظرة عامة على المخزون
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if($store->storeInventories->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية المتاحة</th>
                                            <th>الحد الأدنى</th>
                                            <th>الحد الأقصى</th>
                                            <th>حالة المخزون</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $store->storeInventories->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $inventory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr
                                                class="<?php echo e($inventory->isLowStock() ? 'table-warning' : ($inventory->quantity <= 0 ? 'table-danger' : '')); ?>">
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div>
                                                            <div class="fw-bold"><?php echo e($inventory->product->name); ?></div>
                                                            <?php if($inventory->product->sku): ?>
                                                                <small
                                                                    class="text-muted"><?php echo e($inventory->product->sku); ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span
                                                        class="fw-bold"><?php echo e(number_format($inventory->quantity, 2)); ?></span>
                                                </td>
                                                <td><?php echo e(number_format($inventory->minimum_stock, 2)); ?></td>
                                                <td><?php echo e($inventory->maximum_stock ? number_format($inventory->maximum_stock, 2) : 'غير محدد'); ?>

                                                </td>
                                                <td>
                                                    <span class="badge <?php echo e($inventory->getStockStatusBadgeClass()); ?>">
                                                        <?php echo e($inventory->getStockStatusText()); ?>

                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php if($store->storeInventories->count() > 5): ?>
                                <div class="text-center mt-3">
                                    <a href="<?php echo e(route('admin.stores.inventory', $store)); ?>"
                                        class="btn btn-outline-primary">
                                        <i class="fas fa-eye"></i> عرض جميع المنتجات
                                        (<?php echo e($store->storeInventories->count()); ?>)
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا يوجد منتجات في المخزون</h5>
                                <p class="text-muted">ابدأ بإضافة منتجات إلى هذا المخزن</p>
                                <a href="<?php echo e(route('admin.stores.add-product', $store)); ?>" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة منتجات
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Statistics Sidebar -->
            <div class="col-lg-4">
                <!-- Statistics Cards -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar text-primary"></i> إحصائيات المتجر
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-primary mb-1"><?php echo e($stats['total_products']); ?></h4>
                                    <small class="text-muted">إجمالي المنتجات</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-warning mb-1"><?php echo e($stats['low_stock_count']); ?></h4>
                                    <small class="text-muted">مخزون منخفض</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-danger mb-1"><?php echo e($stats['out_of_stock_count']); ?></h4>
                                    <small class="text-muted">نفد المخزون</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-info mb-1"><?php echo e($stats['total_users']); ?></h4>
                                    <small class="text-muted">الموظفون</small>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="border rounded p-3">
                                    <h4 class="text-success mb-1">
                                        <?php echo e(format_currency($stats['total_inventory_value'])); ?></h4>
                                    <small class="text-muted">قيمة المخزون الإجمالية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt text-primary"></i> إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(user_route('stores.edit', $store)); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-edit"></i> تعديل المخزن
                            </a>
                            <a href="<?php echo e(route('admin.stores.inventory', $store)); ?>" class="btn btn-outline-info">
                                <i class="fas fa-warehouse"></i> إدارة المخزون
                            </a>
                            <a href="<?php echo e(route('admin.stores.add-product', $store)); ?>"
                                class="btn btn-outline-success">
                                <i class="fas fa-plus"></i> إضافة منتجات
                            </a>
                            <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>?source_type=store&source_id=<?php echo e($store->id); ?>"
                                class="btn btn-outline-warning">
                                <i class="fas fa-exchange-alt"></i> نقل منتجات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\stores\show.blade.php ENDPATH**/ ?>