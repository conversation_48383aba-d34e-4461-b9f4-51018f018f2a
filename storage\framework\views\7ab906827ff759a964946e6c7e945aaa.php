<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-exclamation-triangle text-warning"></i> <?php echo e(__('المخزون المنخفض')); ?>

                </h2>
                <p class="text-muted small mb-0">المنتجات التي تحتاج إلى إعادة تموين</p>
            </div>
            <div>
                <a href="<?php echo e(route('admin.inventory.overview')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للنظرة العامة
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="container-fluid">
        <?php if($lowStockItems->count() > 0): ?>
            <!-- Alert -->
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>تنبيه!</strong> يوجد <?php echo e($lowStockItems->count()); ?> منتج بحاجة إلى إعادة تموين.
            </div>

            <!-- Low Stock Items Table -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-table"></i> المنتجات ذات المخزون المنخفض
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكود</th>
                                    <th>التصنيف</th>
                                    <th>نوع الموقع</th>
                                    <th>الموقع</th>
                                    <th>الكمية الحالية</th>
                                    <th>الحد الأدنى</th>
                                    <th>سعر التكلفة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $lowStockItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="fw-bold"><?php echo e($item->product_name); ?></td>
                                        <td><?php echo e($item->sku ?? '-'); ?></td>
                                        <td><?php echo e($item->category); ?></td>
                                        <td>
                                            <?php if($item->location_type == 'فرع'): ?>
                                                <span class="badge bg-primary"><?php echo e($item->location_type); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-success"><?php echo e($item->location_type); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($item->location_name); ?></td>
                                        <td class="text-center">
                                            <span class="badge bg-warning text-dark"><?php echo e(number_format($item->current_quantity, 2)); ?></span>
                                        </td>
                                        <td class="text-center"><?php echo e(number_format($item->threshold, 2)); ?></td>
                                        <td><?php echo e(number_format($item->cost_price, 2)); ?> ج.م</td>
                                        <td>
                                            <?php if($item->current_quantity <= 0): ?>
                                                <span class="badge bg-danger">نفد المخزون</span>
                                            <?php elseif($item->current_quantity <= $item->threshold): ?>
                                                <span class="badge bg-warning">مخزون منخفض</span>
                                            <?php else: ?>
                                                <span class="badge bg-success">متوفر</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if($item->location_type == 'فرع'): ?>
                                                    <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>?source_type=store&destination_type=branch&product_id=<?php echo e($item->product_name); ?>" 
                                                       class="btn btn-outline-primary" title="نقل من مخزن">
                                                        <i class="fas fa-exchange-alt"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>?source_type=branch&destination_type=store&product_id=<?php echo e($item->product_name); ?>" 
                                                       class="btn btn-outline-success" title="نقل من فرع">
                                                        <i class="fas fa-exchange-alt"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <a href="<?php echo e(route('admin.purchases.create')); ?>?product=<?php echo e($item->product_name); ?>" 
                                                   class="btn btn-outline-info" title="شراء جديد">
                                                    <i class="fas fa-shopping-cart"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card border-left-info shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-info">
                                <i class="fas fa-bolt"></i> إجراءات سريعة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="<?php echo e(route('admin.purchases.create')); ?>" class="btn btn-info">
                                    <i class="fas fa-shopping-cart"></i> إنشاء طلب شراء جديد
                                </a>
                                <a href="<?php echo e(route('admin.inventory-transfers.create')); ?>" class="btn btn-primary">
                                    <i class="fas fa-exchange-alt"></i> إنشاء عملية نقل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-left-warning shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-warning">
                                <i class="fas fa-chart-pie"></i> إحصائيات سريعة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="h4 text-warning"><?php echo e($lowStockItems->where('location_type', 'فرع')->count()); ?></div>
                                        <div class="small text-muted">فروع</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="h4 text-success"><?php echo e($lowStockItems->where('location_type', 'مخزن')->count()); ?></div>
                                        <div class="small text-muted">مخازن</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- No Low Stock Items -->
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-success">ممتاز! لا توجد منتجات بمخزون منخفض</h5>
                    <p class="text-muted">جميع المنتجات متوفرة بكميات كافية</p>
                    <a href="<?php echo e(route('admin.inventory.overview')); ?>" class="btn btn-primary">
                        <i class="fas fa-eye"></i> عرض النظرة العامة
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\admin\inventory\low-stock.blade.php ENDPATH**/ ?>