<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-edit text-warning"></i> تحديث أسعار مجمع - <?php echo e($branch->name); ?>

            </h2>
            <a href="<?php echo e(route('admin.price-management.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="container-fluid">
            <form method="POST" action="<?php echo e(route('admin.price-management.bulk-update')); ?>" id="bulkUpdateForm">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="branch_id" value="<?php echo e($branch->id); ?>">

                <!-- Summary Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i> ملخص التحديث
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary" id="totalProducts"><?php echo e($products->count()); ?></h4>
                                    <small class="text-muted">إجمالي المنتجات</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning" id="selectedProducts">0</h4>
                                    <small class="text-muted">منتجات محددة</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success" id="validPrices">0</h4>
                                    <small class="text-muted">أسعار صحيحة</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-danger" id="invalidPrices">0</h4>
                                    <small class="text-muted">أسعار غير صحيحة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tools"></i> إجراءات مجمعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-primary w-100" onclick="selectAll()">
                                    <i class="fas fa-check-square"></i> تحديد الكل
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-secondary w-100" onclick="clearAll()">
                                    <i class="fas fa-square"></i> إلغاء التحديد
                                </button>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input type="number" step="0.01" id="bulkPriceIncrease" class="form-control" placeholder="زيادة %">
                                    <button type="button" class="btn btn-outline-success" onclick="applyBulkIncrease()">
                                        <i class="fas fa-arrow-up"></i> تطبيق
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input type="number" step="0.01" id="bulkPriceDecrease" class="form-control" placeholder="تخفيض %">
                                    <button type="button" class="btn btn-outline-danger" onclick="applyBulkDecrease()">
                                        <i class="fas fa-arrow-down"></i> تطبيق
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list"></i> المنتجات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAllCheckbox" onchange="toggleAll()">
                                        </th>
                                        <th>المنتج</th>
                                        <th>السعر الحالي</th>
                                        <th>مستوى السعر</th>
                                        <th>السعر الجديد</th>
                                        <th>هامش الربح</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr data-product-id="<?php echo e($product->id); ?>">
                                            <td>
                                                <input type="checkbox" class="product-checkbox" 
                                                    name="selected_products[]" value="<?php echo e($product->id); ?>"
                                                    onchange="updateSelection()">
                                            </td>
                                            <td>
                                                <strong><?php echo e($product->name); ?></strong><br>
                                                <small class="text-muted"><?php echo e($product->sku); ?></small>
                                            </td>
                                            <td>
                                                <span class="fw-bold"><?php echo e(number_format($product->resolved_price_info['price'], 2)); ?> ج.م</span><br>
                                                <small class="text-muted">
                                                    <?php
                                                        $sourceLabels = [
                                                            'branch_inventory' => 'سعر الفرع',
                                                            'product_selling_price' => 'سعر المنتج',
                                                            'product_price' => 'السعر الأساسي',
                                                            'cost_plus_calculated' => 'محسوب تلقائياً'
                                                        ];
                                                    ?>
                                                    <?php echo e($sourceLabels[$product->resolved_price_info['source']] ?? $product->resolved_price_info['source']); ?>

                                                </small>
                                            </td>
                                            <td>
                                                <select name="updates[<?php echo e($index); ?>][price_level]" class="form-select form-select-sm price-level-select">
                                                    <option value="1">السعر الأول</option>
                                                    <option value="2">السعر الثاني</option>
                                                    <option value="3">السعر الثالث</option>
                                                </select>
                                            </td>
                                            <td>
                                                <input type="hidden" name="updates[<?php echo e($index); ?>][product_id]" value="<?php echo e($product->id); ?>">
                                                <div class="input-group input-group-sm">
                                                    <input type="number" step="0.01" 
                                                        name="updates[<?php echo e($index); ?>][price]" 
                                                        class="form-control price-input"
                                                        value="<?php echo e($product->resolved_price_info['price']); ?>"
                                                        data-original-price="<?php echo e($product->resolved_price_info['price']); ?>"
                                                        data-cost-price="<?php echo e($product->resolved_price_info['cost_price']); ?>"
                                                        onchange="validatePrice(this)">
                                                    <span class="input-group-text">ج.م</span>
                                                </div>
                                                <div class="price-suggestions mt-1" style="display: none;">
                                                    <?php if(isset($product->price_suggestions)): ?>
                                                        <?php $__currentLoopData = $product->price_suggestions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $suggestion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <button type="button" class="btn btn-outline-secondary btn-xs me-1 mb-1"
                                                                onclick="applySuggestion(this, <?php echo e($suggestion['price']); ?>)">
                                                                <?php echo e($suggestion['label']); ?>: <?php echo e(number_format($suggestion['price'], 2)); ?>

                                                            </button>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td class="profit-margin">
                                                <span class="badge bg-secondary">حساب...</span>
                                            </td>
                                            <td class="price-status">
                                                <span class="badge bg-secondary">غير محدد</span>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-info" onclick="showSuggestions()">
                                <i class="fas fa-lightbulb"></i> إظهار الاقتراحات
                            </button>
                            <div>
                                <button type="button" class="btn btn-secondary me-2" onclick="resetForm()">
                                    <i class="fas fa-undo"></i> إعادة تعيين
                                </button>
                                <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                    <i class="fas fa-save"></i> حفظ التحديثات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            let selectedCount = 0;
            let validCount = 0;
            let invalidCount = 0;

            function updateSelection() {
                const checkboxes = document.querySelectorAll('.product-checkbox:checked');
                selectedCount = checkboxes.length;
                
                document.getElementById('selectedProducts').textContent = selectedCount;
                document.getElementById('submitBtn').disabled = selectedCount === 0 || invalidCount > 0;
                
                // Update select all checkbox
                const selectAllCheckbox = document.getElementById('selectAllCheckbox');
                const totalCheckboxes = document.querySelectorAll('.product-checkbox');
                selectAllCheckbox.checked = selectedCount === totalCheckboxes.length;
                selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalCheckboxes.length;
            }

            function toggleAll() {
                const selectAll = document.getElementById('selectAllCheckbox').checked;
                document.querySelectorAll('.product-checkbox').forEach(checkbox => {
                    checkbox.checked = selectAll;
                });
                updateSelection();
            }

            function selectAll() {
                document.getElementById('selectAllCheckbox').checked = true;
                toggleAll();
            }

            function clearAll() {
                document.getElementById('selectAllCheckbox').checked = false;
                toggleAll();
            }

            function validatePrice(input) {
                const price = parseFloat(input.value) || 0;
                const costPrice = parseFloat(input.dataset.costPrice) || 0;
                const row = input.closest('tr');
                const profitMarginCell = row.querySelector('.profit-margin');
                const statusCell = row.querySelector('.price-status');
                
                let profitMargin = 0;
                let status = 'valid';
                let statusClass = 'bg-success';
                let statusText = 'صحيح';
                
                if (costPrice > 0) {
                    profitMargin = ((price - costPrice) / costPrice) * 100;
                    
                    if (price < costPrice) {
                        status = 'below-cost';
                        statusClass = 'bg-danger';
                        statusText = 'أقل من التكلفة';
                    } else if (profitMargin < 5) {
                        status = 'low-margin';
                        statusClass = 'bg-warning';
                        statusText = 'هامش منخفض';
                    }
                }
                
                // Update profit margin display
                profitMarginCell.innerHTML = `<span class="badge ${profitMargin > 20 ? 'bg-success' : (profitMargin > 5 ? 'bg-warning' : 'bg-danger')}">${profitMargin.toFixed(1)}%</span>`;
                
                // Update status display
                statusCell.innerHTML = `<span class="badge ${statusClass}">${statusText}</span>`;
                
                // Update validation classes
                input.classList.remove('is-valid', 'is-invalid');
                if (status === 'valid') {
                    input.classList.add('is-valid');
                } else if (status === 'below-cost') {
                    input.classList.add('is-invalid');
                }
                
                updateValidationCounts();
            }

            function updateValidationCounts() {
                validCount = document.querySelectorAll('.price-input.is-valid').length;
                invalidCount = document.querySelectorAll('.price-input.is-invalid').length;
                
                document.getElementById('validPrices').textContent = validCount;
                document.getElementById('invalidPrices').textContent = invalidCount;
                
                updateSelection();
            }

            function applyBulkIncrease() {
                const percentage = parseFloat(document.getElementById('bulkPriceIncrease').value) || 0;
                if (percentage <= 0) return;
                
                document.querySelectorAll('.product-checkbox:checked').forEach(checkbox => {
                    const row = checkbox.closest('tr');
                    const priceInput = row.querySelector('.price-input');
                    const originalPrice = parseFloat(priceInput.dataset.originalPrice) || 0;
                    const newPrice = originalPrice * (1 + percentage / 100);
                    
                    priceInput.value = newPrice.toFixed(2);
                    validatePrice(priceInput);
                });
                
                document.getElementById('bulkPriceIncrease').value = '';
            }

            function applyBulkDecrease() {
                const percentage = parseFloat(document.getElementById('bulkPriceDecrease').value) || 0;
                if (percentage <= 0) return;
                
                document.querySelectorAll('.product-checkbox:checked').forEach(checkbox => {
                    const row = checkbox.closest('tr');
                    const priceInput = row.querySelector('.price-input');
                    const originalPrice = parseFloat(priceInput.dataset.originalPrice) || 0;
                    const newPrice = originalPrice * (1 - percentage / 100);
                    
                    priceInput.value = Math.max(0, newPrice).toFixed(2);
                    validatePrice(priceInput);
                });
                
                document.getElementById('bulkPriceDecrease').value = '';
            }

            function applySuggestion(button, price) {
                const row = button.closest('tr');
                const priceInput = row.querySelector('.price-input');
                priceInput.value = price.toFixed(2);
                validatePrice(priceInput);
            }

            function showSuggestions() {
                document.querySelectorAll('.price-suggestions').forEach(div => {
                    div.style.display = div.style.display === 'none' ? 'block' : 'none';
                });
            }

            function resetForm() {
                document.querySelectorAll('.price-input').forEach(input => {
                    input.value = input.dataset.originalPrice;
                    validatePrice(input);
                });
                clearAll();
            }

            // Initialize validation on page load
            document.addEventListener('DOMContentLoaded', function() {
                document.querySelectorAll('.price-input').forEach(validatePrice);
            });

            // Form submission
            document.getElementById('bulkUpdateForm').addEventListener('submit', function(e) {
                // Only include selected products in submission
                const selectedProducts = [];
                document.querySelectorAll('.product-checkbox:checked').forEach(checkbox => {
                    const row = checkbox.closest('tr');
                    const productId = checkbox.value;
                    const price = row.querySelector('.price-input').value;
                    const priceLevel = row.querySelector('.price-level-select').value;
                    
                    selectedProducts.push({
                        product_id: productId,
                        price: parseFloat(price),
                        price_level: parseInt(priceLevel)
                    });
                });
                
                // Create hidden input with selected updates
                const updatesInput = document.createElement('input');
                updatesInput.type = 'hidden';
                updatesInput.name = 'updates';
                updatesInput.value = JSON.stringify(selectedProducts);
                this.appendChild(updatesInput);
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\my-pos-app\resources\views\admin\price-management\bulk-update.blade.php ENDPATH**/ ?>